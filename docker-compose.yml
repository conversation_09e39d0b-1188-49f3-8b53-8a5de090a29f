version: '3.8'

services:
  pdf-ocr-dashboard:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pdf-ocr-dashboard
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      # Add your environment variables here
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://localhost:5000/api}
      - NEXT_PUBLIC_IDENTITY_SERVER_URL=${NEXT_PUBLIC_IDENTITY_SERVER_URL:-http://localhost:5001}
      - NEXT_PUBLIC_CLIENT_ID=${NEXT_PUBLIC_CLIENT_ID:-pdf-ocr-dashboard}
    volumes:
      # Optional: Mount a volume for persistent data if needed
      - pdf_data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - pdf-network

  # Optional: Add a reverse proxy (nginx) for production
  nginx:
    image: nginx:alpine
    container_name: pdf-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      # Add SSL certificates if needed
      # - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - pdf-ocr-dashboard
    restart: unless-stopped
    networks:
      - pdf-network
    profiles:
      - production

volumes:
  pdf_data:
    driver: local

networks:
  pdf-network:
    driver: bridge
