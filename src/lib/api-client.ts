import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { <PERSON>r<PERSON><PERSON><PERSON>, UserManagerSettings } from "oidc-client-ts";
import { API_CONFIG } from "./constants";
import { oidcConfig } from "./oidcConfig";

// Circuit breaker states
enum CircuitState {
  CLOSED = "CLOSED",     // Normal operation
  OPEN = "OPEN",         // Circuit is open, requests are blocked
  HALF_OPEN = "HALF_OPEN" // Testing if service is back up
}

interface CircuitBreakerConfig {
  failureThreshold: number;    // Number of failures before opening circuit
  recoveryTimeout: number;     // Time to wait before trying again (ms)
  monitoringPeriod: number;    // Time window for counting failures (ms)
}

class ApiClient {
  private client: AxiosInstance;
  private userManager: UserManager | null = null;
  private refreshPromise: Promise<string | null> | null = null;

  // Circuit breaker properties
  private circuitState: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private isDisabled: boolean = false;
  
  private circuitConfig: CircuitBreakerConfig = {
    failureThreshold: 5,      // Open circuit after 5 failures
    recoveryTimeout: 30000,   // Wait 30 seconds before retry
    monitoringPeriod: 60000   // Reset failure count every 60 seconds
  };

  constructor() {
    this.client = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        "Content-Type": "application/json",
      },
      // Disable axios retry behavior
      // retryDelay: () => 0,
    });

    // Initialize UserManager if in browser
    if (typeof window !== "undefined") {
      this.userManager = new UserManager(oidcConfig as UserManagerSettings);
    }

    // Request interceptor to add auth token and check circuit breaker
    this.client.interceptors.request.use(
      async (config) => {
        // Check if API is disabled
        if (this.isDisabled) {
          throw new Error("API client is disabled due to repeated failures");
        }

        // Check circuit breaker state
        if (!this.canMakeRequest()) {
          throw new Error("Circuit breaker is open - service temporarily unavailable");
        }

        // API calls are now enabled

        const token = await this.getAccessToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        // Reset failure count on successful response
        this.onSuccess();
        return response;
      },
      async (error) => {
        // Record the failure
        this.onFailure();

        // Handle 401 errors by redirecting to login immediately
        if (error.response?.status === 401) {
          if (typeof window !== "undefined") {
            window.location.href = "/auth/login";
          }
        }

        // Handle timeout errors
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          console.error("Request timeout - circuit breaker activated");
        }

        // Handle network errors
        if (!error.response) {
          console.error("Network error - circuit breaker activated");
        }

        return Promise.reject(error);
      }
    );
  }

  // Circuit breaker logic
  private canMakeRequest(): boolean {
    const now = Date.now();
    
    switch (this.circuitState) {
      case CircuitState.CLOSED:
        return true;
        
      case CircuitState.OPEN:
        if (now - this.lastFailureTime >= this.circuitConfig.recoveryTimeout) {
          this.circuitState = CircuitState.HALF_OPEN;
          console.log("Circuit breaker moving to HALF_OPEN state");
          return true;
        }
        return false;
        
      case CircuitState.HALF_OPEN:
        return true;
        
      default:
        return false;
    }
  }

  private onSuccess(): void {
    if (this.circuitState === CircuitState.HALF_OPEN) {
      this.circuitState = CircuitState.CLOSED;
      this.failureCount = 0;
      console.log("Circuit breaker reset to CLOSED state");
    }
    
    // Reset failure count if monitoring period has passed
    const now = Date.now();
    if (now - this.lastFailureTime > this.circuitConfig.monitoringPeriod) {
      this.failureCount = 0;
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.circuitState === CircuitState.HALF_OPEN) {
      this.circuitState = CircuitState.OPEN;
      console.log("Circuit breaker opened from HALF_OPEN state");
    } else if (this.failureCount >= this.circuitConfig.failureThreshold) {
      this.circuitState = CircuitState.OPEN;
      console.log(`Circuit breaker opened after ${this.failureCount} failures`);
    }
  }



  // Public methods to control the API client
  public disableApiClient(): void {
    this.isDisabled = true;
    console.log("API client has been disabled");
  }

  public enableApiClient(): void {
    this.isDisabled = false;
    this.circuitState = CircuitState.CLOSED;
    this.failureCount = 0;
    console.log("API client has been enabled");
  }

  public getCircuitStatus(): { state: CircuitState; failureCount: number; isDisabled: boolean } {
    return {
      state: this.circuitState,
      failureCount: this.failureCount,
      isDisabled: this.isDisabled
    };
  }

  // Get access token from UserManager
  private async getAccessToken(): Promise<string | null> {
    if (!this.userManager) return null;

    try {
      const user = await this.userManager.getUser();
      return user?.access_token || null;
    } catch (error) {
      console.error("Error getting access token:", error);
      return null;
    }
  }

  // Refresh token using silent renew (removed automatic retry)
  private async refreshToken(): Promise<string | null> {
    if (!this.userManager) return null;

    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = (async () => {
      try {
        const user = await this.userManager!.signinSilent();
        return user?.access_token || null;
      } catch (error) {
        console.error("Silent renew failed:", error);
        return null;
      } finally {
        this.refreshPromise = null;
      }
    })();

    return this.refreshPromise;
  }

  // Wrapper method for making requests with error handling
  private async makeRequest<T>(requestFn: () => Promise<AxiosResponse<T>>): Promise<T> {
    try {
      const response = await requestFn();
      return response.data;
    } catch (error: any) {
      // Log the error but don't retry
      console.error("API request failed:", {
        message: error.message,
        status: error.response?.status,
        url: error.config?.url,
        circuitState: this.circuitState
      });
      
      throw error;
    }
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.makeRequest(() => this.client.get(url, config));
  }

  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.makeRequest(() => this.client.post(url, data, config));
  }

  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return this.makeRequest(() => this.client.put(url, data, config));
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.makeRequest(() => this.client.delete(url, config));
  }

  async uploadFile<T = any>(
    url: string,
    file: File,
    onProgress?: (progress: number) => void,
    additionalData?: Record<string, any>
  ): Promise<T> {
    const formData = new FormData();
    formData.append("file", file);

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    return this.makeRequest(() => 
      this.client.post(url, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            onProgress(progress);
          }
        },
      })
    );
  }

  async downloadFile(url: string, filename?: string): Promise<void> {
    try {
      const response = await this.client.get(url, {
        responseType: "blob",
      });

      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename || "download";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("File download failed:", error);
      throw error;
    }
  }

  // Get the underlying axios instance for advanced usage
  getClient(): AxiosInstance {
    return this.client;
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();
export default apiClient;