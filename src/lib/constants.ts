/**
 * Application Constants
 * Centralized configuration for the PDF OCR Dashboard
 */

// Environment Configuration
export const ENV = {
  NODE_ENV: process.env.NODE_ENV || "development",
  IS_DEVELOPMENT: process.env.NODE_ENV === "development",
  IS_PRODUCTION: process.env.NODE_ENV === "production",
  IS_TEST: process.env.NODE_ENV === "test",
} as const;

// Type definitions for better type safety
export type Environment = typeof ENV.NODE_ENV;
export type JobStatus = "pending" | "processing" | "completed" | "failed";
export type FileType = "application/pdf";

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || "http://*************:7040",
  TIMEOUT: 30000, // 30 seconds
} as const;

// OIDC Configuration
export const OIDC_CONFIG_CONSTANTS = {
  AUTHORITY: process.env.NEXT_PUBLIC_OIDC_AUTHORITY || "http://localhost:3000",
  CLIENT_ID: process.env.NEXT_PUBLIC_OIDC_CLIENT_ID || "lugo-study",
  REDIRECT_URI:
    process.env.NEXT_PUBLIC_OIDC_REDIRECT_URI ||
    "http://localhost:3000/auth/callback",
  POST_LOGOUT_REDIRECT_URI:
    process.env.NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI ||
    "http://localhost:3000/logout-callback",
  SCOPE: process.env.NEXT_PUBLIC_OIDC_SCOPE || "openid profile email roles",
  SILENT_REDIRECT_URI:
    process.env.NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI ||
    "http://localhost:3000/silent-callback",
} as const;

// PDF Configuration
export const PDF_SCALE_MIN = 0.5;
export const PDF_SCALE_MAX = 3.0;
export const PDF_SCALE_STEP = 0.25;
export const PDF_DEFAULT_SCALE = 1.0;

// File Upload Configuration
export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
export const ACCEPTED_FILE_TYPES: Record<string, string[]> = {
  "application/pdf": [".pdf"],
} as const;

// Pagination
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;

// Colors for split ranges
export const SPLIT_RANGE_COLORS = [
  "#ef4444", // red
  "#f97316", // orange
  "#eab308", // yellow
  "#22c55e", // green
  "#3b82f6", // blue
  "#8b5cf6", // violet
  "#ec4899", // pink
  "#06b6d4", // cyan
];

// Job status colors
export const JOB_STATUS_COLORS: Record<JobStatus, string> = {
  pending: "#6b7280",
  processing: "#3b82f6",
  completed: "#22c55e",
  failed: "#ef4444",
} as const;

// Cookie names
export const ACCESS_TOKEN_COOKIE = "access_token";
export const REFRESH_TOKEN_COOKIE = "refresh_token";

// Local storage keys
export const STORAGE_KEYS = {
  VIEWER_PREFERENCES: "pdf_viewer_preferences",
  RECENT_FILES: "recent_files",
  SPLIT_RANGES: "split_ranges",
};

// API Endpoints
export const API_ENDPOINTS = {
  // Files
  FILES: "/files",
  FILE_UPLOAD: "/files/upload",
  FILE_DELETE: (id: string) => `/files/${id}`,
  FILE_DOWNLOAD: (id: string) => `/files/${id}/download`,

  // Folders
  FOLDERS: "/folders",
  FOLDER_CREATE: "/folders",
  FOLDER_DELETE: (id: string) => `/folders/${id}`,

  // PDF Operations
  PDF_SPLIT: "/pdf/split",
  PDF_OCR: "/pdf/ocr",
  PDF_MERGE: "/pdf/merge",

  // Jobs
  JOBS: "/jobs",
  JOB_STATUS: (id: string) => `/jobs/${id}`,
  JOB_CANCEL: (id: string) => `/jobs/${id}/cancel`,


};

// Error messages
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: "File size exceeds the maximum limit",
  INVALID_FILE_TYPE: "Invalid file type. Only PDF files are allowed",
  UPLOAD_FAILED: "Failed to upload file",
  NETWORK_ERROR: "Network error. Please check your connection",
  UNAUTHORIZED: "You are not authorized to perform this action",
  SERVER_ERROR: "Server error. Please try again later",
  FILE_NOT_FOUND: "File not found",
  INVALID_PAGE_RANGE: "Invalid page range",
  VALIDATION_FAILED: "Validation failed",
  UNKNOWN_ERROR: "An unknown error occurred",
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: "File uploaded successfully",
  FILE_DELETED: "File deleted successfully",
  FOLDER_CREATED: "Folder created successfully",
  FOLDER_DELETED: "Folder deleted successfully",
  PDF_SPLIT: "PDF split successfully",
  OCR_COMPLETED: "OCR completed successfully",
  OPERATION_COMPLETED: "Operation completed successfully",
} as const;
