import { UserProfile } from '@/types/user';

const USER_STORAGE_KEY = 'app_user_profile';
const USER_CONTEXT_KEY = 'app_user_context';

export interface UserContext {
  userId: string;
  email: string;
  name: string;
  role: string;
  preferredUsername: string;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: number;
  lastLogin: string;
}

/**
 * User storage utility for managing user data in localStorage
 */
export class UserStorage {
  /**
   * Save user profile to localStorage
   */
  static saveUserProfile(profile: UserProfile): void {
    try {
      localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(profile));
      console.log('✅ User profile saved to localStorage');
    } catch (error) {
      console.error('❌ Failed to save user profile:', error);
    }
  }

  /**
   * Get user profile from localStorage
   */
  static getUserProfile(): UserProfile | null {
    try {
      const stored = localStorage.getItem(USER_STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('❌ Failed to parse user profile from localStorage:', error);
    }
    return null;
  }

  /**
   * Save user context for API usage
   */
  static saveUserContext(user: any): void {
    try {
      const context: UserContext = {
        userId: user.profile?.sub || '',
        email: user.profile?.email || '',
        name: user.profile?.name || '',
        role: user.profile?.role || 'User',
        preferredUsername: user.profile?.preferred_username || '',
        accessToken: user.access_token,
        refreshToken: user.refresh_token,
        expiresAt: user.expires_at,
        lastLogin: new Date().toISOString()
      };

      localStorage.setItem(USER_CONTEXT_KEY, JSON.stringify(context));
      console.log('✅ User context saved to localStorage:', {
        userId: context.userId,
        email: context.email,
        role: context.role
      });
    } catch (error) {
      console.error('❌ Failed to save user context:', error);
    }
  }

  /**
   * Get user context from localStorage
   */
  static getUserContext(): UserContext | null {
    try {
      const stored = localStorage.getItem(USER_CONTEXT_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('❌ Failed to parse user context from localStorage:', error);
    }
    return null;
  }

  /**
   * Get current user's ID (for use as ownerId)
   */
  static getCurrentUserId(): string | null {
    const context = this.getUserContext();
    return context?.userId || null;
  }

  /**
   * Get current user's access token
   */
  static getCurrentAccessToken(): string | null {
    const context = this.getUserContext();
    return context?.accessToken || null;
  }

  /**
   * Check if user context is valid (not expired)
   */
  static isUserContextValid(): boolean {
    const context = this.getUserContext();
    if (!context || !context.expiresAt) return false;
    
    // Check if token expires within the next 5 minutes
    const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
    return context.expiresAt * 1000 > fiveMinutesFromNow;
  }

  /**
   * Clear all user data from localStorage
   */
  static clearUserData(): void {
    try {
      localStorage.removeItem(USER_STORAGE_KEY);
      localStorage.removeItem(USER_CONTEXT_KEY);
      console.log('✅ User data cleared from localStorage');
    } catch (error) {
      console.error('❌ Failed to clear user data:', error);
    }
  }

  /**
   * Update user profile fields
   */
  static updateUserProfile(updates: Partial<UserProfile>): void {
    const current = this.getUserProfile();
    if (current) {
      const updated = { ...current, ...updates };
      this.saveUserProfile(updated);
    }
  }

  /**
   * Get user display info for UI
   */
  static getUserDisplayInfo(): { name: string; email: string; role: string } | null {
    const context = this.getUserContext();
    if (!context) return null;

    return {
      name: context.name || context.preferredUsername || 'Unknown User',
      email: context.email || '',
      role: context.role || 'User'
    };
  }

  /**
   * Create user profile from SSO response
   */
  static createProfileFromSSO(ssoResponse: any): UserProfile {
    return {
      sub: ssoResponse.sub,
      email: ssoResponse.email,
      name: ssoResponse.name,
      role: ssoResponse.role,
      preferred_username: ssoResponse.preferred_username,
      email_verified: ssoResponse.email_verified,
      roles: ssoResponse.role ? [ssoResponse.role] : [],
      isActive: true,
      lastLogin: new Date().toISOString()
    };
  }
} 