import { User, UserManager, UserManagerSettings } from 'oidc-client-ts';
import { oidcConfig } from './oidcConfig';
import { UserProfile } from '@/types/user';

/**
 * Clear all authentication state from storage
 * Useful for debugging and error recovery
 */
export const clearAuthState = (): void => {
  if (typeof window === 'undefined') return;

  try {
    // List of keys to remove from storage
    const keysToRemove = [
      'oidc.user',
      'oidc.state',
      'auth_return_url',
      'login_attempted',
      // Add other OIDC-related keys as needed
    ];

    // Clear from both localStorage and sessionStorage
    keysToRemove.forEach(key => {
      try {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      } catch (e) {
        console.warn(`Could not remove ${key} from storage:`, e);
      }
    });

    console.log('🧹 Cleared authentication state');
  } catch (error) {
    console.error('Failed to clear auth state:', error);
  }
};

/**
 * Debug OIDC configuration
 * Logs configuration without sensitive data
 */
export const logOidcConfig = (): void => {
  if (process.env.NODE_ENV !== 'development') return;

  const configToLog = {
    ...oidcConfig,
    client_secret: '[HIDDEN]',
  };

  console.log('🔧 OIDC Configuration:', configToLog);
};

/**
 * Debug current authentication state
 */
export const debugAuthState = (): void => {
  if (process.env.NODE_ENV !== 'development') return;
  if (typeof window === 'undefined') return;

  try {
    const userState = localStorage.getItem('oidc.user');
    const hasUser = !!userState;
    
    console.log('🔍 Auth Debug State:', {
      hasStoredUser: hasUser,
      userAgent: navigator.userAgent,
      isHttps: window.location.protocol === 'https:',
      origin: window.location.origin,
      timestamp: new Date().toISOString(),
    });

    if (hasUser) {
      try {
        const user = JSON.parse(userState);
        console.log('👤 Stored User Info:', {
          hasAccessToken: !!user.access_token,
          hasRefreshToken: !!user.refresh_token,
          expiresAt: user.expires_at ? new Date(user.expires_at * 1000).toISOString() : null,
          isExpired: user.expires_at ? user.expires_at * 1000 < Date.now() : null,
          scopes: user.scope,
        });
      } catch (e) {
        console.error('Failed to parse stored user:', e);
      }
    }
  } catch (error) {
    console.error('Failed to debug auth state:', error);
  }
};

/**
 * Check if token is expired or expiring soon
 */
export const isTokenExpired = (user: User | null): boolean => {
  if (!user || !user.expires_at) return true;
  
  // Consider token expired if it expires within the next 5 minutes
  const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
  return user.expires_at * 1000 < fiveMinutesFromNow;
};

/**
 * Get user roles from user profile with enhanced claim detection
 */
export const getUserRoles = (user: User | null): string[] => {
  if (!user?.profile) return [];
  
  // ⭐ Try different possible role claim names (Identity Server can use various formats)
  const roleClaims = [
    user.profile.roles,      // Array format: ["Admin", "User"]
    user.profile.role,       // Single role string: "Admin"
    user.profile['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'], // Microsoft format
    user.profile['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/role'], // Alternative Microsoft format
    user.profile['roles'],   // Alternative array format
    user.profile['Role'],    // Capitalized version
    user.profile['ROLE'],    // Upper case version
  ];


  return [];
};

/**
 * Check if user has required role
 */
export const hasRole = (user: User | null, requiredRole: string): boolean => {
  const userRoles = getUserRoles(user);
  return userRoles.includes(requiredRole);
};

/**
 * Check if user has any of the required roles
 */
export const hasAnyRole = (user: User | null, requiredRoles: string[]): boolean => {
  if (requiredRoles.length === 0) return true;
  const userRoles = getUserRoles(user);
  return requiredRoles.some(role => userRoles.includes(role));
};

/**
 * Create a UserManager instance for manual operations
 */
export const createUserManager = (): UserManager => {
  return new UserManager(oidcConfig as UserManagerSettings);
};

/**
 * Force logout and clear all state
 * Useful for error scenarios
 */
export const forceLogout = (): void => {
  // Clear auth state
  clearAuthState();
  
  // Trigger custom logout event
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new Event('userSignout'));
  }
  
  // Redirect to login
  if (typeof window !== 'undefined') {
    window.location.href = '/auth/login';
  }
};

/**
 * Handle authentication errors with appropriate actions
 */
export const handleAuthError = (error: Error): void => {
  console.error('🚨 Authentication Error:', error);

  if (error.message?.includes('No state in response') ||
      error.message?.includes('No matching state found')) {
    console.error('🚨 State error detected - clearing auth state');
    clearAuthState();
  }

  if (error.message?.includes('token') && error.message?.includes('expired')) {
    console.error('🚨 Token expired - forcing logout');
    forceLogout();
  }

  // Add more specific error handling as needed
};

/**
 * Validate OIDC configuration
 */
export const validateOidcConfig = (): boolean => {
  const required = [
    'authority',
    'client_id',
    'redirect_uri',
    'post_logout_redirect_uri',
  ];

  for (const key of required) {
    if (!oidcConfig[key as keyof typeof oidcConfig]) {
      console.error(`🚨 Missing required OIDC config: ${key}`);
      return false;
    }
  }

  return true;
};

/**
 * Maps SSO userinfo response to UserProfile interface
 * @param userinfo Raw userinfo response from SSO provider
 * @returns Mapped UserProfile object
 */
export function mapSSOUserToProfile(userinfo: any): UserProfile {
  return {
    sub: userinfo.sub,
    email: userinfo.email,
    name: userinfo.name,
    role: userinfo.role,
    preferred_username: userinfo.preferred_username,
    email_verified: userinfo.email_verified,
    // Parse role into roles array for better role management
    roles: userinfo.role ? [userinfo.role] : [],
    isActive: true,
    lastLogin: new Date().toISOString()
  };
}

/**
 * Enhanced role checking with SSO role mapping
 * @param user OIDC user object
 * @param requiredRole Role to check
 * @returns Boolean indicating if user has the role
 */
export function hasSSORoleAccess(user: any, requiredRole: string): boolean {
  if (!user?.profile?.role) return false;
  
  // Direct role match
  if (user.profile.role === requiredRole) return true;
  
  // Role hierarchy mapping
  const roleHierarchy: Record<string, string[]> = {
    'Admin': ['Admin', 'Manager', 'User'],
    'Manager': ['Manager', 'User'], 
    'User': ['User']
  };
  
  const userRole = user.profile.role;
  return roleHierarchy[userRole]?.includes(requiredRole) ?? false;
} 