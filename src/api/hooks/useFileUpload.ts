import { useState, useCallback } from "react";
import VeasyFileManagerAPI from "../index";
import {
  UploadOptions,
  FileDto,
  ChunkedUploadOptions,
  UploadProgress,
} from "../types/interfaces";

interface FileUploadHookResult {
  upload: (file: File, options?: UploadOptions) => Promise<FileDto>;
  uploadMultiple: (
    files: File[],
    options?: UploadOptions
  ) => Promise<FileDto[]>;
  chunkedUpload: (
    file: File,
    options?: ChunkedUploadOptions
  ) => Promise<FileDto>;
  resumeUpload: (
    sessionId: string,
    file: File,
    options?: ChunkedUploadOptions
  ) => Promise<FileDto>;
  progress: number;
  isUploading: boolean;
  error: Error | null;
  reset: () => void;
  cancelUpload: () => void;
}

interface UseFileUploadOptions {
  onUploadComplete?: (file: FileDto) => void;
  onUploadError?: (error: Error) => void;
  onProgress?: (progress: number) => void;
  autoChunkedThreshold?: number; // Size in bytes after which to use chunked upload automatically
}

/**
 * React hook for handling file uploads with progress tracking
 * @param api VeasyFileManagerAPI instance
 * @param options Hook options
 * @returns File upload methods and state
 */
export function useFileUpload(
  api: VeasyFileManagerAPI,
  hookOptions?: UseFileUploadOptions
): FileUploadHookResult {
  const [progress, setProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [cancelTokenSource, setCancelTokenSource] =
    useState<AbortController | null>(null);

  // Reset state
  const reset = useCallback(() => {
    setProgress(0);
    setIsUploading(false);
    setError(null);
  }, []);

  // Cancel upload
  const cancelUpload = useCallback(() => {
    if (cancelTokenSource) {
      cancelTokenSource.abort();
      setIsUploading(false);
      setCancelTokenSource(null);
    }
  }, [cancelTokenSource]);

  // Chunked upload with progress - declare first
  const chunkedUpload = useCallback(
    async (
      file: File,
      uploadOptions?: ChunkedUploadOptions
    ): Promise<FileDto> => {
      reset();
      setIsUploading(true);

      // Create AbortController for cancellation
      const abortController = new AbortController();
      setCancelTokenSource(abortController);

      try {
        // Prepare options with progress callback
        const combinedOptions: ChunkedUploadOptions = {
          ...uploadOptions,
          onProgress: (progressData: UploadProgress) => {
            const currentProgress = progressData.progress;
            setProgress(currentProgress);
            hookOptions?.onProgress?.(currentProgress);
            uploadOptions?.onProgress?.(progressData);
          },
        };

        const result = await api.chunkedUpload.uploadLargeFile(
          file,
          combinedOptions
        );

        // Call hook's onUploadComplete callback
        hookOptions?.onUploadComplete?.(result);
        return result;
      } catch (err) {
        // Check if the error is due to cancellation
        if (abortController.signal.aborted) {
          const cancelError = new Error("Upload cancelled");
          setError(cancelError);
          throw cancelError;
        }

        const error = err as Error;
        // Don't retry on circuit breaker errors
        if ((error as any)?.isCircuitBreakerError?.()) {
          setError(error);
          hookOptions?.onUploadError?.(error);
          throw error;
        }

        setError(error);
        hookOptions?.onUploadError?.(error);
        throw error;
      } finally {
        setIsUploading(false);
        setCancelTokenSource(null);
      }
    },
    [api, hookOptions, reset]
  );

  // Standard upload
  const upload = useCallback(
    async (file: File, uploadOptions?: UploadOptions): Promise<FileDto> => {
      reset();
      setIsUploading(true);

      try {
        // Use chunked upload automatically if file size exceeds threshold
        const autoChunkedThreshold =
          hookOptions?.autoChunkedThreshold ?? 100 * 1024 * 1024; // 100MB default

        if (file.size > autoChunkedThreshold) {
          return await chunkedUpload(file, uploadOptions);
        }

        const result = await api.files.upload(file, uploadOptions);

        setProgress(100);
        hookOptions?.onUploadComplete?.(result);

        return result;
      } catch (err) {
        const error = err as Error;
        // Don't retry on circuit breaker errors
        if ((error as any)?.isCircuitBreakerError?.()) {
          setError(error);
          hookOptions?.onUploadError?.(error);
          throw error;
        }
        
        setError(error);
        hookOptions?.onUploadError?.(error);
        throw error;
      } finally {
        setIsUploading(false);
      }
    },
    [api, hookOptions, reset, chunkedUpload]
  );

  // Multiple files upload
  const uploadMultiple = useCallback(
    async (
      files: File[],
      uploadOptions?: UploadOptions
    ): Promise<FileDto[]> => {
      reset();
      setIsUploading(true);

      try {
        const results: FileDto[] = [];

        // Process files one by one to track overall progress
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const result = await api.files.upload(file, uploadOptions);
          results.push(result);

          // Update progress
          const currentProgress = Math.round(((i + 1) / files.length) * 100);
          setProgress(currentProgress);
          hookOptions?.onProgress?.(currentProgress);
        }

        return results;
      } catch (err) {
        const error = err as Error;
        // Don't retry on circuit breaker errors
        if ((error as any)?.isCircuitBreakerError?.()) {
          setError(error);
          hookOptions?.onUploadError?.(error);
          throw error;
        }
        
        setError(error);
        hookOptions?.onUploadError?.(error);
        throw error;
      } finally {
        setIsUploading(false);
      }
    },
    [api, hookOptions, reset]
  );

  // Resume chunked upload (not implemented yet)
  const resumeUpload = useCallback(
    async (
      sessionId: string,
      file: File,
      uploadOptions?: ChunkedUploadOptions
    ): Promise<FileDto> => {
      throw new Error("Resume upload functionality is not yet implemented");
    },
    []
  );

  return {
    upload,
    uploadMultiple,
    chunkedUpload,
    resumeUpload,
    progress,
    isUploading,
    error,
    reset,
    cancelUpload,
  };
}
