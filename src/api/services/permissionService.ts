import { ApiClient } from '../core/apiClient';
import {
  Permission,
  ErrorCode
} from '../types/interfaces';
import { ValidationApiError, ApiError } from '../core/apiClient';

export interface PermissionTypeDto {
  value: Permission;
  name: string;
  description: string;
  applicableToFiles: boolean;
  applicableToFolders: boolean;
  sortOrder: number;
}

export interface PermissionRequest {
  userId?: string;
  roleId?: string;
  permission: Permission;
  expiresAt?: string;
  inheritToChildren?: boolean; // For folders
}

export interface PermissionDto {
  id: string;
  resourceId: string;
  resourceType: 'file' | 'folder';
  userId?: string;
  roleId?: string;
  userName?: string;
  roleName?: string;
  permission: Permission;
  grantedAt: string;
  expiresAt?: string;
  grantedBy: string;
  grantedByName: string;
  inheritToChildren?: boolean;
  isActive: boolean;
}

export interface BulkPermissionRequest {
  resourceIds: string[];
  permission: PermissionRequest;
}

export interface BulkPermissionResponse {
  successful: string[];
  failed: Array<{
    resourceId: string;
    error: string;
  }>;
  totalProcessed: number;
}

export interface EffectivePermissions {
  resourceId: string;
  resourceType: 'file' | 'folder';
  permissions: Permission[];
  inheritedPermissions: Array<{
    permission: Permission;
    sourceResourceId: string;
    sourceResourceName: string;
  }>;
  canRead: boolean;
  canWrite: boolean;
  canDelete: boolean;
  canShare: boolean;
  canAdmin: boolean;
}

export class PermissionService {
  private client: ApiClient;

  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }

  /**
   * Get available permission types
   * @param resourceType Optional filter by resource type
   * @returns List of available permission types
   */
  async getPermissionTypes(resourceType?: 'file' | 'folder'): Promise<PermissionTypeDto[]> {
    const params: Record<string, any> = {};
    if (resourceType) params.resourceType = resourceType;

    try {
      return await this.client.get<PermissionTypeDto[]>('/permissions/types', { params });
    } catch (error) {
      throw this.handlePermissionTypesError(error as ApiError);
    }
  }

  /**
   * Grant permission for a file
   * @param fileId File ID
   * @param permission Permission details
   * @returns Permission ID
   */
  async grantFilePermission(fileId: string, permission: PermissionRequest): Promise<string> {
    this.validateId(fileId, 'fileId');
    this.validatePermissionRequest(permission);

    try {
      return await this.client.post<string>(`/files/${fileId}/permissions`, permission);
    } catch (error) {
      throw this.handleGrantError(error as ApiError, 'file');
    }
  }

  /**
   * Grant permission for a folder
   * @param folderId Folder ID
   * @param permission Permission details
   * @returns Permission ID
   */
  async grantFolderPermission(folderId: string, permission: PermissionRequest): Promise<string> {
    this.validateId(folderId, 'folderId');
    this.validatePermissionRequest(permission);

    try {
      return await this.client.post<string>(`/folders/${folderId}/permissions`, permission);
    } catch (error) {
      throw this.handleGrantError(error as ApiError, 'folder');
    }
  }

  /**
   * List permissions for a file
   * @param fileId File ID
   * @returns List of file permissions
   */
  async listFilePermissions(fileId: string): Promise<PermissionDto[]> {
    this.validateId(fileId, 'fileId');

    try {
      return await this.client.get<PermissionDto[]>(`/files/${fileId}/permissions`);
    } catch (error) {
      throw this.handleListError(error as ApiError, 'file');
    }
  }

  /**
   * List permissions for a folder
   * @param folderId Folder ID
   * @returns List of folder permissions
   */
  async listFolderPermissions(folderId: string): Promise<PermissionDto[]> {
    this.validateId(folderId, 'folderId');

    try {
      return await this.client.get<PermissionDto[]>(`/folders/${folderId}/permissions`);
    } catch (error) {
      throw this.handleListError(error as ApiError, 'folder');
    }
  }

  /**
   * Get specific permission details
   * @param permissionId Permission ID
   * @returns Permission details
   */
  async getPermission(permissionId: string): Promise<PermissionDto> {
    this.validateId(permissionId, 'permissionId');

    try {
      return await this.client.get<PermissionDto>(`/permissions/${permissionId}`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Permission');
    }
  }

  /**
   * Update an existing permission
   * @param permissionId Permission ID
   * @param updates Permission updates
   * @returns Updated permission
   */
  async updatePermission(permissionId: string, updates: {
    permission?: Permission;
    expiresAt?: string;
    inheritToChildren?: boolean;
  }): Promise<PermissionDto> {
    this.validateId(permissionId, 'permissionId');
    this.validatePermissionUpdates(updates);

    try {
      return await this.client.put<PermissionDto>(`/permissions/${permissionId}`, updates);
    } catch (error) {
      throw this.handleUpdateError(error as ApiError);
    }
  }

  /**
   * Revoke a permission
   * @param permissionId Permission ID
   */
  async revokePermission(permissionId: string): Promise<void> {
    this.validateId(permissionId, 'permissionId');

    try {
      await this.client.delete(`/permissions/${permissionId}`);
    } catch (error) {
      throw this.handleRevokeError(error as ApiError);
    }
  }

  /**
   * Check if user has specific permission
   * @param resourceId Resource ID (file or folder)
   * @param resourceType Resource type
   * @param permission Permission to check
   * @param userId Optional user ID (defaults to current user)
   * @returns Whether user has permission
   */
  async checkPermission(
    resourceId: string,
    resourceType: 'file' | 'folder',
    permission: Permission,
    userId?: string
  ): Promise<{ hasPermission: boolean; reason?: string }> {
    this.validateId(resourceId, 'resourceId');
    this.validateResourceType(resourceType);

    const params: Record<string, any> = {
      resourceType,
      permission
    };
    if (userId) params.userId = userId;

    try {
      return await this.client.get(`/permissions/check/${resourceId}`, { params });
    } catch (error) {
      throw this.handleCheckError(error as ApiError);
    }
  }

  /**
   * Get effective permissions for a resource
   * @param resourceId Resource ID (file or folder)
   * @param resourceType Resource type
   * @param userId Optional user ID (defaults to current user)
   * @returns Effective permissions including inherited permissions
   */
  async getEffectivePermissions(
    resourceId: string,
    resourceType: 'file' | 'folder',
    userId?: string
  ): Promise<EffectivePermissions> {
    this.validateId(resourceId, 'resourceId');
    this.validateResourceType(resourceType);

    const params: Record<string, any> = { resourceType };
    if (userId) params.userId = userId;

    try {
      return await this.client.get<EffectivePermissions>(`/permissions/effective/${resourceId}`, { params });
    } catch (error) {
      throw this.handleEffectiveError(error as ApiError);
    }
  }

  /**
   * Grant permissions to multiple resources
   * @param request Bulk permission request
   * @returns Bulk operation results
   */
  async bulkGrantPermissions(request: BulkPermissionRequest): Promise<BulkPermissionResponse> {
    this.validateBulkPermissionRequest(request);

    try {
      return await this.client.post<BulkPermissionResponse>('/permissions/bulk/grant', request);
    } catch (error) {
      throw this.handleBulkError(error as ApiError);
    }
  }

  /**
   * Revoke permissions from multiple resources
   * @param resourceIds Resource IDs
   * @param userId User ID (optional, if not provided removes all permissions for resources)
   * @param permission Optional specific permission to revoke
   * @returns Bulk operation results
   */
  async bulkRevokePermissions(
    resourceIds: string[],
    userId?: string,
    permission?: Permission
  ): Promise<BulkPermissionResponse> {
    this.validateBulkRevokeRequest(resourceIds, userId, permission);

    const request = {
      resourceIds,
      ...(userId && { userId }),
      ...(permission && { permission })
    };

    try {
      return await this.client.post<BulkPermissionResponse>('/permissions/bulk/revoke', request);
    } catch (error) {
      throw this.handleBulkError(error as ApiError);
    }
  }

  // Validation methods

  private validateId(id: string, fieldName: string): void {
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new ValidationApiError(`Invalid ${fieldName}`, [
        { field: fieldName, message: `${fieldName} is required and must be a non-empty string`, code: 'REQUIRED' }
      ]);
    }
  }

  private validateResourceType(resourceType: 'file' | 'folder'): void {
    if (!resourceType || !['file', 'folder'].includes(resourceType)) {
      throw new ValidationApiError('Invalid resource type', [
        { field: 'resourceType', message: 'Resource type must be either "file" or "folder"', code: 'INVALID_VALUE' }
      ]);
    }
  }

  private validatePermissionRequest(permission: PermissionRequest): void {
    if (!permission) {
      throw new ValidationApiError('Permission request is required', [
        { field: 'permission', message: 'Permission data is required', code: 'REQUIRED' }
      ]);
    }

    if (!permission.userId && !permission.roleId) {
      throw new ValidationApiError('User or role required', [
        { field: 'userId', message: 'Either userId or roleId must be specified', code: 'REQUIRED' }
      ]);
    }

    if (permission.userId && permission.roleId) {
      throw new ValidationApiError('Cannot specify both user and role', [
        { field: 'userId', message: 'Cannot specify both userId and roleId', code: 'INVALID_OPERATION' }
      ]);
    }

    if (!permission.permission) {
      throw new ValidationApiError('Permission type is required', [
        { field: 'permission', message: 'Permission type must be specified', code: 'REQUIRED' }
      ]);
    }

    if (!Object.values(Permission).includes(permission.permission)) {
      throw new ValidationApiError('Invalid permission type', [
        { field: 'permission', message: 'Invalid permission type', code: 'INVALID_VALUE' }
      ]);
    }

    if (permission.expiresAt) {
      const expiryDate = new Date(permission.expiresAt);
      if (isNaN(expiryDate.getTime()) || expiryDate <= new Date()) {
        throw new ValidationApiError('Invalid expiry date', [
          { field: 'expiresAt', message: 'Expiry date must be a valid future date', code: 'INVALID_VALUE' }
        ]);
      }
    }
  }

  private validatePermissionUpdates(updates: {
    permission?: Permission;
    expiresAt?: string;
    inheritToChildren?: boolean;
  }): void {
    if (!updates || Object.keys(updates).length === 0) {
      throw new ValidationApiError('Updates are required', [
        { field: 'updates', message: 'At least one field must be provided for update', code: 'REQUIRED' }
      ]);
    }

    if (updates.permission && !Object.values(Permission).includes(updates.permission)) {
      throw new ValidationApiError('Invalid permission type', [
        { field: 'permission', message: 'Invalid permission type', code: 'INVALID_VALUE' }
      ]);
    }

    if (updates.expiresAt) {
      const expiryDate = new Date(updates.expiresAt);
      if (isNaN(expiryDate.getTime()) || expiryDate <= new Date()) {
        throw new ValidationApiError('Invalid expiry date', [
          { field: 'expiresAt', message: 'Expiry date must be a valid future date', code: 'INVALID_VALUE' }
        ]);
      }
    }
  }

  private validateBulkPermissionRequest(request: BulkPermissionRequest): void {
    if (!request) {
      throw new ValidationApiError('Bulk permission request is required', [
        { field: 'request', message: 'Request data is required', code: 'REQUIRED' }
      ]);
    }

    if (!request.resourceIds || !Array.isArray(request.resourceIds) || request.resourceIds.length === 0) {
      throw new ValidationApiError('Resource IDs are required', [
        { field: 'resourceIds', message: 'At least one resource ID is required', code: 'REQUIRED' }
      ]);
    }

    if (request.resourceIds.length > 100) {
      throw new ValidationApiError('Too many resources', [
        { field: 'resourceIds', message: 'Cannot process more than 100 resources at once', code: 'TOO_MANY_ITEMS' }
      ]);
    }

    request.resourceIds.forEach((id, index) => {
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        throw new ValidationApiError('Invalid resource ID', [
          { field: `resourceIds[${index}]`, message: 'Resource ID must be a non-empty string', code: 'INVALID_VALUE' }
        ]);
      }
    });

    this.validatePermissionRequest(request.permission);
  }

  private validateBulkRevokeRequest(resourceIds: string[], userId?: string, permission?: Permission): void {
    if (!resourceIds || !Array.isArray(resourceIds) || resourceIds.length === 0) {
      throw new ValidationApiError('Resource IDs are required', [
        { field: 'resourceIds', message: 'At least one resource ID is required', code: 'REQUIRED' }
      ]);
    }

    if (resourceIds.length > 100) {
      throw new ValidationApiError('Too many resources', [
        { field: 'resourceIds', message: 'Cannot process more than 100 resources at once', code: 'TOO_MANY_ITEMS' }
      ]);
    }

    resourceIds.forEach((id, index) => {
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        throw new ValidationApiError('Invalid resource ID', [
          { field: `resourceIds[${index}]`, message: 'Resource ID must be a non-empty string', code: 'INVALID_VALUE' }
        ]);
      }
    });

    if (permission && !Object.values(Permission).includes(permission)) {
      throw new ValidationApiError('Invalid permission type', [
        { field: 'permission', message: 'Invalid permission type', code: 'INVALID_VALUE' }
      ]);
    }
  }

  // Error handling methods

  private handlePermissionTypesError(error: ApiError): ApiError {
    if (error.statusCode === 500) {
      return new ApiError('Failed to retrieve permission types', 500, ErrorCode.INTERNAL_SERVER_ERROR, error.correlationId);
    }
    return error;
  }

  private handleGrantError(error: ApiError, resourceType: string): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Permission already exists', 409, ErrorCode.PERMISSION_ALREADY_EXISTS, error.correlationId);
    }
    if (error.statusCode === 403) {
      return new ApiError(`Insufficient permissions to grant access to ${resourceType}`, 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 404) {
      return new ApiError(`${resourceType.charAt(0).toUpperCase() + resourceType.slice(1)} not found`, 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }

  private handleListError(error: ApiError, resourceType: string): ApiError {
    if (error.statusCode === 403) {
      return new ApiError(`Insufficient permissions to view ${resourceType} permissions`, 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 404) {
      return new ApiError(`${resourceType.charAt(0).toUpperCase() + resourceType.slice(1)} not found`, 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }

  private handleNotFoundError(error: ApiError, resourceType: string): ApiError {
    if (error.statusCode === 404) {
      return new ApiError(`${resourceType} not found`, 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }

  private handleUpdateError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to update permission', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 409) {
      return new ApiError('Permission update conflict', 409, 'PERMISSION_CONFLICT', error.correlationId);
    }
    return this.handleNotFoundError(error, 'Permission');
  }

  private handleRevokeError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to revoke permission', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleNotFoundError(error, 'Permission');
  }

  private handleCheckError(error: ApiError): ApiError {
    if (error.statusCode === 404) {
      return new ApiError('Resource not found for permission check', 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }

  private handleEffectiveError(error: ApiError): ApiError {
    if (error.statusCode === 404) {
      return new ApiError('Resource not found for effective permissions', 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to view effective permissions', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleBulkError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions for bulk operation', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 413) {
      return new ApiError('Too many items for bulk operation', 413, 'TOO_MANY_ITEMS', error.correlationId);
    }
    return error;
  }
} 