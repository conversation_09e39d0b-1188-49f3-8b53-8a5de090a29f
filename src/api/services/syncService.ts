import { ApiClient } from '../core/apiClient';
import {
  SyncOptions,
  SyncJobDto,
  SyncStatusResponse,
  SyncTriggerResponse,
  SyncStatus,
  SyncDirection,
  ErrorCode
} from '../types/interfaces';
import { ValidationApiError, ApiError } from '../core/apiClient';

export interface SyncJobRequest {
  fileId?: string;
  folderId?: string;
  direction: SyncDirection;
  forceSync?: boolean;
  priority?: 'Low' | 'Normal' | 'High';
}

export interface SyncStatistics {
  totalJobs: number;
  pendingJobs: number;
  completedJobs: number;
  failedJobs: number;
  totalSyncedFiles: number;
  totalSyncedSize: number;
  lastSyncTime?: string;
  avgSyncDuration: number;
}

export interface SyncSettings {
  autoSyncEnabled: boolean;
  syncInterval: number; // in minutes
  defaultDirection: SyncDirection;
  excludedFileTypes: string[];
  maxFileSize: number;
  enableNotifications: boolean;
}

export class SyncService {
  private client: ApiClient;

  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }

  /**
   * Trigger Google Drive synchronization
   * @param options Sync options
   * @returns Sync trigger response with job ID
   */
  async triggerGoogleDriveSync(options?: SyncOptions): Promise<SyncTriggerResponse> {
    this.validateSyncOptions(options);

    try {
      return await this.client.post<SyncTriggerResponse>('/sync/google-drive', options || {});
    } catch (error) {
      throw this.handleSyncError(error as ApiError);
    }
  }

  /**
   * Get synchronization status
   * @param options Optional query options
   * @returns Sync status information
   */
  async getSyncStatus(options?: {
    fileId?: string;
    provider?: string;
    folderId?: string;
  }): Promise<SyncStatusResponse> {
    const params: Record<string, any> = {};
    if (options?.fileId) {
      this.validateId(options.fileId, 'fileId');
      params.fileId = options.fileId;
    }
    if (options?.folderId) {
      this.validateId(options.folderId, 'folderId');
      params.folderId = options.folderId;
    }
    if (options?.provider) params.provider = options.provider;

    try {
      return await this.client.get<SyncStatusResponse>('/sync/status', { params });
    } catch (error) {
      throw this.handleStatusError(error as ApiError);
    }
  }

  /**
   * Get synchronization status (backward compatibility)
   * @param fileId Optional file ID to get specific file sync status
   * @param provider Optional provider filter (e.g., "GoogleDrive")
   * @returns Sync status information
   */
  async getSyncStatusLegacy(fileId?: string, provider?: string): Promise<SyncStatusResponse> {
    return this.getSyncStatus({ fileId, provider });
  }

  /**
   * Get all sync jobs with filtering and pagination
   * @param options Query options
   * @returns List of sync jobs
   */
  async getSyncJobs(options?: {
    page?: number;
    pageSize?: number;
    status?: SyncStatus;
    provider?: string;
    direction?: SyncDirection;
    createdAfter?: string;
    createdBefore?: string;
  }): Promise<{ jobs: SyncJobDto[]; totalCount: number; page: number; pageSize: number }> {
    const params: Record<string, any> = {};

    if (options?.page) params.page = options.page;
    if (options?.pageSize) params.pageSize = options.pageSize;
    if (options?.status) params.status = options.status;
    if (options?.provider) params.provider = options.provider;
    if (options?.direction) params.direction = options.direction;
    if (options?.createdAfter) params.createdAfter = options.createdAfter;
    if (options?.createdBefore) params.createdBefore = options.createdBefore;

    try {
      return await this.client.get('/sync/jobs', { params });
    } catch (error) {
      throw this.handleJobsError(error as ApiError);
    }
  }

  /**
   * Get specific sync job details
   * @param jobId Sync job ID
   * @returns Sync job information
   */
  async getSyncJob(jobId: string): Promise<SyncJobDto> {
    this.validateId(jobId, 'jobId');

    try {
      return await this.client.get<SyncJobDto>(`/sync/jobs/${jobId}`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Sync job');
    }
  }

  /**
   * Cancel a running sync job
   * @param jobId Sync job ID
   */
  async cancelSyncJob(jobId: string): Promise<void> {
    this.validateId(jobId, 'jobId');

    try {
      await this.client.post(`/sync/jobs/${jobId}/cancel`);
    } catch (error) {
      throw this.handleCancelError(error as ApiError);
    }
  }



  /**
   * Get sync statistics
   * @param provider Optional provider filter
   * @param dateRange Optional date range filter
   * @returns Sync statistics
   */
  async getSyncStatistics(
    provider?: string,
    dateRange?: { from: string; to: string }
  ): Promise<SyncStatistics> {
    const params: Record<string, any> = {};
    if (provider) params.provider = provider;
    if (dateRange?.from) params.dateFrom = dateRange.from;
    if (dateRange?.to) params.dateTo = dateRange.to;

    try {
      return await this.client.get<SyncStatistics>('/sync/statistics', { params });
    } catch (error) {
      throw this.handleStatisticsError(error as ApiError);
    }
  }

  /**
   * Configure sync settings
   * @param settings Sync configuration settings
   * @returns Updated settings
   */
  async configureSyncSettings(settings: Partial<SyncSettings>): Promise<SyncSettings> {
    this.validateSyncSettings(settings);

    try {
      return await this.client.put<SyncSettings>('/sync/settings', settings);
    } catch (error) {
      throw this.handleSettingsError(error as ApiError);
    }
  }

  /**
   * Get current sync settings
   * @returns Current sync settings
   */
  async getSyncSettings(): Promise<SyncSettings> {
    try {
      return await this.client.get<SyncSettings>('/sync/settings');
    } catch (error) {
      throw this.handleSettingsError(error as ApiError);
    }
  }

  /**
   * Create a custom sync job
   * @param request Sync job request
   * @returns Created sync job
   */
  async createSyncJob(request: SyncJobRequest): Promise<SyncJobDto> {
    this.validateSyncJobRequest(request);

    try {
      return await this.client.post<SyncJobDto>('/sync/jobs', request);
    } catch (error) {
      throw this.handleCreateJobError(error as ApiError);
    }
  }

  /**
   * Delete completed or failed sync jobs (cleanup)
   * @param olderThan Delete jobs older than this date
   * @param status Optional status filter for deletion
   * @returns Number of deleted jobs
   */
  async cleanupSyncJobs(
    olderThan: string,
    status?: SyncStatus[]
  ): Promise<{ deletedCount: number; message: string }> {
    if (!olderThan) {
      throw new ValidationApiError('Date filter is required', [
        { field: 'olderThan', message: 'Date filter is required for cleanup', code: 'REQUIRED' }
      ]);
    }

    const params: Record<string, any> = { olderThan };
    if (status && status.length > 0) {
      params.status = status.join(',');
    }

    try {
      return await this.client.delete('/sync/jobs/cleanup', { params });
    } catch (error) {
      throw this.handleCleanupError(error as ApiError);
    }
  }

  /**
   * Force sync all pending files
   * @param provider Optional provider filter
   * @returns Batch sync response
   */
  async forceSyncAll(provider?: string): Promise<{
    totalFiles: number;
    syncJobIds: string[];
    message: string;
  }> {
    const params: Record<string, any> = {};
    if (provider) params.provider = provider;

    try {
      return await this.client.post('/sync/force-all', {}, { params });
    } catch (error) {
      throw this.handleForceSyncError(error as ApiError);
    }
  }

  /**
   * Pause all sync operations
   */
  async pauseSync(): Promise<{ message: string; pausedJobs: number }> {
    try {
      return await this.client.post('/sync/pause');
    } catch (error) {
      throw this.handlePauseError(error as ApiError);
    }
  }

  /**
   * Resume paused sync operations
   */
  async resumeSync(): Promise<{ message: string; resumedJobs: number }> {
    try {
      return await this.client.post('/sync/resume');
    } catch (error) {
      throw this.handleResumeError(error as ApiError);
    }
  }

  // Validation methods

  private validateSyncOptions(options?: SyncOptions): void {
    if (!options) return;

    if (options.fileId) {
      this.validateId(options.fileId, 'fileId');
    }
  }

  private validateId(id: string, fieldName: string): void {
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new ValidationApiError(`Invalid ${fieldName}`, [
        { field: fieldName, message: `${fieldName} is required and must be a non-empty string`, code: 'REQUIRED' }
      ]);
    }
  }

  private validateSyncSettings(settings: Partial<SyncSettings>): void {
    if (!settings || Object.keys(settings).length === 0) {
      throw new ValidationApiError('Settings are required', [
        { field: 'settings', message: 'At least one setting must be provided', code: 'REQUIRED' }
      ]);
    }

    if (settings.syncInterval !== undefined) {
      if (!Number.isInteger(settings.syncInterval) || settings.syncInterval < 1 || settings.syncInterval > 1440) {
        throw new ValidationApiError('Invalid sync interval', [
          { field: 'syncInterval', message: 'Sync interval must be between 1 and 1440 minutes', code: 'INVALID_VALUE' }
        ]);
      }
    }



    if (settings.maxFileSize !== undefined) {
      if (!Number.isInteger(settings.maxFileSize) || settings.maxFileSize < 1) {
        throw new ValidationApiError('Invalid max file size', [
          { field: 'maxFileSize', message: 'Max file size must be a positive integer', code: 'INVALID_VALUE' }
        ]);
      }
    }

    if (settings.excludedFileTypes !== undefined) {
      if (!Array.isArray(settings.excludedFileTypes)) {
        throw new ValidationApiError('Invalid excluded file types', [
          { field: 'excludedFileTypes', message: 'Excluded file types must be an array', code: 'INVALID_TYPE' }
        ]);
      }
    }
  }

  private validateSyncJobRequest(request: SyncJobRequest): void {
    if (!request) {
      throw new ValidationApiError('Sync job request is required', [
        { field: 'request', message: 'Request data is required', code: 'REQUIRED' }
      ]);
    }

    if (!request.direction) {
      throw new ValidationApiError('Sync direction is required', [
        { field: 'direction', message: 'Sync direction must be specified', code: 'REQUIRED' }
      ]);
    }

    if (!Object.values(SyncDirection).includes(request.direction)) {
      throw new ValidationApiError('Invalid sync direction', [
        { field: 'direction', message: 'Invalid sync direction value', code: 'INVALID_VALUE' }
      ]);
    }

    if (request.fileId && request.folderId) {
      throw new ValidationApiError('Cannot specify both file and folder', [
        { field: 'fileId', message: 'Cannot sync both file and folder in the same job', code: 'INVALID_OPERATION' }
      ]);
    }

    if (!request.fileId && !request.folderId) {
      throw new ValidationApiError('File or folder ID required', [
        { field: 'fileId', message: 'Either fileId or folderId must be specified', code: 'REQUIRED' }
      ]);
    }

    if (request.fileId) {
      this.validateId(request.fileId, 'fileId');
    }

    if (request.folderId) {
      this.validateId(request.folderId, 'folderId');
    }

    if (request.priority && !['Low', 'Normal', 'High'].includes(request.priority)) {
      throw new ValidationApiError('Invalid priority', [
        { field: 'priority', message: 'Priority must be Low, Normal, or High', code: 'INVALID_VALUE' }
      ]);
    }
  }

  // Error handling methods

  private handleSyncError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Sync already in progress', 409, 'SYNC_IN_PROGRESS', error.correlationId);
    }
    if (error.statusCode === 503) {
      return new ApiError('Sync service unavailable', 503, ErrorCode.SYNC_PROVIDER_UNAVAILABLE, error.correlationId);
    }
    return error;
  }

  private handleStatusError(error: ApiError): ApiError {
    if (error.statusCode === 404) {
      return new ApiError('No sync status found', 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }

  private handleJobsError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to view sync jobs', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleNotFoundError(error: ApiError, resourceType: string): ApiError {
    if (error.statusCode === 404) {
      return new ApiError(`${resourceType} not found`, 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }

  private handleCancelError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Sync job cannot be cancelled in current state', 409, 'INVALID_STATE', error.correlationId);
    }
    return this.handleNotFoundError(error, 'Sync job');
  }



  private handleStatisticsError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to view sync statistics', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleSettingsError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to modify sync settings', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleCreateJobError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Sync job already exists for this resource', 409, 'DUPLICATE_SYNC_JOB', error.correlationId);
    }
    if (error.statusCode === 507) {
      return new ApiError('Sync quota exceeded', 507, ErrorCode.SYNC_QUOTA_EXCEEDED, error.correlationId);
    }
    return error;
  }

  private handleCleanupError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to cleanup sync jobs', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleForceSyncError(error: ApiError): ApiError {
    if (error.statusCode === 503) {
      return new ApiError('Sync service unavailable for batch operation', 503, ErrorCode.SYNC_PROVIDER_UNAVAILABLE, error.correlationId);
    }
    return error;
  }

  private handlePauseError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Sync operations are already paused', 409, 'ALREADY_PAUSED', error.correlationId);
    }
    return error;
  }

  private handleResumeError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Sync operations are already running', 409, 'ALREADY_RUNNING', error.correlationId);
    }
    return error;
  }
}

