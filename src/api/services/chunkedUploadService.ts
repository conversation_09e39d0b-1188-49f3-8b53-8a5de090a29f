import { ApiClient } from '../core/apiClient';
import {
  FileDto,
  ChunkedUploadOptions,
  UploadProgress,
  ChunkedUploadInitRequest,
  ChunkedUploadSession,
  ChunkUploadResult,
  CompleteChunkedUploadRequest,
  ChunkedUploadStatusResponse,
  ErrorCode
} from '../types/interfaces';
import { ValidationApiError, ApiError } from '../core/apiClient';

export class ChunkedUploadService {
  private client: ApiClient;
  private activeUploads: Map<string, AbortController> = new Map();
  private defaultChunkSize: number = 10 * 1024 * 1024; // 10MB default
  private maxConcurrentUploads: number = 3;
  
  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }
  
  /**
   * Initialize a chunked upload session for large files
   * @param request Upload initialization request
   * @returns Upload session details
   */
  async initializeUpload(request: ChunkedUploadInitRequest): Promise<ChunkedUploadSession> {
    this.validateInitRequest(request);

    try {
      return await this.client.post<ChunkedUploadSession>('/files/upload/chunked/initialize', request);
    } catch (error) {
      throw this.handleInitError(error as ApiError);
    }
  }
  
  /**
   * Upload a specific chunk of a file
   * @param sessionId Upload session ID
   * @param chunkNumber Chunk number (1-based)
   * @param chunkData Chunk data as File or Blob
   * @returns Chunk upload result
   */
  async uploadChunk(
    sessionId: string,
    chunkNumber: number,
    chunkData: File | Blob
  ): Promise<ChunkUploadResult> {
    this.validateChunkParams(sessionId, chunkNumber, chunkData);

    const formData = new FormData();
    formData.append('chunk', chunkData);

    try {
      return await this.client.post<ChunkUploadResult>(
        `/files/upload/chunked/${sessionId}/chunk/${chunkNumber}`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' }
        }
      );
    } catch (error) {
      throw this.handleChunkError(error as ApiError, chunkNumber);
    }
  }
  
  /**
   * Complete a chunked upload session
   * @param sessionId Upload session ID
   * @param request Completion request with chunk hashes
   * @returns Final file information
   */
  async completeUpload(
    sessionId: string,
    request: CompleteChunkedUploadRequest
  ): Promise<FileDto> {
    this.validateCompleteParams(sessionId, request);

    try {
      return await this.client.post<FileDto>(
        `/files/upload/chunked/${sessionId}/complete`,
        request
      );
    } catch (error) {
      throw this.handleCompleteError(error as ApiError);
    }
  }
  
  /**
   * Get upload session status
   * @param sessionId Upload session ID
   * @returns Current upload status
   */
  async getUploadStatus(sessionId: string): Promise<ChunkedUploadStatusResponse> {
    this.validateSessionId(sessionId);

    try {
      return await this.client.get<ChunkedUploadStatusResponse>(
        `/files/upload/chunked/${sessionId}/status`
      );
    } catch (error) {
      throw this.handleStatusError(error as ApiError);
    }
  }
  
  /**
   * Cancel an active upload session
   * @param sessionId Upload session ID
   */
  async cancelUpload(sessionId: string): Promise<void> {
    this.validateSessionId(sessionId);

    // Cancel any active request
    const controller = this.activeUploads.get(sessionId);
    if (controller) {
      controller.abort();
      this.activeUploads.delete(sessionId);
    }

    try {
      await this.client.delete(`/files/upload/chunked/${sessionId}`);
    } catch (error) {
      throw this.handleCancelError(error as ApiError);
    }
  }
  
  /**
   * Upload large file with chunked upload
   * @param file File to upload
   * @param options Upload options
   * @returns Promise that resolves to uploaded file
   */
  async uploadLargeFile(file: File, options?: ChunkedUploadOptions): Promise<FileDto> {
    const chunkSize = options?.chunkSize || 1024 * 1024; // 1MB default
    const totalChunks = Math.ceil(file.size / chunkSize);
    let uploadedChunks = 0;
    const chunkHashes: string[] = [];

    // Initialize upload session
    const initRequest: ChunkedUploadInitRequest = {
      fileName: file.name,
      totalFileSize: file.size,
      contentType: file.type,
      parentFolderId: options?.parentFolderId,
      displayName: options?.displayName,
      description: options?.description,
      syncToGoogleDrive: options?.syncToGoogleDrive,
      tags: options?.tags
    };

    const session = await this.initializeUpload(initRequest);
    const controller = new AbortController();
    this.activeUploads.set(session.sessionId, controller);

    try {
      // Upload chunks
      for (let chunkNumber = 1; chunkNumber <= totalChunks; chunkNumber++) {
        if (controller.signal.aborted) {
          throw new Error('Upload cancelled');
        }

        const start = (chunkNumber - 1) * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        const chunk = file.slice(start, end);

        try {
          const result = await this.uploadChunk(session.sessionId, chunkNumber, chunk);
          chunkHashes.push(result.chunkHash);
          uploadedChunks++;

          // Report progress
          const progress: UploadProgress = {
            totalChunks,
            uploadedChunks,
            progress: (uploadedChunks / totalChunks) * 100,
            chunkNumber,
            uploadedBytes: uploadedChunks * chunkSize,
            totalBytes: file.size
          };

          options?.onProgress?.(progress);
          options?.onChunkComplete?.(chunkNumber, totalChunks);

        } catch (error) {
          options?.onError?.(error as Error, chunkNumber);
          throw error;
        }
      }

      // Complete upload
      const completeRequest: CompleteChunkedUploadRequest = {
        chunkHashes
      };

      const finalFile = await this.completeUpload(session.sessionId, completeRequest);
      this.activeUploads.delete(session.sessionId);
      
      return finalFile;

    } catch (error) {
      // Cleanup on error
      try {
        await this.cancelUpload(session.sessionId);
      } catch (cancelError) {
        console.error('Failed to cancel upload:', cancelError);
      }
      throw error;
    }
  }

  // Validation methods
  private validateInitRequest(request: ChunkedUploadInitRequest): void {
    if (!request.fileName || request.fileName.trim().length === 0) {
      throw new ValidationApiError('File name is required', [
        { field: 'fileName', message: 'File name cannot be empty', code: 'REQUIRED' }
      ]);
    }

    if (!request.totalFileSize || request.totalFileSize <= 0) {
      throw new ValidationApiError('Invalid file size', [
        { field: 'totalFileSize', message: 'Total file size must be greater than 0', code: 'INVALID_VALUE' }
      ]);
    }

    if (!request.contentType || request.contentType.trim().length === 0) {
      throw new ValidationApiError('Content type is required', [
        { field: 'contentType', message: 'Content type cannot be empty', code: 'REQUIRED' }
      ]);
    }
  }

  private validateSessionId(sessionId: string): void {
    if (!sessionId || sessionId.trim().length === 0) {
      throw new ValidationApiError('Session ID is required', [
        { field: 'sessionId', message: 'Session ID cannot be empty', code: 'REQUIRED' }
      ]);
    }
  }

  private validateChunkParams(sessionId: string, chunkNumber: number, chunkData: File | Blob): void {
    this.validateSessionId(sessionId);

    if (!Number.isInteger(chunkNumber) || chunkNumber < 1) {
      throw new ValidationApiError('Invalid chunk number', [
        { field: 'chunkNumber', message: 'Chunk number must be a positive integer', code: 'INVALID_VALUE' }
      ]);
    }

    if (!chunkData || chunkData.size === 0) {
      throw new ValidationApiError('Chunk data is required', [
        { field: 'chunkData', message: 'Chunk data cannot be empty', code: 'REQUIRED' }
      ]);
    }
  }

  private validateCompleteParams(sessionId: string, request: CompleteChunkedUploadRequest): void {
    this.validateSessionId(sessionId);

    if (!request.chunkHashes || request.chunkHashes.length === 0) {
      throw new ValidationApiError('Chunk hashes are required', [
        { field: 'chunkHashes', message: 'At least one chunk hash is required', code: 'REQUIRED' }
      ]);
    }
  }

  // Error handling methods
  private handleInitError(error: ApiError): ApiError {
    if (error.statusCode === 413) {
      return new ApiError('File too large for chunked upload', 413, ErrorCode.FILE_TOO_LARGE, error.correlationId);
    }
    if (error.statusCode === 507) {
      return new ApiError('Insufficient storage space', 507, ErrorCode.STORAGE_QUOTA_EXCEEDED, error.correlationId);
    }
    return error;
  }

  private handleChunkError(error: ApiError, chunkNumber: number): ApiError {
    if (error.statusCode === 400) {
      return new ApiError(`Invalid chunk ${chunkNumber}`, 400, 'INVALID_CHUNK', error.correlationId);
    }
    if (error.statusCode === 404) {
      return new ApiError('Upload session not found or expired', 404, 'SESSION_NOT_FOUND', error.correlationId);
    }
    return error;
  }

  private handleCompleteError(error: ApiError): ApiError {
    if (error.statusCode === 400) {
      return new ApiError('Chunk verification failed', 400, 'CHUNK_VERIFICATION_FAILED', error.correlationId);
    }
    if (error.statusCode === 404) {
      return new ApiError('Upload session not found', 404, 'SESSION_NOT_FOUND', error.correlationId);
    }
    return error;
  }

  private handleStatusError(error: ApiError): ApiError {
    if (error.statusCode === 404) {
      return new ApiError('Upload session not found', 404, 'SESSION_NOT_FOUND', error.correlationId);
    }
    return error;
  }

  private handleCancelError(error: ApiError): ApiError {
    if (error.statusCode === 404) {
      return new ApiError('Upload session not found', 404, 'SESSION_NOT_FOUND', error.correlationId);
    }
    return error;
  }
}

