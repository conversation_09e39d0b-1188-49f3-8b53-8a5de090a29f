import { ApiClient } from '../core/apiClient';
import {
  FileDto,
  UploadOptions,
  MultiUploadOptions,
  MultiUploadResponse,
  FileUpdateData,
  FileCopyOptions,
  FileMoveOptions,
  DownloadOptions,
  PresignedUrlResponse,
  ShareOptions,
  ShareDto,
  PermissionRequest,
  PermissionDto,
  PaginationInfo,
  SortField,
  SortDirection,
  FileListOptions
} from '../types/interfaces';
import { 
  downloadBlobWithFilename, 
  extractFilenameFromContentDisposition, 
  generateSafeFilename 
} from '@/lib/file-utils';

export interface FileListResponse {
  items: FileDto[];
  pagination: PaginationInfo;
}

export interface BulkDeleteResponse {
  successful: string[];
  failed: { id: string; error: string }[];
}

export class FileService {
  constructor(private apiClient: ApiClient) {}

  /**
   * Upload a single file
   */
  async upload(file: File, options: UploadOptions = {}): Promise<FileDto> {
    const formData = new FormData();
    formData.append('file', file);

    if (options.parentFolderId) {
      formData.append('parentFolderId', options.parentFolderId);
    }
    if (options.displayName) {
      formData.append('displayName', options.displayName);
    }
    if (options.description) {
      formData.append('description', options.description);
    }
    if (options.syncToGoogleDrive !== undefined) {
      formData.append('syncToGoogleDrive', options.syncToGoogleDrive.toString());
    }
    if (options.tags) {
      options.tags.forEach(tag => formData.append('tags', tag));
    }
    if (options.overwriteExisting !== undefined) {
      formData.append('overwriteExisting', options.overwriteExisting.toString());
    }
    if (options.customMetadata) {
      formData.append('customMetadata', JSON.stringify(options.customMetadata));
    }

    return this.apiClient.post<FileDto>('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Upload multiple files
   */
  async uploadMultiple(files: File[], options: MultiUploadOptions = {}): Promise<MultiUploadResponse> {
    const formData = new FormData();
    
    files.forEach(file => {
      formData.append('files', file);
    });

    if (options.parentFolderId) {
      formData.append('parentFolderId', options.parentFolderId);
    }
    if (options.syncToGoogleDrive !== undefined) {
      formData.append('syncToGoogleDrive', options.syncToGoogleDrive.toString());
    }
    if (options.failOnError !== undefined) {
      formData.append('failOnError', options.failOnError.toString());
    }
    if (options.tags) {
      options.tags.forEach(tag => formData.append('tags', tag));
    }

    return this.apiClient.post<MultiUploadResponse>('/files/upload/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Upload file with progress tracking
   */
  async uploadWithProgress(
    file: File,
    options: UploadOptions = {},
    onProgress?: (progressEvent: any) => void
  ): Promise<FileDto> {
    const formData = new FormData();
    formData.append('file', file);

    if (options.parentFolderId) {
      formData.append('parentFolderId', options.parentFolderId);
    }
    if (options.displayName) {
      formData.append('displayName', options.displayName);
    }
    if (options.description) {
      formData.append('description', options.description);
    }
    if (options.syncToGoogleDrive !== undefined) {
      formData.append('syncToGoogleDrive', options.syncToGoogleDrive.toString());
    }
    if (options.tags) {
      options.tags.forEach(tag => formData.append('tags', tag));
    }
    if (options.overwriteExisting !== undefined) {
      formData.append('overwriteExisting', options.overwriteExisting.toString());
    }
    if (options.customMetadata) {
      formData.append('customMetadata', JSON.stringify(options.customMetadata));
    }

    return this.apiClient.uploadWithProgress<FileDto>('/files/upload', formData, onProgress);
  }

  /**
   * Get list of user files with filtering and pagination
   */
  async getList(options: FileListOptions = {}): Promise<FileListResponse> {
    const params = new URLSearchParams();

    if (options.parentFolderId) params.append('parentFolderId', options.parentFolderId);
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.search) params.append('search', options.search);
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);
    if (options.uploaderEmail) params.append('uploaderEmail', options.uploaderEmail);
    if (options.mimeType) params.append('mimeType', options.mimeType);
    if (options.createdAfter) params.append('createdAfter', options.createdAfter);
    if (options.createdBefore) params.append('createdBefore', options.createdBefore);
    if (options.includeShared !== undefined) params.append('includeShared', options.includeShared.toString());
    if (options.isArchived !== undefined) params.append('isArchived', options.isArchived.toString());

    return this.apiClient.get<FileListResponse>(`/files?${params.toString()}`);
  }

  /**
   * Get file details by ID
   */
  async getById(id: string): Promise<FileDto> {
    return this.apiClient.get<FileDto>(`/files/${id}`);
  }

  /**
   * Update file metadata
   */
  async update(id: string, data: FileUpdateData): Promise<FileDto> {
    return this.apiClient.put<FileDto>(`/files/${id}`, data);
  }

  /**
   * Download file or get presigned URL
   */
  async download(id: string, options: DownloadOptions = {}): Promise<Blob | PresignedUrlResponse | { blob: Blob; headers: any }> {
    const params = new URLSearchParams();
    
    if (options.presigned !== undefined) {
      params.append('presigned', options.presigned.toString());
    }
    if (options.expiration) {
      params.append('expiration', options.expiration.toString());
    }

    if (options.presigned) {
      return this.apiClient.get<PresignedUrlResponse>(`/files/${id}/download?${params.toString()}`);
    } else {
      return this.apiClient.downloadFile(`/files/${id}/download?${params.toString()}`);
    }
  }

  /**
   * Download file with correct filename and extension
   */
  async downloadWithFilename(fileInfo: { id: string; name?: string; displayName?: string; mimeType?: string }, options: DownloadOptions = {}): Promise<void> {
    try {
      const response = await this.download(fileInfo.id, { ...options, presigned: false });
      
      // Handle presigned URL case (shouldn't happen with presigned: false, but just in case)
      if ('url' in response) {
        const link = document.createElement('a');
        link.href = response.url;
        link.download = generateSafeFilename(fileInfo.displayName || fileInfo.name, fileInfo.mimeType);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        return;
      }

      // Handle blob response with headers
      let blob: Blob;
      let filename: string;

      if ('blob' in response) {
        // New format with headers
        blob = response.blob;
        
        // Try to extract filename from Content-Disposition header
        const contentDisposition = response.headers['content-disposition'] || response.headers['Content-Disposition'];
        const extractedFilename = extractFilenameFromContentDisposition(contentDisposition);
        
        filename = extractedFilename || generateSafeFilename(fileInfo.displayName || fileInfo.name, fileInfo.mimeType);
      } else {
        // Legacy format (direct blob)
        blob = response;
        filename = generateSafeFilename(fileInfo.displayName || fileInfo.name, fileInfo.mimeType);
      }

      // Download the file with correct filename
      downloadBlobWithFilename(blob, filename);

    } catch (error) {
      console.error('Failed to download file:', error);
      throw new Error('Failed to download file. Please try again.');
    }
  }

  /**
   * Delete file (move to recycle bin by default)
   */
  async delete(id: string, permanent: boolean = false): Promise<void> {
    const params = new URLSearchParams();
    if (permanent) {
      params.append('permanent', 'true');
    }

    return this.apiClient.delete<void>(`/files/${id}?${params.toString()}`);
  }

  /**
   * Bulk delete files
   */
  async bulkDelete(ids: string[], permanent: boolean = false): Promise<BulkDeleteResponse> {
    return this.apiClient.post<BulkDeleteResponse>('/files/bulk-delete', {
      fileIds: ids,
      permanent
    });
  }

  /**
   * Copy file to another location
   */
  async copy(id: string, options: FileCopyOptions): Promise<FileDto> {
    return this.apiClient.post<FileDto>(`/files/${id}/copy`, options);
  }

  /**
   * Move file to another folder
   */
  async move(id: string, options: FileMoveOptions): Promise<FileDto> {
    return this.apiClient.post<FileDto>(`/files/${id}/move`, options);
  }

  /**
   * Create a shareable link for the file
   */
  async createShare(id: string, options: ShareOptions): Promise<ShareDto> {
    return this.apiClient.post<ShareDto>(`/files/${id}/shares`, options);
  }

  /**
   * Get all shares for a file
   */
  async getShares(id: string): Promise<ShareDto[]> {
    return this.apiClient.get<ShareDto[]>(`/files/${id}/shares`);
  }

  /**
   * Delete a file share
   */
  async deleteShare(fileId: string, shareId: string): Promise<void> {
    return this.apiClient.delete<void>(`/files/${fileId}/shares/${shareId}`);
  }

  /**
   * Grant permission to a user or role for the file
   */
  async grantPermission(id: string, request: PermissionRequest): Promise<string> {
    const response = await this.apiClient.post<{ id: string }>(`/files/${id}/permissions`, request);
    return response.id;
  }

  /**
   * Get all permissions for a file
   */
  async getPermissions(id: string): Promise<PermissionDto[]> {
    return this.apiClient.get<PermissionDto[]>(`/files/${id}/permissions`);
  }

  /**
   * Remove permission from a file
   */
  async removePermission(fileId: string, permissionId: string): Promise<void> {
    return this.apiClient.delete<void>(`/files/${fileId}/permissions/${permissionId}`);
  }

  /**
   * Get file version history
   */
  async getVersionHistory(id: string): Promise<FileDto[]> {
    return this.apiClient.get<FileDto[]>(`/files/${id}/versions`);
  }

  /**
   * Restore file to a specific version
   */
  async restoreVersion(id: string, versionNumber: number): Promise<FileDto> {
    return this.apiClient.post<FileDto>(`/files/${id}/versions/${versionNumber}/restore`);
  }

  /**
   * Get file preview URL
   */
  async getPreview(id: string, width?: number, height?: number): Promise<{ url: string }> {
    const params = new URLSearchParams();
    if (width) params.append('width', width.toString());
    if (height) params.append('height', height.toString());

    return this.apiClient.get<{ url: string }>(`/files/${id}/preview?${params.toString()}`);
  }

  /**
   * Search files across all accessible folders
   */
  async search(query: string, options: Omit<FileListOptions, 'search'> = {}): Promise<FileListResponse> {
    return this.getList({ ...options, search: query });
  }

  /**
   * Get file statistics for the current user
   */
  async getStatistics(): Promise<{
    totalFiles: number;
    totalSize: number;
    filesByType: { mimeType: string; count: number; size: number }[];
    recentActivity: { date: string; uploads: number; downloads: number }[];
  }> {
    return this.apiClient.get('/files/statistics');
  }

  /**
   * Add tags to a file
   */
  async addTags(id: string, tags: string[]): Promise<FileDto> {
    return this.apiClient.post<FileDto>(`/files/${id}/tags`, { tags });
  }

  /**
   * Remove tags from a file
   */
  async removeTags(id: string, tags: string[]): Promise<FileDto> {
    return this.apiClient.delete<FileDto>(`/files/${id}/tags`, { data: { tags } });
  }

  /**
   * Get files by tags
   */
  async getByTags(tags: string[], options: Omit<FileListOptions, 'search'> = {}): Promise<FileListResponse> {
    const params = new URLSearchParams();
    tags.forEach(tag => params.append('tags', tag));
    
    // Add other options
    if (options.parentFolderId) params.append('parentFolderId', options.parentFolderId);
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);

    return this.apiClient.get<FileListResponse>(`/files/by-tags?${params.toString()}`);
  }

  /**
   * Archive a file (only owner can perform this action)
   */
  async archive(id: string): Promise<boolean> {
    const response = await this.apiClient.post<{ data: boolean }>(`/files/${id}/archive`);
    return response.data;
  }

  /**
   * Unarchive a file (restore from archived state)
   */
  async unarchive(id: string): Promise<boolean> {
    const response = await this.apiClient.post<{ data: boolean }>(`/files/${id}/unarchive`);
    return response.data;
  }
} 