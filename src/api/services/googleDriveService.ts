import { ApiClient } from '../core/apiClient';
import {
  FileDto,
  ErrorCode
} from '../types/interfaces';
import { ValidationApiError, ApiError } from '../core/apiClient';

export interface GoogleDriveAuthStatus {
  isAuthenticated: boolean;
  userEmail?: string;
  userName?: string;
  authenticatedAt?: string;
  tokenExpiresAt?: string;
  scopes: string[];
  authUrl?: string;
}

export interface GoogleDriveFile {
  id: string;
  name: string;
  mimeType: string;
  size?: number;
  createdTime: string;
  modifiedTime: string;
  parents?: string[];
  webViewLink?: string;
  webContentLink?: string;
  thumbnailLink?: string;
  isFolder: boolean;
  capabilities?: {
    canEdit: boolean;
    canDownload: boolean;
    canDelete: boolean;
    canShare: boolean;
  };
}

export interface GoogleDriveFolder {
  id: string;
  name: string;
  createdTime: string;
  modifiedTime: string;
  parents?: string[];
  webViewLink?: string;
  childFolderCount?: number;
  childFileCount?: number;
}

export interface GoogleDriveQuotaInfo {
  limit: number;
  usage: number;
  usageInDrive: number;
  usageInDriveTrash: number;
  available: number;
  percentageUsed: number;
}

export interface GoogleDriveMoveRequest {
  fileId: string;
  direction: 'to-gdrive' | 'from-gdrive';
  targetFolderId?: string;
}

export interface GoogleDriveUploadOptions {
  parentFolderId?: string;
  description?: string;
  convert?: boolean; // Convert to Google Workspace format
  keepRevisionForever?: boolean;
}

export interface GoogleDriveSearchOptions {
  query?: string;
  mimeType?: string;
  parentFolderId?: string;
  maxResults?: number;
  orderBy?: 'name' | 'modifiedTime' | 'createdTime' | 'size';
  orderDirection?: 'asc' | 'desc';
  includeShared?: boolean;
  includeTeamDrives?: boolean;
}

export interface GoogleDriveDownloadOptions {
  mimeType?: string; // For Google Workspace documents
  format?: 'pdf' | 'docx' | 'xlsx' | 'pptx' | 'odt' | 'ods' | 'odp';
}

export class GoogleDriveService {
  private client: ApiClient;

  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }

  /**
   * Get Google Drive authentication status
   * @returns Authentication status information
   */
  async getAuthStatus(): Promise<GoogleDriveAuthStatus> {
    try {
      return await this.client.get<GoogleDriveAuthStatus>('/google-drive/auth/status');
    } catch (error) {
      throw this.handleAuthError(error as ApiError);
    }
  }

  /**
   * Initiate Google Drive authentication
   * @param redirectUrl Optional redirect URL after authentication
   * @returns Authentication URL and state
   */
  async initiateAuth(redirectUrl?: string): Promise<{
    authUrl: string;
    state: string;
    expiresAt: string;
  }> {
    const params: Record<string, any> = {};
    if (redirectUrl) params.redirectUrl = redirectUrl;

    try {
      return await this.client.post('/google-drive/auth/initiate', {}, { params });
    } catch (error) {
      throw this.handleAuthError(error as ApiError);
    }
  }

  /**
   * Complete Google Drive authentication with authorization code
   * @param code Authorization code from Google
   * @param state State parameter for validation
   * @returns Authentication result
   */
  async completeAuth(code: string, state: string): Promise<{
    success: boolean;
    userInfo: {
      email: string;
      name: string;
    };
    message: string;
  }> {
    this.validateAuthCode(code);
    this.validateState(state);

    try {
      return await this.client.post('/google-drive/auth/complete', { code, state });
    } catch (error) {
      throw this.handleAuthCompleteError(error as ApiError);
    }
  }

  /**
   * Revoke Google Drive authentication
   * @returns Revocation result
   */
  async revokeAuth(): Promise<{ success: boolean; message: string }> {
    try {
      return await this.client.post('/google-drive/auth/revoke');
    } catch (error) {
      throw this.handleAuthError(error as ApiError);
    }
  }

  /**
   * Get files from Google Drive
   * @param options Search and filter options
   * @returns List of Google Drive files
   */
  async getFiles(options?: GoogleDriveSearchOptions): Promise<{
    files: GoogleDriveFile[];
    totalCount: number;
    nextPageToken?: string;
  }> {
    const params: Record<string, any> = {};

    if (options?.query) params.query = options.query;
    if (options?.mimeType) params.mimeType = options.mimeType;
    if (options?.parentFolderId) params.parentFolderId = options.parentFolderId;
    if (options?.maxResults) params.maxResults = options.maxResults;
    if (options?.orderBy) params.orderBy = options.orderBy;
    if (options?.orderDirection) params.orderDirection = options.orderDirection;
    if (options?.includeShared !== undefined) params.includeShared = options.includeShared;
    if (options?.includeTeamDrives !== undefined) params.includeTeamDrives = options.includeTeamDrives;

    try {
      return await this.client.get('/google-drive/files', { params });
    } catch (error) {
      throw this.handleGoogleDriveError(error as ApiError);
    }
  }

  /**
   * Get folders from Google Drive
   * @param parentFolderId Optional parent folder ID
   * @param maxResults Maximum number of results
   * @returns List of Google Drive folders
   */
  async getFolders(parentFolderId?: string, maxResults?: number): Promise<{
    folders: GoogleDriveFolder[];
    totalCount: number;
  }> {
    const params: Record<string, any> = {};
    if (parentFolderId) params.parentFolderId = parentFolderId;
    if (maxResults) params.maxResults = maxResults;

    try {
      return await this.client.get('/google-drive/folders', { params });
    } catch (error) {
      throw this.handleGoogleDriveError(error as ApiError);
    }
  }

  /**
   * Move file between local storage and Google Drive
   * @param request Move request
   * @returns Move operation result
   */
  async moveFile(request: GoogleDriveMoveRequest): Promise<{
    success: boolean;
    message: string;
    newFileId?: string;
    newLocation?: string;
  }> {
    this.validateMoveRequest(request);

    try {
      return await this.client.post('/google-drive/move', request);
    } catch (error) {
      throw this.handleMoveError(error as ApiError);
    }
  }

  /**
   * Upload file to Google Drive
   * @param file File to upload
   * @param options Upload options
   * @returns Upload result
   */
  async uploadFile(file: File, options?: GoogleDriveUploadOptions): Promise<{
    success: boolean;
    googleDriveFile: GoogleDriveFile;
    message: string;
  }> {
    this.validateFile(file);

    const formData = new FormData();
    formData.append('file', file);

    if (options?.parentFolderId) formData.append('parentFolderId', options.parentFolderId);
    if (options?.description) formData.append('description', options.description);
    if (options?.convert !== undefined) formData.append('convert', String(options.convert));
    if (options?.keepRevisionForever !== undefined) formData.append('keepRevisionForever', String(options.keepRevisionForever));

    try {
      return await this.client.post('/google-drive/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    } catch (error) {
      throw this.handleUploadError(error as ApiError);
    }
  }

  /**
   * Download file from Google Drive
   * @param fileId Google Drive file ID
   * @param options Download options
   * @returns File blob or download URL
   */
  async downloadFile(fileId: string, options?: GoogleDriveDownloadOptions): Promise<Blob> {
    this.validateId(fileId, 'fileId');

    const params: Record<string, any> = {};
    if (options?.mimeType) params.mimeType = options.mimeType;
    if (options?.format) params.format = options.format;

    try {
      const response = await this.client.downloadFile(`/google-drive/files/${fileId}/download`, { params });
      return 'blob' in response ? response.blob : response;
    } catch (error) {
      throw this.handleDownloadError(error as ApiError);
    }
  }

  /**
   * Delete file from Google Drive
   * @param fileId Google Drive file ID
   * @param permanent Whether to permanently delete (true) or move to trash (false)
   * @returns Delete result
   */
  async deleteFile(fileId: string, permanent: boolean = false): Promise<{
    success: boolean;
    message: string;
  }> {
    this.validateId(fileId, 'fileId');

    try {
      return await this.client.delete(`/google-drive/files/${fileId}`, {
        params: { permanent }
      });
    } catch (error) {
      throw this.handleDeleteError(error as ApiError);
    }
  }

  /**
   * Create folder in Google Drive
   * @param name Folder name
   * @param parentFolderId Optional parent folder ID
   * @returns Created folder information
   */
  async createFolder(name: string, parentFolderId?: string): Promise<{
    success: boolean;
    folder: GoogleDriveFolder;
    message: string;
  }> {
    this.validateFolderName(name);

    const request = {
      name,
      ...(parentFolderId && { parentFolderId })
    };

    try {
      return await this.client.post('/google-drive/folders', request);
    } catch (error) {
      throw this.handleCreateFolderError(error as ApiError);
    }
  }

  /**
   * Get Google Drive quota information
   * @returns Quota and storage usage information
   */
  async getQuotaInfo(): Promise<GoogleDriveQuotaInfo> {
    try {
      return await this.client.get<GoogleDriveQuotaInfo>('/google-drive/quota');
    } catch (error) {
      throw this.handleGoogleDriveError(error as ApiError);
    }
  }

  /**
   * Search files in Google Drive
   * @param query Search query
   * @param options Search options
   * @returns Search results
   */
  async searchFiles(query: string, options?: Omit<GoogleDriveSearchOptions, 'query'>): Promise<{
    files: GoogleDriveFile[];
    folders: GoogleDriveFolder[];
    totalCount: number;
    searchQuery: string;
  }> {
    if (!query || query.trim().length === 0) {
      throw new ValidationApiError('Search query is required', [
        { field: 'query', message: 'Search query cannot be empty', code: 'REQUIRED' }
      ]);
    }

    const params: Record<string, any> = { query };

    if (options?.mimeType) params.mimeType = options.mimeType;
    if (options?.parentFolderId) params.parentFolderId = options.parentFolderId;
    if (options?.maxResults) params.maxResults = options.maxResults;
    if (options?.orderBy) params.orderBy = options.orderBy;
    if (options?.orderDirection) params.orderDirection = options.orderDirection;
    if (options?.includeShared !== undefined) params.includeShared = options.includeShared;
    if (options?.includeTeamDrives !== undefined) params.includeTeamDrives = options.includeTeamDrives;

    try {
      return await this.client.get('/google-drive/search', { params });
    } catch (error) {
      throw this.handleSearchError(error as ApiError);
    }
  }

  /**
   * Get file or folder details from Google Drive
   * @param fileId Google Drive file/folder ID
   * @returns File or folder details
   */
  async getFileDetails(fileId: string): Promise<GoogleDriveFile> {
    this.validateId(fileId, 'fileId');

    try {
      return await this.client.get<GoogleDriveFile>(`/google-drive/files/${fileId}`);
    } catch (error) {
      throw this.handleGoogleDriveError(error as ApiError);
    }
  }

  /**
   * Share file or folder in Google Drive
   * @param fileId Google Drive file/folder ID
   * @param options Sharing options
   * @returns Sharing result
   */
  async shareFile(fileId: string, options: {
    email?: string;
    role: 'reader' | 'writer' | 'commenter' | 'owner';
    type: 'user' | 'group' | 'domain' | 'anyone';
    allowFileDiscovery?: boolean;
    sendNotificationEmail?: boolean;
    message?: string;
  }): Promise<{
    success: boolean;
    permissionId: string;
    message: string;
  }> {
    this.validateId(fileId, 'fileId');
    this.validateShareOptions(options);

    try {
      return await this.client.post(`/google-drive/files/${fileId}/share`, options);
    } catch (error) {
      throw this.handleShareError(error as ApiError);
    }
  }

  /**
   * Copy file in Google Drive
   * @param fileId Source file ID
   * @param name New file name
   * @param parentFolderId Optional parent folder for the copy
   * @returns Copy result
   */
  async copyFile(fileId: string, name: string, parentFolderId?: string): Promise<{
    success: boolean;
    copiedFile: GoogleDriveFile;
    message: string;
  }> {
    this.validateId(fileId, 'fileId');
    this.validateFileName(name);

    const request = {
      name,
      ...(parentFolderId && { parentFolderId })
    };

    try {
      return await this.client.post(`/google-drive/files/${fileId}/copy`, request);
    } catch (error) {
      throw this.handleCopyError(error as ApiError);
    }
  }

  // Validation methods

  private validateId(id: string, fieldName: string): void {
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new ValidationApiError(`Invalid ${fieldName}`, [
        { field: fieldName, message: `${fieldName} is required and must be a non-empty string`, code: 'REQUIRED' }
      ]);
    }
  }

  private validateAuthCode(code: string): void {
    if (!code || typeof code !== 'string' || code.trim().length === 0) {
      throw new ValidationApiError('Authorization code is required', [
        { field: 'code', message: 'Authorization code must be a non-empty string', code: 'REQUIRED' }
      ]);
    }
  }

  private validateState(state: string): void {
    if (!state || typeof state !== 'string' || state.trim().length === 0) {
      throw new ValidationApiError('State parameter is required', [
        { field: 'state', message: 'State parameter must be a non-empty string', code: 'REQUIRED' }
      ]);
    }
  }

  private validateMoveRequest(request: GoogleDriveMoveRequest): void {
    if (!request) {
      throw new ValidationApiError('Move request is required', [
        { field: 'request', message: 'Request data is required', code: 'REQUIRED' }
      ]);
    }

    this.validateId(request.fileId, 'fileId');

    if (!request.direction || !['to-gdrive', 'from-gdrive'].includes(request.direction)) {
      throw new ValidationApiError('Invalid direction', [
        { field: 'direction', message: 'Direction must be either "to-gdrive" or "from-gdrive"', code: 'INVALID_VALUE' }
      ]);
    }

    if (request.targetFolderId) {
      this.validateId(request.targetFolderId, 'targetFolderId');
    }
  }

  private validateFile(file: File): void {
    if (!file || !(file instanceof File)) {
      throw new ValidationApiError('Invalid file', [
        { field: 'file', message: 'Must be a valid File object', code: 'INVALID_TYPE' }
      ]);
    }

    if (file.size === 0) {
      throw new ValidationApiError('Empty file not allowed', [
        { field: 'file', message: 'File cannot be empty', code: 'EMPTY_FILE' }
      ]);
    }

    // Google Drive file size limit (5TB)
    const maxSize = 5 * 1024 * 1024 * 1024 * 1024; // 5TB
    if (file.size > maxSize) {
      throw new ValidationApiError('File too large', [
        { field: 'file', message: 'File size cannot exceed 5TB for Google Drive upload', code: ErrorCode.FILE_TOO_LARGE }
      ]);
    }
  }

  private validateFolderName(name: string): void {
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      throw new ValidationApiError('Folder name is required', [
        { field: 'name', message: 'Folder name must be a non-empty string', code: 'REQUIRED' }
      ]);
    }

    if (name.length > 255) {
      throw new ValidationApiError('Folder name too long', [
        { field: 'name', message: 'Folder name cannot exceed 255 characters', code: 'TOO_LONG' }
      ]);
    }

    // Google Drive doesn't allow certain characters
    const invalidChars = /[\\\/\:\*\?\"\<\>\|]/;
    if (invalidChars.test(name)) {
      throw new ValidationApiError('Invalid folder name', [
        { field: 'name', message: 'Folder name contains invalid characters', code: 'INVALID_FORMAT' }
      ]);
    }
  }

  private validateFileName(name: string): void {
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      throw new ValidationApiError('File name is required', [
        { field: 'name', message: 'File name must be a non-empty string', code: 'REQUIRED' }
      ]);
    }

    if (name.length > 255) {
      throw new ValidationApiError('File name too long', [
        { field: 'name', message: 'File name cannot exceed 255 characters', code: 'TOO_LONG' }
      ]);
    }
  }

  private validateShareOptions(options: any): void {
    if (!options) {
      throw new ValidationApiError('Share options are required', [
        { field: 'options', message: 'Share options must be provided', code: 'REQUIRED' }
      ]);
    }

    if (!options.role || !['reader', 'writer', 'commenter', 'owner'].includes(options.role)) {
      throw new ValidationApiError('Invalid role', [
        { field: 'role', message: 'Role must be one of: reader, writer, commenter, owner', code: 'INVALID_VALUE' }
      ]);
    }

    if (!options.type || !['user', 'group', 'domain', 'anyone'].includes(options.type)) {
      throw new ValidationApiError('Invalid type', [
        { field: 'type', message: 'Type must be one of: user, group, domain, anyone', code: 'INVALID_VALUE' }
      ]);
    }

    if (['user', 'group'].includes(options.type) && !options.email) {
      throw new ValidationApiError('Email required for user/group sharing', [
        { field: 'email', message: 'Email is required when sharing with user or group', code: 'REQUIRED' }
      ]);
    }
  }

  // Error handling methods

  private handleAuthError(error: ApiError): ApiError {
    if (error.statusCode === 401) {
      return new ApiError('Google Drive authentication failed', 401, ErrorCode.SYNC_AUTHENTICATION_FAILED, error.correlationId);
    }
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions for Google Drive access', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleAuthCompleteError(error: ApiError): ApiError {
    if (error.statusCode === 400) {
      return new ApiError('Invalid authorization code or state', 400, 'INVALID_AUTH_CODE', error.correlationId);
    }
    if (error.statusCode === 401) {
      return new ApiError('Google Drive authentication failed', 401, ErrorCode.SYNC_AUTHENTICATION_FAILED, error.correlationId);
    }
    return error;
  }

  private handleGoogleDriveError(error: ApiError): ApiError {
    if (error.statusCode === 401) {
      return new ApiError('Google Drive authentication required', 401, ErrorCode.SYNC_AUTHENTICATION_FAILED, error.correlationId);
    }
    if (error.statusCode === 403) {
      return new ApiError('Insufficient Google Drive permissions', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 404) {
      return new ApiError('File or folder not found in Google Drive', 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    if (error.statusCode === 429) {
      return new ApiError('Google Drive API rate limit exceeded', 429, ErrorCode.RATE_LIMIT_EXCEEDED, error.correlationId);
    }
    if (error.statusCode === 500) {
      return new ApiError('Google Drive API error', 500, 'GOOGLE_DRIVE_API_ERROR', error.correlationId);
    }
    return error;
  }

  private handleMoveError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('File move conflict - file already exists at destination', 409, 'MOVE_CONFLICT', error.correlationId);
    }
    if (error.statusCode === 507) {
      return new ApiError('Insufficient storage space in destination', 507, ErrorCode.STORAGE_QUOTA_EXCEEDED, error.correlationId);
    }
    return this.handleGoogleDriveError(error);
  }

  private handleUploadError(error: ApiError): ApiError {
    if (error.statusCode === 413) {
      return new ApiError('File too large for Google Drive upload', 413, ErrorCode.FILE_TOO_LARGE, error.correlationId);
    }
    if (error.statusCode === 507) {
      return new ApiError('Insufficient Google Drive storage space', 507, ErrorCode.STORAGE_QUOTA_EXCEEDED, error.correlationId);
    }
    return this.handleGoogleDriveError(error);
  }

  private handleDownloadError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('File download not allowed by Google Drive permissions', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleGoogleDriveError(error);
  }

  private handleDeleteError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to delete file from Google Drive', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleGoogleDriveError(error);
  }

  private handleCreateFolderError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Folder already exists in Google Drive', 409, 'FOLDER_EXISTS', error.correlationId);
    }
    if (error.statusCode === 507) {
      return new ApiError('Insufficient Google Drive storage space', 507, ErrorCode.STORAGE_QUOTA_EXCEEDED, error.correlationId);
    }
    return this.handleGoogleDriveError(error);
  }

  private handleSearchError(error: ApiError): ApiError {
    if (error.statusCode === 400) {
      return new ApiError('Invalid Google Drive search query', 400, 'INVALID_SEARCH_QUERY', error.correlationId);
    }
    return this.handleGoogleDriveError(error);
  }

  private handleShareError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to share file in Google Drive', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 400) {
      return new ApiError('Invalid sharing parameters', 400, 'INVALID_SHARE_PARAMS', error.correlationId);
    }
    return this.handleGoogleDriveError(error);
  }

  private handleCopyError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to copy file in Google Drive', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 507) {
      return new ApiError('Insufficient Google Drive storage space for copy', 507, ErrorCode.STORAGE_QUOTA_EXCEEDED, error.correlationId);
    }
    return this.handleGoogleDriveError(error);
  }
} 