import { ApiClient } from '../core/apiClient';
import { HealthCheckResponse } from '../types/interfaces';
import { ApiError } from '../core/apiClient';

export class HealthService {
  private client: ApiClient;

  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }

  /**
   * Basic health check endpoint
   * @returns True if the service is healthy
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.get('/health-check');
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Detailed health check with service status
   * @returns Comprehensive health information
   */
  async getDetailedHealth(): Promise<HealthCheckResponse> {
    try {
      return await this.client.get<HealthCheckResponse>('/health');
    } catch (error) {
      throw new ApiError(
        'Failed to retrieve health information',
        (error as any).statusCode || 500,
        'HEALTH_CHECK_FAILED',
        (error as any).correlationId
      );
    }
  }

  /**
   * Check if a specific service is operational
   * @param serviceName Name of the service to check
   * @returns Service status
   */
  async checkService(serviceName: 'database' | 'storage' | 'sync'): Promise<{
    service: string;
    status: 'up' | 'down';
    lastChecked: string;
    responseTime?: number;
  }> {
    try {
      return await this.client.get(`/health/services/${serviceName}`);
    } catch (error) {
      throw new ApiError(
        `Failed to check ${serviceName} service health`,
        (error as any).statusCode || 500,
        'SERVICE_CHECK_FAILED',
        (error as any).correlationId
      );
    }
  }

  /**
   * Get system metrics and performance data
   * @returns System performance metrics
   */
  async getSystemMetrics(): Promise<{
    uptime: number;
    memoryUsage: {
      used: number;
      total: number;
      percentage: number;
    };
    cpuUsage: number;
    activeConnections: number;
    requestsPerMinute: number;
    errorRate: number;
  }> {
    try {
      return await this.client.get('/health/metrics');
    } catch (error) {
      throw new ApiError(
        'Failed to retrieve system metrics',
        (error as any).statusCode || 500,
        'METRICS_FAILED',
        (error as any).correlationId
      );
    }
  }
}
