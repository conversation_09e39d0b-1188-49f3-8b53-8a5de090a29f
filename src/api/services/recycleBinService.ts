import { ApiClient } from '../core/apiClient';
import { 
  SortDirection, 
  PaginationInfo, 
  DeletedItemType, 
  RecycleBinApiDeletedItem,
  RecycleBinResponse,
  RecycleBinStatistics as IRecycleBinStatistics,
  FolderDto,
  FileDto
} from '../types/interfaces';

export interface DeletedItemDto {
  id: string;
  originalName: string;
  originalPath?: string;
  itemType: DeletedItemType;
  deletedAt: string;
  deletedBy: string;
  deletedByName?: string;
  expiresAt?: string;
  size?: number;
  mimeType?: string;
  canRestore: boolean;
}

export interface RecycleBinStatistics extends IRecycleBinStatistics {}

export interface EmptyRecycleBinResult {
  deletedItems: number;
  freedSpace: number;
  message: string;
}

export interface GetDeletedItemsOptions {
  page?: number;
  pageSize?: number;
  itemType?: DeletedItemType;
  search?: string;
  uploaderEmail?: string;
  deletedAfter?: string;
  deletedBefore?: string;
  onlyRestorable?: boolean;
  sortBy?: string;
  sortDirection?: SortDirection;
}

export interface RestoreItemOptions {
  newParentFolderId?: string;
}

// RecycleBin item interface for components (simplified from API response)
export interface RecycleBinItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  originalPath: string;
  deletedAt: string;
  deletedBy: string;
  expiresAt: string;
  size?: number;
  mimeType?: string;
}

export class RecycleBinService {
  constructor(private apiClient: ApiClient) {}

  /**
   * Get all deleted items from recycle bin (unified endpoint)
   */
  async getDeletedItems(options: GetDeletedItemsOptions = {}): Promise<{
    items: DeletedItemDto[];
    totalCount: number;
    totalPages: number;
    page: number;
    pageSize: number;
  }> {
    const params = new URLSearchParams();
    
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.itemType !== undefined) params.append('itemType', options.itemType.toString());
    if (options.search) params.append('search', options.search);
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);

    try {
      const response = await this.apiClient.get<RecycleBinResponse>(`/recycle-bin?${params.toString()}`);
      
      // Transform API response to match component expectations
      const transformedItems: DeletedItemDto[] = response.data.items.map((item: RecycleBinApiDeletedItem) => ({
        id: item.id,
        originalName: item.originalName,
        originalPath: item.originalPath,
        itemType: item.itemType,
        deletedAt: item.deletedAt,
        deletedBy: item.deletedBy,
        deletedByName: item.deletedByEmail || '',
        expiresAt: new Date(Date.now() + item.daysRemaining * 24 * 60 * 60 * 1000).toISOString(),
        size: item.originalSize || undefined,
        mimeType: item.originalContentType || undefined,
        canRestore: item.canRestore
      }));

      return {
        items: transformedItems,
        totalCount: response.data.totalCount,
        totalPages: response.data.totalPages,
        page: response.data.page,
        pageSize: response.data.pageSize
      };
    } catch (error: any) {
      if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to view recycle bin');
      }
      throw error;
    }
  }

  /**
   * Get deleted folders from recycle bin
   */
  async getFolders(options: {
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortDirection?: SortDirection;
  } = {}): Promise<{ items: FolderDto[]; pagination: PaginationInfo }> {
    const params = new URLSearchParams();
    
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);

    try {
      return await this.apiClient.get<{ items: FolderDto[]; pagination: PaginationInfo }>(`/recycle-bin/folders?${params.toString()}`);
    } catch (error: any) {
      if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to view recycle bin');
      }
      throw error;
    }
  }

  /**
   * Get recycle bin statistics
   */
  async getStatistics(): Promise<RecycleBinStatistics> {
    try {
      return await this.apiClient.get<RecycleBinStatistics>('/recycle-bin/statistics');
    } catch (error: any) {
      if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to view recycle bin statistics');
      }
      throw error;
    }
  }

  /**
   * Restore a single item (folder or file)
   */
  async restoreItem(id: string, options?: RestoreItemOptions): Promise<void> {
    try {
      await this.apiClient.post<void>(`/recycle-bin/${id}/restore`, options || {});
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('Item not found in recycle bin');
      } else if (error.statusCode === 400) {
        if (error.message?.includes('name conflict')) {
          throw new Error('An item with this name already exists in the target location');
        } else if (error.message?.includes('parent folder')) {
          throw new Error('The specified parent folder does not exist or is inaccessible');
        }
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to restore this item');
      }
      throw error;
    }
  }

  /**
   * Permanently delete a single item
   */
  async permanentlyDeleteItem(id: string): Promise<void> {
    try {
      await this.apiClient.delete<void>(`/recycle-bin/${id}/permanent`);
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('Item not found in recycle bin');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to permanently delete this item');
      }
      throw error;
    }
  }

  /**
   * Empty the entire recycle bin (DANGEROUS OPERATION)
   */
  async emptyRecycleBin(): Promise<EmptyRecycleBinResult> {
    try {
      return await this.apiClient.delete<EmptyRecycleBinResult>('/recycle-bin/empty');
    } catch (error: any) {
      if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to empty recycle bin. This operation requires admin privileges.');
      }
      throw error;
    }
  }

  /**
   * Restore folder from recycle bin
   */
  

  /**
   * Restore file from recycle bin
   */

  /**
   * Permanently delete folder from recycle bin
   */
  

  /**
   * Permanently delete file from recycle bin
   */

  /**
   * Bulk restore items from recycle bin
   */
  async bulkRestore(items: { 
    folders: string[], 
    files: string[] 
  }): Promise<{ 
    restoredFolders: FolderDto[], 
    restoredFiles: FileDto[], 
    errors: Array<{ id: string; type: 'folder' | 'file'; error: string }> 
  }> {
    try {
      return await this.apiClient.post<any>('/recycle-bin/bulk-restore', items);
    } catch (error: any) {
      if (error.statusCode === 400) {
        throw new Error('Invalid bulk restore request. Please check your selection.');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions for bulk restore operations');
      }
      throw error;
    }
  }

  /**
   * Bulk permanent delete items from recycle bin  
   */
  async bulkPermanentDelete(items: { 
    folders: string[], 
    files: string[] 
  }): Promise<{ 
    deletedFolders: number, 
    deletedFiles: number, 
    errors: Array<{ id: string; type: 'folder' | 'file'; error: string }> 
  }> {
    try {
      return await this.apiClient.post<any>('/recycle-bin/bulk-delete', items);
    } catch (error: any) {
      if (error.statusCode === 400) {
        throw new Error('Invalid bulk delete request. Please check your selection.');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions for bulk delete operations');
      }
      throw error;
    }
  }
}

// Create and export default instance
const apiClient = new ApiClient();
export const recycleBinService = new RecycleBinService(apiClient);
export default recycleBinService; 