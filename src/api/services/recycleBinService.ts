import { ApiClient } from '../core/apiClient';
import { FolderDto, FileDto, SortDirection, PaginationInfo } from '../types/interfaces';

export interface RecycleBinItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  originalPath: string;
  deletedAt: string;
  deletedBy: string;
  expiresAt: string;
  size?: number;
  mimeType?: string;
}

export class RecycleBinService {
  constructor(private apiClient: ApiClient) {}

  /**
   * Get deleted folders from recycle bin
   */
  async getFolders(options: {
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortDirection?: SortDirection;
  } = {}): Promise<{ items: FolderDto[]; pagination: PaginationInfo }> {
    const params = new URLSearchParams();
    
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);

    try {
      return await this.apiClient.get<{ items: FolderDto[]; pagination: PaginationInfo }>(`/folders/recycle-bin?${params.toString()}`);
    } catch (error: any) {
      if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to view recycle bin');
      }
      throw error;
    }
  }

  /**
   * Get deleted files from recycle bin  
   */
  async getFiles(options: {
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortDirection?: SortDirection;
  } = {}): Promise<{ items: FileDto[]; pagination: PaginationInfo }> {
    const params = new URLSearchParams();
    
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);

    try {
      return await this.apiClient.get<{ items: FileDto[]; pagination: PaginationInfo }>(`/files/recycle-bin?${params.toString()}`);
    } catch (error: any) {
      if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to view recycle bin');
      }
      throw error;
    }
  }

  /**
   * Restore folder from recycle bin
   */
  async restoreFolder(id: string, options?: { newParentFolderId?: string; newName?: string }): Promise<FolderDto> {
    try {
      return await this.apiClient.post<FolderDto>(`/folders/${id}/restore`, options || {});
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('Folder not found in recycle bin');
      } else if (error.statusCode === 400) {
        if (error.message?.includes('name conflict')) {
          throw new Error('A folder with this name already exists in the target location');
        } else if (error.message?.includes('parent folder')) {
          throw new Error('The specified parent folder does not exist or is inaccessible');
        }
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to restore this folder');
      }
      throw error;
    }
  }

  /**
   * Restore file from recycle bin
   */
  async restoreFile(id: string, options?: { newParentFolderId?: string; newName?: string }): Promise<FileDto> {
    try {
      return await this.apiClient.post<FileDto>(`/files/${id}/restore`, options || {});
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('File not found in recycle bin');
      } else if (error.statusCode === 400) {
        if (error.message?.includes('name conflict')) {
          throw new Error('A file with this name already exists in the target location');
        } else if (error.message?.includes('parent folder')) {
          throw new Error('The specified parent folder does not exist or is inaccessible');
        }
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to restore this file');
      }
      throw error;
    }
  }

  /**
   * Permanently delete folder from recycle bin
   */
  async permanentDeleteFolder(id: string): Promise<void> {
    try {
      return await this.apiClient.delete<void>(`/folders/${id}/permanent`);
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('Folder not found in recycle bin');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to permanently delete this folder');
      }
      throw error;
    }
  }

  /**
   * Permanently delete file from recycle bin
   */
  async permanentDeleteFile(id: string): Promise<void> {
    try {
      return await this.apiClient.delete<void>(`/files/${id}/permanent`);
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('File not found in recycle bin');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to permanently delete this file');
      }
      throw error;
    }
  }

  /**
   * Bulk restore items from recycle bin
   */
  async bulkRestore(items: { 
    folders: string[], 
    files: string[] 
  }): Promise<{ 
    restoredFolders: FolderDto[], 
    restoredFiles: FileDto[], 
    errors: Array<{ id: string; type: 'folder' | 'file'; error: string }> 
  }> {
    try {
      return await this.apiClient.post<any>('/recycle-bin/bulk-restore', items);
    } catch (error: any) {
      if (error.statusCode === 400) {
        throw new Error('Invalid bulk restore request. Please check your selection.');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions for bulk restore operations');
      }
      throw error;
    }
  }

  /**
   * Bulk permanent delete items from recycle bin  
   */
  async bulkPermanentDelete(items: { 
    folders: string[], 
    files: string[] 
  }): Promise<{ 
    deletedFolders: number, 
    deletedFiles: number, 
    errors: Array<{ id: string; type: 'folder' | 'file'; error: string }> 
  }> {
    try {
      return await this.apiClient.post<any>('/recycle-bin/bulk-delete', items);
    } catch (error: any) {
      if (error.statusCode === 400) {
        throw new Error('Invalid bulk delete request. Please check your selection.');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions for bulk delete operations');
      }
      throw error;
    }
  }

  /**
   * Empty the entire recycle bin (DANGEROUS OPERATION)
   */
  async emptyRecycleBin(): Promise<{ 
    deletedFolders: number, 
    deletedFiles: number 
  }> {
    try {
      return await this.apiClient.post<any>('/recycle-bin/empty', {});
    } catch (error: any) {
      if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to empty recycle bin. This operation requires admin privileges.');
      }
      throw error;
    }
  }
}

// Create and export default instance
const apiClient = new ApiClient();
export const recycleBinService = new RecycleBinService(apiClient);
export default recycleBinService; 