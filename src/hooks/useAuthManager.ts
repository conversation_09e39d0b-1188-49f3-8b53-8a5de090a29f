import { useCallback, useEffect, useState } from "react";
import { User, UserManager, UserManagerSettings } from "oidc-client-ts";
import { oidcConfig } from "@/lib/oidcConfig";

/**
 * Custom hook for managing OIDC UserManager instance
 * Separates UserManager logic from AuthContext for better testability
 */
export const useAuthManager = () => {
  const [userManager, setUserManager] = useState<UserManager | null>(null);
  const [initError, setInitError] = useState<string | null>(null);

  const initializeUserManager = useCallback(() => {
    if (typeof window === "undefined") return;

    try {
      // Validate configuration
      if (!oidcConfig) {
        throw new Error(
          "Invalid OIDC configuration. Please check your environment variables."
        );
      }

      const manager = new UserManager(oidcConfig as UserManagerSettings);
      setUserManager(manager);
      setInitError(null);

      return manager;
    } catch (error) {
      console.error("Error initializing UserManager:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to initialize authentication";
      setInitError(errorMessage);
      return null;
    }
  }, []);

  useEffect(() => {
    initializeUserManager();
  }, [initializeUserManager]);

  const setupEventHandlers = useCallback(
    (
      manager: UserManager,
      handlers: {
        onUserLoaded?: (user: User) => void;
        onUserUnloaded?: () => void;
        onAccessTokenExpiring?: () => void;
        onAccessTokenExpired?: () => void;
        onSilentRenewError?: (error: Error) => void;
        onUserSignedOut?: () => void;
      }
    ) => {
      if (handlers.onUserLoaded) {
        manager.events.addUserLoaded(handlers.onUserLoaded);
      }
      if (handlers.onUserUnloaded) {
        manager.events.addUserUnloaded(handlers.onUserUnloaded);
      }
      if (handlers.onAccessTokenExpiring) {
        manager.events.addAccessTokenExpiring(handlers.onAccessTokenExpiring);
      }
      if (handlers.onAccessTokenExpired) {
        manager.events.addAccessTokenExpired(handlers.onAccessTokenExpired);
      }
      if (handlers.onSilentRenewError) {
        manager.events.addSilentRenewError(handlers.onSilentRenewError);
      }
      if (handlers.onUserSignedOut) {
        manager.events.addUserSignedOut(handlers.onUserSignedOut);
      }

      // Return cleanup function
      return () => {
        if (handlers.onUserLoaded) {
          manager.events.removeUserLoaded(handlers.onUserLoaded);
        }
        if (handlers.onUserUnloaded) {
          manager.events.removeUserUnloaded(handlers.onUserUnloaded);
        }
        if (handlers.onAccessTokenExpiring) {
          manager.events.removeAccessTokenExpiring(
            handlers.onAccessTokenExpiring
          );
        }
        if (handlers.onAccessTokenExpired) {
          manager.events.removeAccessTokenExpired(
            handlers.onAccessTokenExpired
          );
        }
        if (handlers.onSilentRenewError) {
          manager.events.removeSilentRenewError(handlers.onSilentRenewError);
        }
        if (handlers.onUserSignedOut) {
          manager.events.removeUserSignedOut(handlers.onUserSignedOut);
        }
      };
    },
    []
  );

  return {
    userManager,
    initError,
    setupEventHandlers,
    reinitialize: initializeUserManager,
  };
};
