import { useTheme } from '@/contexts/ThemeContext';
import { theme, getThemeClasses } from '@/lib/utils';

/**
 * Extended theme hook that provides utilities and helpers
 */
export function useExtendedTheme() {
  const themeContext = useTheme();
  
  return {
    ...themeContext,
    
    // Theme utility classes
    classes: theme,
    
    // Get theme pattern classes
    getClasses: getThemeClasses,
    
    // Helper functions
    isDark: themeContext.theme === 'dark',
    isLight: themeContext.theme === 'light',
    
    // CSS custom property values (for inline styles)
    css: {
      background: 'var(--background)',
      surface: 'var(--surface)',
      surfaceElevated: 'var(--surface-elevated)',
      textPrimary: 'var(--text-primary)',
      textSecondary: 'var(--text-secondary)',
      textTertiary: 'var(--text-tertiary)',
      border: 'var(--border)',
      borderLight: 'var(--border-light)',
      primary500: 'var(--primary-500)',
      primary600: 'var(--primary-600)',
    },
  };
}

/**
 * Hook to get theme-aware class names for common patterns
 */
export function useThemeClasses() {
  const { classes, getClasses } = useExtendedTheme();
  
  return {
    // Direct access to utility classes
    ...classes,
    
    // Pattern-based classes
    card: getClasses('card'),
    cardElevated: getClasses('cardElevated'),
    buttonPrimary: getClasses('buttonPrimary'),
    buttonSecondary: getClasses('buttonSecondary'),
    input: getClasses('input'),
    sidebar: getClasses('sidebar'),
    header: getClasses('header'),
    hoverItem: getClasses('hoverItem'),
    activeItem: getClasses('activeItem'),
  };
}

// Re-export the original useTheme for convenience
export { useTheme } from '@/contexts/ThemeContext'; 