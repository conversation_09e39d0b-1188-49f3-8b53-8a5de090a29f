// PDF related types
export interface PDFDocument {
  readonly id: string;
  readonly name: string;
  readonly url: string;
  readonly size: number;
  readonly pageCount: number;
  readonly uploadedAt: Date;
  readonly folderId?: string;
  readonly mimeType?: string;
  readonly checksum?: string;
}

export interface PDFPage {
  readonly pageNumber: number;
  readonly width: number;
  readonly height: number;
  readonly scale: number;
  readonly rotation?: number;
}

export interface PDFRenderOptions {
  readonly scale: number;
  readonly rotation: number;
  readonly enableTextSelection?: boolean;
  readonly enableAnnotations?: boolean;
}

// File management types
export type FileType = "file" | "folder";

export interface BaseFileItem {
  readonly id: string;
  readonly name: string;
  readonly type: FileType;
  readonly parentId?: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly createdBy?: string;
  readonly modifiedBy?: string;
}

export interface FileItem extends BaseFileItem {
  readonly type: "file";
  readonly size: number;
  readonly url?: string;
  readonly mimeType?: string;
  readonly checksum?: string;
}

export interface FolderItem extends BaseFileItem {
  readonly type: "folder";
  readonly children?: readonly (FileItem | FolderItem)[];
  readonly childCount?: number;
}

export type FileSystemItem = FileItem | FolderItem;

// Legacy interface for backward compatibility
export interface Folder {
  readonly type: "folder";
  readonly id: string;
  readonly name: string;
  readonly parentId?: string;
  readonly children: readonly FileItem[];
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

// Job/Task types
export interface Job {
  id: string;
  type: "ocr" | "split" | "upload";
  status: "pending" | "processing" | "completed" | "failed";
  progress: number;
  fileId: string;
  fileName: string;
  createdAt: Date;
  completedAt?: Date;
  error?: string;
  result?: unknown;
}

export interface OCRJob extends Job {
  type: "ocr";
  result?: {
    text: string;
    confidence: number;
    pages: Array<{
      pageNumber: number;
      text: string;
      confidence: number;
    }>;
  };
}

export interface SplitJob extends Job {
  type: "split";
  splitRanges: Array<{
    start: number;
    end: number;
    name: string;
  }>;
  result?: {
    files: Array<{
      name: string;
      url: string;
      pageRange: string;
    }>;
  };
}

// Upload types
export interface UploadProgress {
  fileId: string;
  fileName: string;
  progress: number;
  status: "uploading" | "processing" | "completed" | "error";
  error?: string;
}

// Auth types
export interface AuthState {
  isAuthenticated: boolean;
  accessToken: string | null;
  user?: {
    id: string;
    email: string;
    name: string;
  };
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Viewer types
export interface ViewerState {
  currentPage: number;
  scale: number;
  rotation: number;
  isLoading: boolean;
  error?: string;
}

export interface SplitRange {
  id: string;
  start: number;
  end: number;
  name: string;
  color: string;
}

// Component props types
export interface PdfViewerProps {
  fileUrl: string;
  onPageChange?: (page: number) => void;
  onScaleChange?: (scale: number) => void;
  onTotalPagesChange?: (totalPages: number) => void;
  initialPage?: number;
  initialScale?: number;
}

export interface FileListProps {
  files: FileSystemItem[];
  onFileSelect?: (file: FileSystemItem) => void;
  onFileDelete?: (fileId: string) => void;
  onFolderCreate?: (name: string, parentId?: string) => void;
}

export interface UploadZoneProps {
  onFilesSelected: (files: File[]) => void;
  accept?: string;
  maxSize?: number;
  multiple?: boolean;
  disabled?: boolean;
}
