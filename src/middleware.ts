import { NextResponse } from "next/server";
import { NextRequest } from "next/server";


export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  if (
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/api/") ||
    pathname.includes(".") 
  ) {
    return NextResponse.next();
  }

  const response = NextResponse.next();

  // Add security headers
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

  // Get API base URL from environment
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "http://*************:7040";
  const oidcAuthority = process.env.NEXT_PUBLIC_OIDC_AUTHORITY || "http://localhost:3000";

  // CSP temporarily disabled for development
  // Uncomment below when ready to re-enable CSP
  /*
  response.headers.set(
    "Content-Security-Policy",
    "default-src 'self'; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://static.cloudflareinsights.com; " +
      "style-src 'self' 'unsafe-inline'; " +
      "img-src 'self' data: https:; " +
      `connect-src 'self' https://sso.veasy.vn https://identityapi.veasy.vn https://static.cloudflareinsights.com ${apiBaseUrl} ${oidcAuthority}; ` +
      "frame-src 'self' https://sso.veasy.vn;"
  );
  */

  return response;
}

export const config = {
  matcher: [
   
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
