"use client";

import React, { useState } from 'react';
import { 
  XMarkIcon, 
  FolderIcon, 
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';
import { FolderDto } from '@/api/types/interfaces';

interface CreateFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (name: string, description?: string) => Promise<void>;
  currentFolder?: FolderDto | null;
  loading?: boolean;
}

export function CreateFolderModal({
  isOpen,
  onClose,
  onSubmit,
  currentFolder,
  loading = false
}: CreateFolderModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [errors, setErrors] = useState<{ name?: string; description?: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setFormData({ name: '', description: '' });
      setErrors({});
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const validateForm = () => {
    const newErrors: { name?: string; description?: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Tên folder là bắt buộc';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Tên folder phải có ít nhất 2 ký tự';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Tên folder không được vượt quá 100 ký tự';
    }

    if (formData.description.trim().length > 500) {
      newErrors.description = 'Mô tả không được vượt quá 500 ký tự';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData.name.trim(), formData.description.trim() || undefined);
      onClose();
    } catch (error) {
      console.error('Failed to create folder:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: 'name' | 'description', value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 animate-slide-up">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <FolderIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Tạo Folder Mới
              </h3>
              <p className="text-sm text-gray-500">
                {currentFolder 
                  ? `Trong folder: ${currentFolder.name}` 
                  : 'Trong thư mục gốc'
                }
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Modal Body */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Current Location Info */}
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <FolderIcon className="w-4 h-4 text-blue-600" />
              <span className="text-blue-800 font-medium">Vị trí tạo folder:</span>
            </div>
            <div className="mt-1 text-sm text-blue-700">
              {currentFolder ? (
                <div>
                  <div className="font-medium">{currentFolder.path}</div>
                  <div className="text-xs text-blue-600 mt-1">
                    Parent ID: {currentFolder.id}
                  </div>
                </div>
              ) : (
                <div>
                  <div className="font-medium">/</div>
                  <div className="text-xs text-blue-600 mt-1">
                    Thư mục gốc (Root)
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Folder Name */}
          <div className="mb-4">
            <label htmlFor="folderName" className="block text-sm font-medium text-gray-700 mb-2">
              Tên Folder <span className="text-red-500">*</span>
            </label>
            <input
              id="folderName"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Nhập tên folder..."
              disabled={isSubmitting}
              className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                errors.name ? 'border-red-300 ring-1 ring-red-300' : 'border-gray-300'
              }`}
              maxLength={100}
              autoFocus
            />
            {errors.name && (
              <div className="mt-1 flex items-center gap-1 text-sm text-red-600">
                <ExclamationTriangleIcon className="w-4 h-4" />
                {errors.name}
              </div>
            )}
            <div className="mt-1 text-xs text-gray-500">
              {formData.name.length}/100 ký tự
            </div>
          </div>

          {/* Folder Description */}
          <div className="mb-6">
            <label htmlFor="folderDescription" className="block text-sm font-medium text-gray-700 mb-2">
              Mô tả (Tùy chọn)
            </label>
            <textarea
              id="folderDescription"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Nhập mô tả cho folder..."
              disabled={isSubmitting}
              rows={3}
              className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed resize-none ${
                errors.description ? 'border-red-300 ring-1 ring-red-300' : 'border-gray-300'
              }`}
              maxLength={500}
            />
            {errors.description && (
              <div className="mt-1 flex items-center gap-1 text-sm text-red-600">
                <ExclamationTriangleIcon className="w-4 h-4" />
                {errors.description}
              </div>
            )}
            <div className="mt-1 text-xs text-gray-500">
              {formData.description.length}/500 ký tự
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !formData.name.trim()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isSubmitting && (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              )}
              {isSubmitting ? 'Đang tạo...' : 'Tạo Folder'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
} 