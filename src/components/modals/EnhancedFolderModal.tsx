"use client";

import React, { useState, useEffect } from "react";
import { Modal } from "@/components/ui/Modal";
import {
  FolderIcon,
  FolderPlusIcon,
  PencilIcon,
  ShareIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CloudArrowUpIcon,
  TagIcon,
  LockClosedIcon,
  GlobeAltIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import { FolderService } from "@/api/services/folderService";
import { ApiClient } from "@/api/core/apiClient";
import { FolderDto, FolderCreateData, FolderUpdateData } from "@/api/types/interfaces";

interface EnhancedFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: "create" | "edit" | "delete" | "move" | "copy" | "share";
  folder?: FolderDto;
  parentFolder?: FolderDto;
  apiClient: ApiClient;
  onSuccess?: (folder?: FolderDto) => void;
}

interface FolderFormData {
  name: string;
  description: string;
  isPublic: boolean;
  tags: string[];
  parentFolderId?: string;
}

export function EnhancedFolderModal({
  isOpen,
  onClose,
  mode,
  folder,
  parentFolder,
  apiClient,
  onSuccess,
}: EnhancedFolderModalProps) {
  const [formData, setFormData] = useState<FolderFormData>({
    name: "",
    description: "",
    isPublic: false,
    tags: [],
    parentFolderId: parentFolder?.id,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newTag, setNewTag] = useState("");

  const folderService = new FolderService(apiClient);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (mode === "edit" && folder) {
        setFormData({
          name: folder.name,
          description: folder.description || "",
          isPublic: false, // You might need to add this to FolderDto
          tags: [], // You might need to add this to FolderDto
          parentFolderId: folder.parentFolderId,
        });
      } else {
        setFormData({
          name: "",
          description: "",
          isPublic: false,
          tags: [],
          parentFolderId: parentFolder?.id,
        });
      }
      setErrors({});
      setIsSubmitting(false);
      setNewTag("");
    }
  }, [isOpen, mode, folder, parentFolder]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Tên thư mục là bắt buộc";
    } else if (formData.name.trim().length < 2) {
      newErrors.name = "Tên thư mục phải có ít nhất 2 ký tự";
    } else if (formData.name.length > 255) {
      newErrors.name = "Tên thư mục không được quá 255 ký tự";
    } else if (/[<>:"/\\|?*]/.test(formData.name)) {
      newErrors.name = "Tên thư mục chứa ký tự không hợp lệ";
    }

    if (formData.description && formData.description.length > 1000) {
      newErrors.description = "Mô tả không được quá 1000 ký tự";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      let result: FolderDto | undefined;

      switch (mode) {
        case "create":
          const createData: FolderCreateData = {
            name: formData.name.trim(),
            description: formData.description || undefined,
            parentFolderId: formData.parentFolderId,
          };
          result = await folderService.create(createData);
          break;

        case "edit":
          if (!folder) throw new Error("Folder is required for edit mode");
          const updateData: FolderUpdateData = {
            name: formData.name.trim(),
            description: formData.description || undefined,
          };
          result = await folderService.update(folder.id, updateData);
          break;

        case "delete":
          if (!folder) throw new Error("Folder is required for delete mode");
          await folderService.delete(folder.id, false, false);
          break;

        case "move":
          // TODO: Implement folder move functionality
          break;

        case "copy":
          // TODO: Implement folder copy functionality
          break;

        case "share":
          // TODO: Implement folder sharing functionality
          break;
      }

      onSuccess?.(result);
      onClose();
    } catch (error) {
      console.error("Folder operation error:", error);
      setErrors({
        submit: error instanceof Error ? error.message : "Có lỗi xảy ra",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const getModalConfig = () => {
    switch (mode) {
      case "create":
        return {
          title: "Tạo thư mục mới",
          icon: FolderPlusIcon,
          color: "blue",
          submitText: "Tạo thư mục",
        };
      case "edit":
        return {
          title: "Chỉnh sửa thư mục",
          icon: PencilIcon,
          color: "yellow",
          submitText: "Cập nhật",
        };
      case "delete":
        return {
          title: "Xóa thư mục",
          icon: TrashIcon,
          color: "red",
          submitText: "Xóa",
        };
      case "move":
        return {
          title: "Di chuyển thư mục",
          icon: FolderIcon,
          color: "green",
          submitText: "Di chuyển",
        };
      case "copy":
        return {
          title: "Sao chép thư mục",
          icon: FolderIcon,
          color: "purple",
          submitText: "Sao chép",
        };
      case "share":
        return {
          title: "Chia sẻ thư mục",
          icon: ShareIcon,
          color: "emerald",
          submitText: "Chia sẻ",
        };
      default:
        return {
          title: "Quản lý thư mục",
          icon: FolderIcon,
          color: "gray",
          submitText: "Xác nhận",
        };
    }
  };

  const config = getModalConfig();
  const IconComponent = config.icon;

  const renderFormContent = () => {
    switch (mode) {
      case "create":
      case "edit":
        return (
          <div className="space-y-6">
            {/* Folder Name */}
            <div>
              <label
                htmlFor="folderName"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Tên thư mục *
              </label>
              <input
                type="text"
                id="folderName"
                value={formData.name}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, name: e.target.value }));
                  if (errors.name) {
                    setErrors(prev => ({ ...prev, name: "" }));
                  }
                }}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-${config.color}-500 focus:border-${config.color}-500 transition-colors ${
                  errors.name
                    ? "border-red-300 bg-red-50"
                    : "border-gray-300"
                }`}
                placeholder="Nhập tên thư mục"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  {errors.name}
                </p>
              )}
            </div>

            {/* Description */}
            <div>
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Mô tả
              </label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, description: e.target.value }));
                  if (errors.description) {
                    setErrors(prev => ({ ...prev, description: "" }));
                  }
                }}
                rows={3}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-${config.color}-500 focus:border-${config.color}-500 transition-colors ${
                  errors.description
                    ? "border-red-300 bg-red-50"
                    : "border-gray-300"
                }`}
                placeholder="Nhập mô tả thư mục (tùy chọn)"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  {errors.description}
                </p>
              )}
            </div>

            {/* Privacy Settings */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Quyền truy cập
              </label>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="privacy"
                    checked={!formData.isPublic}
                    onChange={() => setFormData(prev => ({ ...prev, isPublic: false }))}
                    className="mr-3 text-blue-600"
                  />
                  <LockClosedIcon className="w-5 h-5 text-gray-400 mr-2" />
                  <span className="text-sm">
                    <strong>Riêng tư</strong> - Chỉ bạn có thể truy cập
                  </span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="privacy"
                    checked={formData.isPublic}
                    onChange={() => setFormData(prev => ({ ...prev, isPublic: true }))}
                    className="mr-3 text-blue-600"
                  />
                  <GlobeAltIcon className="w-5 h-5 text-gray-400 mr-2" />
                  <span className="text-sm">
                    <strong>Công khai</strong> - Mọi người có thể xem
                  </span>
                </label>
              </div>
            </div>

            {/* Tags */}
            <div>
              <label
                htmlFor="tags"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Tags
              </label>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Thêm tag"
                  />
                  <button
                    type="button"
                    onClick={addTag}
                    className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                  >
                    <TagIcon className="w-4 h-4" />
                  </button>
                </div>
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-1 text-blue-600 hover:text-blue-800"
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Parent Folder Info */}
            {parentFolder && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-sm text-gray-600">
                  <strong>Thư mục cha:</strong> {parentFolder.name}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Đường dẫn: {parentFolder.path}
                </p>
              </div>
            )}
          </div>
        );

      case "delete":
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full">
              <ExclamationTriangleIcon className="w-6 h-6 text-red-600" />
            </div>
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Xác nhận xóa thư mục
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Bạn có chắc chắn muốn xóa thư mục &quot;{folder?.name}&quot;?
                Thao tác này sẽ di chuyển thư mục vào thùng rác.
              </p>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex">
                  <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400 mr-2 flex-shrink-0" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">Lưu ý:</p>
                    <ul className="mt-1 list-disc list-inside space-y-1">
                      <li>Tất cả files và thư mục con sẽ được di chuyển cùng</li>
                      <li>Bạn có thể khôi phục từ thùng rác sau này</li>
                      <li>Các liên kết chia sẻ sẽ ngừng hoạt động tạm thời</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-500">Chức năng này đang được phát triển</p>
          </div>
        );
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={config.title} size="lg">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Icon */}
        <div className="flex justify-center">
          <div className={`p-3 bg-${config.color}-100 rounded-full`}>
            <IconComponent className={`h-8 w-8 text-${config.color}-600`} />
          </div>
        </div>

        {renderFormContent()}

        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-800 flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-2" />
              {errors.submit}
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            Hủy
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-4 py-2 text-sm font-medium text-white bg-${config.color}-600 border border-transparent rounded-md hover:bg-${config.color}-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-${config.color}-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2`}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Đang xử lý...</span>
              </>
            ) : (
              <>
                <IconComponent className="h-4 w-4" />
                <span>{config.submitText}</span>
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
}
