"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  XMarkIcon,
  TrashIcon,
  ArrowPathIcon,
  FolderIcon,
  DocumentIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { RecycleBinService } from "@/api/services/recycleBinService";
import { ApiClient } from "@/api/core/apiClient";
import { useAuth } from "@/contexts/AuthContext";
import { showToast } from "@/components/ui/ToastProvider";
import { DeletedItemDto, DeletedItemType, SortDirection } from "@/api/types/interfaces";

interface RecycleBinModalProps {
  isOpen: boolean;
  onClose: () => void;
  onItemRestored?: (itemId: string) => void;
  onItemDeleted?: (itemId: string) => void;
}

interface RecycleBinState {
  items: DeletedItemDto[];
  loading: boolean;
  error: string | null;
  selectedItems: Set<string>;
  statistics: {
    totalItems: number;
    totalFiles: number;
    totalFolders: number;
    totalSize: number;
    itemsExpiringSoon: number;
    oldestItemDate: string;
  } | null;
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
  filters: {
    itemType: DeletedItemType | "all";
    search: string;
  };
}

export function RecycleBinModal({
  isOpen,
  onClose,
  onItemRestored,
  onItemDeleted,
}: RecycleBinModalProps) {
  const { user } = useAuth();
  const [state, setState] = useState<RecycleBinState>({
    items: [],
    loading: false,
    error: null,
    selectedItems: new Set(),
    statistics: null,
    pagination: {
      page: 1,
      pageSize: 20,
      totalItems: 0,
      totalPages: 0,
    },
    filters: {
      itemType: "all",
      search: "",
    },
  });

  // Initialize API client and service with stable references
  const apiClient = useRef<ApiClient | null>(null);
  const recycleBinService = useRef<RecycleBinService | null>(null);

  // Update API client when token changes
  useEffect(() => {
    if (user?.access_token) {
      apiClient.current = new ApiClient(
        process.env.NEXT_PUBLIC_API_BASE_URL || "https://localhost:7040",
        user.access_token
      );
      recycleBinService.current = new RecycleBinService(apiClient.current);
    }
  }, [user?.access_token]);

  // Load deleted items with stable dependencies
  const loadDeletedItems = useCallback(async (options?: {
    page?: number;
    pageSize?: number;
    search?: string;
    itemType?: DeletedItemType | "all";
  }) => {
    if (!user?.access_token || !recycleBinService.current) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    const currentOptions = {
      page: options?.page ?? state.pagination.page,
      pageSize: options?.pageSize ?? state.pagination.pageSize,
      search: options?.search ?? state.filters.search,
      itemType: options?.itemType ?? state.filters.itemType,
    };

    try {
      const response = await recycleBinService.current.getDeletedItems({
        page: currentOptions.page,
        pageSize: currentOptions.pageSize,
        search: currentOptions.search || undefined,
        itemType: currentOptions.itemType !== "all" ? currentOptions.itemType : undefined,
        sortBy: "DeletedAt",
        sortDirection: SortDirection.DESC,
      });

      setState((prev) => ({
        ...prev,
        items: response.items || [],
        pagination: {
          ...prev.pagination,
          totalItems: response.totalCount || 0,
          totalPages: response.totalPages || 0,
        },
        loading: false,
      }));
    } catch (error: any) {
      console.error("Failed to load deleted items:", error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error: "Không thể tải danh sách thùng rác. Vui lòng thử lại.",
      }));
      // DO NOT automatically retry - let user manually retry
    }
  }, [user?.access_token]); // Only depend on stable token reference

  // Load statistics with stable dependencies
  const loadStatistics = useCallback(async () => {
    if (!user?.access_token || !recycleBinService.current) return;

    try {
      const stats = await recycleBinService.current.getStatistics();
      setState((prev) => ({ ...prev, statistics: stats }));
    } catch (error: any) {
      console.error("Failed to load recycle bin statistics:", error);
    }
  }, [user?.access_token]); // Only depend on stable token reference

  // Load data when modal opens - only run when modal opens or token changes
  useEffect(() => {
    if (isOpen && user?.access_token) {
      loadDeletedItems();
      loadStatistics();
    }
  }, [isOpen, user?.access_token]); // Remove loadDeletedItems and loadStatistics from dependencies

  // Restore item
  const handleRestoreItem = async (itemId: string) => {
    if (!user?.access_token || !recycleBinService.current) return;

    try {
      await recycleBinService.current.restoreItem(itemId);
      showToast.success("Đã khôi phục thành công!");
      
      // Remove item from list
      setState((prev) => ({
        ...prev,
        items: prev.items.filter((item) => item.id !== itemId),
        selectedItems: new Set([...prev.selectedItems].filter(id => id !== itemId)),
      }));

      onItemRestored?.(itemId);
      loadStatistics(); // Refresh statistics
    } catch (error: any) {
      console.error("Failed to restore item:", error);
      showToast.error("Không thể khôi phục. Vui lòng thử lại.");
    }
  };

  // Permanently delete item
  const handlePermanentDelete = async (itemId: string) => {
    if (!user?.access_token || !recycleBinService.current) return;

    try {
      await recycleBinService.current.permanentlyDeleteItem(itemId);
      showToast.success("Đã xóa vĩnh viễn!");
      
      // Remove item from list
      setState((prev) => ({
        ...prev,
        items: prev.items.filter((item) => item.id !== itemId),
        selectedItems: new Set([...prev.selectedItems].filter(id => id !== itemId)),
      }));

      onItemDeleted?.(itemId);
      loadStatistics(); // Refresh statistics
    } catch (error: any) {
      console.error("Failed to permanently delete item:", error);
      showToast.error("Không thể xóa vĩnh viễn. Vui lòng thử lại.");
    }
  };

  // Empty recycle bin
  const handleEmptyRecycleBin = async () => {
    if (!user?.access_token || !recycleBinService.current) return;

    try {
      const result = await recycleBinService.current.emptyRecycleBin();
      showToast.success(`Đã dọn dẹp ${result.deletedItems} mục và giải phóng ${formatFileSize(result.freedSpace)}`);
      
      // Reload data
      loadDeletedItems();
      loadStatistics();
    } catch (error: any) {
      console.error("Failed to empty recycle bin:", error);
      showToast.error("Không thể dọn dẹp thùng rác. Vui lòng thử lại.");
    }
  };

  // Handle search with current filters
  const handleSearch = () => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 }
    }));
    loadDeletedItems({
      page: 1,
      search: state.filters.search,
      itemType: state.filters.itemType
    });
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(dateString));
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <TrashIcon className="w-6 h-6 text-red-500" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Thùng Rác
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Quản lý các file và folder đã xóa
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Statistics */}
          {state.statistics && (
            <div className="p-6 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {state.statistics.totalItems}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Tổng mục</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {state.statistics.totalFiles}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Files</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {state.statistics.totalFolders}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Folders</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {formatFileSize(state.statistics.totalSize)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Dung lượng</div>
                </div>
              </div>
              
              {state.statistics.itemsExpiringSoon > 0 && (
                <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <div className="flex items-center gap-2">
                    <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />
                    <span className="text-sm text-yellow-800 dark:text-yellow-200">
                      {state.statistics.itemsExpiringSoon} mục sẽ bị xóa vĩnh viễn trong 7 ngày tới
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Filters and Actions */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex flex-col sm:flex-row gap-4 justify-between">
              <div className="flex gap-3">
                <input
                  type="text"
                  placeholder="Tìm kiếm..."
                  value={state.filters.search}
                  onChange={(e) =>
                    setState((prev) => ({
                      ...prev,
                      filters: { ...prev.filters, search: e.target.value },
                    }))
                  }
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <select
                  value={state.filters.itemType}
                  onChange={(e) =>
                    setState((prev) => ({
                      ...prev,
                      filters: { ...prev.filters, itemType: e.target.value as any },
                    }))
                  }
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">Tất cả</option>
                  <option value={DeletedItemType.File}>Files</option>
                  <option value={DeletedItemType.Folder}>Folders</option>
                </select>
                <button
                  onClick={handleSearch}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Tìm kiếm
                </button>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={handleEmptyRecycleBin}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
                >
                  <TrashIcon className="w-4 h-4" />
                  Dọn dẹp thùng rác
                </button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto">
            {state.loading ? (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-3 text-gray-600 dark:text-gray-400">Đang tải...</span>
              </div>
            ) : state.error ? (
              <div className="p-8 text-center">
                <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <p className="text-red-600 dark:text-red-400">{state.error}</p>
                <button
                  onClick={handleSearch}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Thử lại
                </button>
              </div>
            ) : state.items.length === 0 ? (
              <div className="p-8 text-center">
                <TrashIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">Thùng rác trống</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {state.items.map((item) => (
                  <div
                    key={item.id}
                    className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <div className="flex-shrink-0">
                          {item.itemType === DeletedItemType.Folder ? (
                            <FolderIcon className="w-8 h-8 text-blue-500" />
                          ) : (
                            <DocumentIcon className="w-8 h-8 text-gray-500" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-gray-900 dark:text-white truncate">
                            {item.originalName}
                          </h3>
                          <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                            <span>
                              {item.itemType === DeletedItemType.Folder ? "Folder" : "File"}
                            </span>
                            <span>Xóa lúc: {formatDate(item.deletedAt)}</span>
                            {item.originalPath && (
                              <span className="truncate">Đường dẫn: {item.originalPath}</span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <button
                          onClick={() => handleRestoreItem(item.id)}
                          className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                          title="Khôi phục"
                        >
                          <ArrowPathIcon className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => handlePermanentDelete(item.id)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                          title="Xóa vĩnh viễn"
                        >
                          <TrashIcon className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pagination */}
          {state.pagination.totalPages > 1 && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Trang {state.pagination.page} / {state.pagination.totalPages}
                  ({state.pagination.totalItems} mục)
                </span>
                <div className="flex gap-2">
                  <button
                    onClick={() =>
                      setState((prev) => ({
                        ...prev,
                        pagination: { ...prev.pagination, page: prev.pagination.page - 1 },
                      }))
                    }
                    disabled={state.pagination.page <= 1}
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Trước
                  </button>
                  <button
                    onClick={() =>
                      setState((prev) => ({
                        ...prev,
                        pagination: { ...prev.pagination, page: prev.pagination.page + 1 },
                      }))
                    }
                    disabled={state.pagination.page >= state.pagination.totalPages}
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Sau
                  </button>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
