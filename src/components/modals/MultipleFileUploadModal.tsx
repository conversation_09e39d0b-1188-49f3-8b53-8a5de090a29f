"use client";

import React, { useState, useCallback, useRef } from "react";
import {
  XMarkIcon,
  CloudArrowUpIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  DocumentIcon,
  ShieldCheckIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { FileService } from "@/api/services/fileService";
import { ChunkedUploadService } from "@/api/services/chunkedUploadService";
import { UploadOptions, ChunkedUploadOptions } from "@/api/types/interfaces";
import { showToast } from "@/components/ui/ToastProvider";

interface UploadProgress {
  fileId: string;
  fileName: string;
  fileSize: number;
  progress: number;
  status: "pending" | "security_check" | "uploading" | "completed" | "error";
  error?: string;
  isLargeFile?: boolean;
  needsChunked?: boolean;
}

interface SecurityIssue {
  type: "virus" | "malware" | "suspicious_extension" | "size_exceeded" | "forbidden_type";
  message: string;
  severity: "low" | "medium" | "high" | "critical";
}

interface FileSecurityInfo {
  fileId: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  extension: string;
  issues: SecurityIssue[];
  isApproved: boolean;
  requiresConfirmation: boolean;
}

interface MultipleFileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadComplete: () => void;
  currentFolderId: string;
  fileService: FileService;
  chunkedUploadService: ChunkedUploadService;
}

const DANGEROUS_EXTENSIONS = [
  ".exe", ".bat", ".cmd", ".com", ".scr", ".pif", ".vbs", ".js", ".jar",
  ".app", ".deb", ".pkg", ".dmg", ".run", ".msi", ".dll", ".sys"
];

const LARGE_FILE_THRESHOLD = 50 * 1024 * 1024; // 50MB
const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB
const MAX_FILES = 100;

export const MultipleFileUploadModal: React.FC<MultipleFileUploadModalProps> = ({
  isOpen,
  onClose,
  onUploadComplete,
  currentFolderId,
  fileService,
  chunkedUploadService,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [securityInfo, setSecurityInfo] = useState<FileSecurityInfo[]>([]);
  const [showSecurityWarnings, setShowSecurityWarnings] = useState(false);
  const [showLargeFileConfirmation, setShowLargeFileConfirmation] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [currentStep, setCurrentStep] = useState<"select" | "security" | "confirm" | "upload">("select");

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Security check for files
  const performSecurityCheck = useCallback((files: File[]): FileSecurityInfo[] => {
    return files.map((file, index) => {
      const fileId = `${file.name}-${Date.now()}-${index}`;
      const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
      const issues: SecurityIssue[] = [];

      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        issues.push({
          type: "size_exceeded",
          message: `File size (${formatFileSize(file.size)}) exceeds maximum allowed size (${formatFileSize(MAX_FILE_SIZE)})`,
          severity: "critical"
        });
      }

      // Check dangerous extensions
      if (DANGEROUS_EXTENSIONS.includes(extension)) {
        issues.push({
          type: "suspicious_extension",
          message: `Potentially dangerous file extension: ${extension}`,
          severity: "high"
        });
      }

      // Check MIME type vs extension mismatch
      if (file.type && !file.name.toLowerCase().includes(file.type.split('/')[1])) {
        issues.push({
          type: "suspicious_extension",
          message: "File extension doesn't match content type",
          severity: "medium"
        });
      }

      // Check for forbidden types
      const forbiddenTypes = ["application/x-executable", "application/x-msdownload"];
      if (forbiddenTypes.includes(file.type)) {
        issues.push({
          type: "forbidden_type",
          message: "This file type is not allowed",
          severity: "critical"
        });
      }

      const hasCriticalIssues = issues.some(issue => issue.severity === "critical");
      const hasHighIssues = issues.some(issue => issue.severity === "high");

      return {
        fileId,
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        extension,
        issues,
        isApproved: !hasCriticalIssues,
        requiresConfirmation: hasHighIssues && !hasCriticalIssues
      };
    });
  }, []);

  // Handle file selection
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    if (files.length > MAX_FILES) {
      showToast.error(`Maximum ${MAX_FILES} files allowed. Selected: ${files.length}`);
      return;
    }

    processSelectedFiles(files);
  }, [processSelectedFiles]);

  // Handle drag and drop
  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    
    if (files.length > MAX_FILES) {
      showToast.error(`Maximum ${MAX_FILES} files allowed. Dropped: ${files.length}`);
      return;
    }

    // Process dropped files directly
    processSelectedFiles(files);
  }, []);

  // Process selected files (extracted from handleFileSelect)
  const processSelectedFiles = useCallback((files: File[]) => {
    setSelectedFiles(files);
    
    // Perform security check
    const securityResults = performSecurityCheck(files);
    setSecurityInfo(securityResults);

    // Check if there are security issues
    const hasSecurityIssues = securityResults.some(info => 
      info.issues.length > 0 || info.requiresConfirmation
    );

    if (hasSecurityIssues) {
      setShowSecurityWarnings(true);
      setCurrentStep("security");
    } else {
      // Check for large files
      const hasLargeFiles = files.some(file => file.size > LARGE_FILE_THRESHOLD);
      if (hasLargeFiles) {
        setShowLargeFileConfirmation(true);
        setCurrentStep("confirm");
      } else {
        setCurrentStep("upload");
      }
    }

    // Initialize upload progress
    const initialProgress: UploadProgress[] = files.map((file, index) => ({
      fileId: `${file.name}-${Date.now()}-${index}`,
      fileName: file.name,
      fileSize: file.size,
      progress: 0,
      status: "pending",
      isLargeFile: file.size > LARGE_FILE_THRESHOLD,
      needsChunked: file.size > 10 * 1024 * 1024 // 10MB
    }));
    setUploadProgress(initialProgress);
  }, [performSecurityCheck]);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }, []);

  // Proceed with upload after security confirmation
  const handleSecurityConfirmation = useCallback(() => {
    const approvedFiles = selectedFiles.filter((_, index) => securityInfo[index]?.isApproved);
    
    if (approvedFiles.length === 0) {
      showToast.error("No files approved for upload");
      return;
    }

    if (approvedFiles.length !== selectedFiles.length) {
      showToast.warning(`${selectedFiles.length - approvedFiles.length} files were blocked due to security issues`);
    }

    setSelectedFiles(approvedFiles);
    setShowSecurityWarnings(false);

    // Check for large files in approved files
    const hasLargeFiles = approvedFiles.some(file => file.size > LARGE_FILE_THRESHOLD);
    if (hasLargeFiles) {
      setShowLargeFileConfirmation(true);
      setCurrentStep("confirm");
    } else {
      setCurrentStep("upload");
    }
  }, [selectedFiles, securityInfo]);

  // Handle large file confirmation
  const handleLargeFileConfirmation = useCallback(() => {
    setShowLargeFileConfirmation(false);
    setCurrentStep("upload");
  }, []);

  // Start upload process
  const startUpload = useCallback(async () => {
    if (selectedFiles.length === 0) return;

    setIsUploading(true);
    setCurrentStep("upload");

    const BATCH_SIZE = 3; // Smaller batch for large files
    let completedCount = 0;
    let errorCount = 0;

    // Process files in batches
    for (let i = 0; i < selectedFiles.length; i += BATCH_SIZE) {
      const batch = selectedFiles.slice(i, i + BATCH_SIZE);
      
      const batchPromises = batch.map(async (file, batchIndex) => {
        const overallIndex = i + batchIndex;
        const progressItem = uploadProgress[overallIndex];
        
        if (!progressItem) return;

        try {
          // Update status to security check
          setUploadProgress(prev => prev.map(p => 
            p.fileId === progressItem.fileId 
              ? { ...p, status: "security_check" }
              : p
          ));

          await new Promise(resolve => setTimeout(resolve, 500)); // Simulate security check

          // Update status to uploading
          setUploadProgress(prev => prev.map(p => 
            p.fileId === progressItem.fileId 
              ? { ...p, status: "uploading" }
              : p
          ));

          const uploadOptions: UploadOptions = {
            parentFolderId: currentFolderId,
            displayName: file.name,
            syncToGoogleDrive: false,
            overwriteExisting: false,
          };

          let uploadedFile;

          // Use chunked upload for large files
          if (progressItem.needsChunked) {
            const chunkedOptions: ChunkedUploadOptions = {
              ...uploadOptions,
              chunkSize: 1024 * 1024, // 1MB chunks
              onProgress: (progressData) => {
                setUploadProgress(prev => prev.map(p => 
                  p.fileId === progressItem.fileId 
                    ? { ...p, progress: progressData.progress }
                    : p
                ));
              },
            };
            uploadedFile = await chunkedUploadService.uploadLargeFile(file, chunkedOptions);
          } else {
            uploadedFile = await fileService.uploadWithProgress(
              file,
              uploadOptions,
              (progressEvent: any) => {
                const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                setUploadProgress(prev => prev.map(p => 
                  p.fileId === progressItem.fileId 
                    ? { ...p, progress }
                    : p
                ));
              }
            );
          }

          // Mark as completed
          setUploadProgress(prev => prev.map(p => 
            p.fileId === progressItem.fileId 
              ? { ...p, status: "completed", progress: 100 }
              : p
          ));

          completedCount++;
        } catch (error: any) {
          console.error(`Failed to upload ${file.name}:`, error);
          
          setUploadProgress(prev => prev.map(p => 
            p.fileId === progressItem.fileId 
              ? { 
                  ...p, 
                  status: "error", 
                  error: error.message || "Upload failed",
                  progress: 0 
                }
              : p
          ));

          errorCount++;
        }
      });

      await Promise.all(batchPromises);
      
      // Small delay between batches
      if (i + BATCH_SIZE < selectedFiles.length) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    setIsUploading(false);

    // Show results
    if (completedCount > 0) {
      showToast.success(`Successfully uploaded ${completedCount} files`);
    }
    if (errorCount > 0) {
      showToast.error(`Failed to upload ${errorCount} files`);
    }

    // Auto close and reload after 2 seconds
    setTimeout(() => {
      handleClose();
      onUploadComplete();
    }, 2000);
  }, [selectedFiles, uploadProgress, currentFolderId, fileService, chunkedUploadService, onUploadComplete]);

  // Close modal
  const handleClose = useCallback(() => {
    if (isUploading) {
      const confirmed = window.confirm("Upload is in progress. Are you sure you want to close?");
      if (!confirmed) return;
    }

    setSelectedFiles([]);
    setUploadProgress([]);
    setSecurityInfo([]);
    setShowSecurityWarnings(false);
    setShowLargeFileConfirmation(false);
    setIsUploading(false);
    setCurrentStep("select");
    
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    
    onClose();
  }, [isUploading, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CloudArrowUpIcon className="w-6 h-6 text-blue-500" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Upload Multiple Files
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Step {currentStep === "select" ? 1 : currentStep === "security" ? 2 : currentStep === "confirm" ? 3 : 4} of 4
              </p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            disabled={isUploading}
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Step 1: File Selection */}
          {currentStep === "select" && (
            <div className="space-y-6">
              {/* File Drop Zone */}
              <div
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center hover:border-blue-400 dark:hover:border-blue-500 transition-colors"
              >
                <CloudArrowUpIcon className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Drop files here or click to browse
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Maximum {MAX_FILES} files, up to {formatFileSize(MAX_FILE_SIZE)} each
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileSelect}
                  className="hidden"
                  accept="*/*"
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="btn-primary"
                >
                  Browse Files
                </button>
              </div>
            </div>
          )}

          {/* Step 2: Security Warnings */}
          {currentStep === "security" && showSecurityWarnings && (
            <div className="space-y-6">
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200 mb-3">
                  <ShieldCheckIcon className="w-5 h-5" />
                  <h3 className="font-medium">Security Check Results</h3>
                </div>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  Some files have potential security issues. Please review below.
                </p>
              </div>

              <div className="space-y-3">
                {securityInfo.map((info, index) => (
                  <div key={info.fileId} className={`border rounded-lg p-4 ${
                    !info.isApproved 
                      ? "border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20"
                      : info.requiresConfirmation
                      ? "border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20"
                      : "border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20"
                  }`}>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <DocumentIcon className="w-4 h-4" />
                        <span className="font-medium">{info.fileName}</span>
                        <span className="text-xs text-gray-500">({formatFileSize(info.fileSize)})</span>
                      </div>
                      {info.isApproved ? (
                        <CheckCircleIcon className="w-5 h-5 text-green-500" />
                      ) : (
                        <XCircleIcon className="w-5 h-5 text-red-500" />
                      )}
                    </div>

                    {info.issues.length > 0 && (
                      <div className="space-y-1">
                        {info.issues.map((issue, issueIndex) => (
                          <div key={issueIndex} className="flex items-center gap-2 text-sm">
                            <ExclamationTriangleIcon className={`w-4 h-4 ${
                              issue.severity === "critical" ? "text-red-500" :
                              issue.severity === "high" ? "text-orange-500" :
                              issue.severity === "medium" ? "text-yellow-500" :
                              "text-blue-500"
                            }`} />
                            <span className={
                              issue.severity === "critical" ? "text-red-700 dark:text-red-300" :
                              issue.severity === "high" ? "text-orange-700 dark:text-orange-300" :
                              issue.severity === "medium" ? "text-yellow-700 dark:text-yellow-300" :
                              "text-blue-700 dark:text-blue-300"
                            }>
                              {issue.message}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSecurityConfirmation}
                  className="btn-primary"
                  disabled={!securityInfo.some(info => info.isApproved)}
                >
                  Continue with Approved Files
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Large File Confirmation */}
          {currentStep === "confirm" && showLargeFileConfirmation && (
            <div className="space-y-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div className="flex items-center gap-2 text-blue-800 dark:text-blue-200 mb-3">
                  <ClockIcon className="w-5 h-5" />
                  <h3 className="font-medium">Large File Detection</h3>
                </div>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Some files are larger than {formatFileSize(LARGE_FILE_THRESHOLD)} and will use chunked upload for better reliability.
                </p>
              </div>

              <div className="space-y-3">
                {selectedFiles
                  .filter(file => file.size > LARGE_FILE_THRESHOLD)
                  .map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-center gap-2">
                        <DocumentIcon className="w-4 h-4 text-blue-500" />
                        <span className="font-medium">{file.name}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{formatFileSize(file.size)}</div>
                        <div className="text-xs text-gray-500">Chunked upload</div>
                      </div>
                    </div>
                  ))}
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Upload Information:</h4>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• Large files will be split into 1MB chunks</li>
                  <li>• Upload can be resumed if connection is lost</li>
                  <li>• Progress will be tracked for each file</li>
                  <li>• Estimated time: {Math.ceil(selectedFiles.reduce((acc, f) => acc + f.size, 0) / (1024 * 1024))} minutes</li>
                </ul>
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleLargeFileConfirmation}
                  className="btn-primary"
                >
                  Confirm Upload
                </button>
              </div>
            </div>
          )}

          {/* Step 4: Upload Progress */}
          {currentStep === "upload" && (
            <div className="space-y-6">
              {!isUploading && uploadProgress.length > 0 && (
                <div className="flex justify-center">
                  <button
                    onClick={startUpload}
                    className="btn-primary px-8 py-3 text-lg"
                  >
                    <CloudArrowUpIcon className="w-5 h-5 mr-2" />
                    Start Upload
                  </button>
                </div>
              )}

              {/* Upload Progress */}
              {uploadProgress.length > 0 && (
                <div className="space-y-4">
                  {/* Summary */}
                  <div className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      Upload Progress ({uploadProgress.length} files)
                    </h3>
                    <div className="flex items-center gap-4 text-sm">
                      <span className="text-green-600 dark:text-green-400">
                        ✓ {uploadProgress.filter(u => u.status === "completed").length} completed
                      </span>
                      <span className="text-blue-600 dark:text-blue-400">
                        ⏳ {uploadProgress.filter(u => u.status === "uploading" || u.status === "security_check").length} processing
                      </span>
                      <span className="text-red-600 dark:text-red-400">
                        ✗ {uploadProgress.filter(u => u.status === "error").length} failed
                      </span>
                    </div>
                  </div>

                  {/* Individual Progress */}
                  <div className="max-h-96 overflow-y-auto space-y-2">
                    {uploadProgress.map((item) => (
                      <div key={item.fileId} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <DocumentIcon className="w-4 h-4 text-gray-500 flex-shrink-0" />
                            <span className="font-medium truncate">{item.fileName}</span>
                            {item.isLargeFile && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Large</span>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-500">{formatFileSize(item.fileSize)}</span>
                            {item.status === "completed" && <CheckCircleIcon className="w-5 h-5 text-green-500" />}
                            {item.status === "error" && <XCircleIcon className="w-5 h-5 text-red-500" />}
                            {item.status === "security_check" && (
                              <div className="flex items-center gap-1">
                                <ShieldCheckIcon className="w-4 h-4 text-blue-500" />
                                <span className="text-xs text-blue-600">Security Check</span>
                              </div>
                            )}
                            {(item.status === "uploading" || item.status === "security_check") && (
                              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                            )}
                          </div>
                        </div>

                        {/* Progress Bar */}
                        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all ${
                              item.status === "completed" ? "bg-green-500" :
                              item.status === "error" ? "bg-red-500" :
                              item.status === "security_check" ? "bg-blue-500" :
                              "bg-blue-500"
                            }`}
                            style={{ 
                              width: item.status === "security_check" ? "30%" : 
                                     item.status === "pending" ? "0%" : 
                                     `${item.progress}%` 
                            }}
                          />
                        </div>

                        {item.error && (
                          <p className="text-xs text-red-600 dark:text-red-400 mt-1">{item.error}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        {currentStep !== "upload" && (
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              disabled={isUploading}
            >
              Cancel
            </button>
          </div>
        )}
      </div>
    </div>
  );
}; 