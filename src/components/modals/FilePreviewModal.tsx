"use client";

import React, { useState, useEffect, useCallback } from 'react';
import {
  XMarkIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import { FileDto } from '@/api/types/interfaces';
import { FileService } from '@/api/services/fileService';

interface FilePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: FileDto | null;
  fileService: FileService;
}

export function FilePreviewModal({
  isOpen,
  onClose,
  file,
  fileService
}: FilePreviewModalProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [zoom, setZoom] = useState(100);
  const [fullscreen, setFullscreen] = useState(false);

  // Handle ESC key to exit fullscreen or close modal
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      if (fullscreen) {
        setFullscreen(false);
      } else {
        onClose();
      }
    }
  }, [fullscreen, onClose]);

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, handleKeyDown]);

  useEffect(() => {
    if (isOpen && file) {
      loadPreview();
    } else {
      // Clean up preview URL when modal closes
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
      setPreviewUrl(null);
      setError(null);
      setZoom(100);
      setFullscreen(false);
    }
  }, [isOpen, file]);

  // Cleanup blob URLs on unmount
  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const loadPreview = async () => {
    if (!file) return;

    setLoading(true);
    setError(null);

    try {
      // Check if file type is previewable
      const extension = file.name.split('.').pop()?.toLowerCase();
      const previewableTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'pdf', 'txt', 'md'];
      
      if (!previewableTypes.includes(extension || '')) {
        setError('Preview not available for this file type');
        setLoading(false);
        return;
      }

      // Use FileService stream method to get the proper content with authentication
      const streamResponse = await fileService.stream(file.id);
      
      // Create blob URL for preview
      const blobUrl = URL.createObjectURL(streamResponse.blob);
      setPreviewUrl(blobUrl);
    } catch (error: any) {
      console.error('Failed to load preview:', error);
      setError('Failed to load preview. The file might be too large or corrupted.');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    if (!file) return;
    
    try {
      await fileService.downloadWithFilename({
        id: file.id,
        name: file.name,
        displayName: file.displayName,
        mimeType: file.mimeType
      });
    } catch (error: any) {
      console.error('Failed to download file:', error);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 300));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25));
  };

  const handleZoomReset = () => {
    setZoom(100);
  };

  const toggleFullscreen = () => {
    setFullscreen(prev => !prev);
  };

  const getFileIcon = () => {
    if (!file) return <DocumentIcon className="w-16 h-16 text-gray-400" />;
    
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension || '')) {
      return <img src="/icons/image.svg" alt="Image" className="w-16 h-16" />;
    }
    if (['pdf'].includes(extension || '')) {
      return <img src="/icons/pdf.svg" alt="PDF" className="w-16 h-16" />;
    }
    if (['txt', 'md'].includes(extension || '')) {
      return <img src="/icons/text.svg" alt="Text" className="w-16 h-16" />;
    }
    return <DocumentIcon className="w-16 h-16 text-gray-400" />;
  };

  const renderPreview = () => {
    // Dynamic height based on fullscreen mode
    const containerHeight = fullscreen ? 'h-full' : 'h-96';
    const minHeight = fullscreen ? 'min-h-full' : 'min-h-96';

    if (loading) {
      return (
        <div className={`flex items-center justify-center ${containerHeight}`}>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-300">Loading preview...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className={`flex flex-col items-center justify-center ${containerHeight} text-center`}>
          {getFileIcon()}
          <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">{file?.displayName || file?.name}</h3>
          <p className="mt-2 text-sm text-red-600 dark:text-red-400">{error}</p>
          <button
            onClick={handleDownload}
            className="mt-4 flex items-center gap-2 px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600"
          >
            <ArrowDownTrayIcon className="w-4 h-4" />
            Download File
          </button>
        </div>
      );
    }

    if (!previewUrl) {
      return (
        <div className={`flex flex-col items-center justify-center ${containerHeight} text-center`}>
          {getFileIcon()}
          <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">{file?.displayName || file?.name}</h3>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">No preview available</p>
        </div>
      );
    }

    const extension = file?.name.split('.').pop()?.toLowerCase();

    // Image preview
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension || '')) {
      return (
        <div className={`flex items-center justify-center ${minHeight} p-4 overflow-auto`}>
          <img
            src={previewUrl}
            alt={file?.displayName || file?.name}
            className="max-w-full max-h-full object-contain transition-transform"
            style={{ transform: `scale(${zoom / 100})` }}
          />
        </div>
      );
    }

    // PDF preview
    if (extension === 'pdf') {
      return (
        <div className={`${containerHeight} ${fullscreen ? 'p-0' : 'p-2'}`}>
          <iframe
            src={`${previewUrl}#view=FitH`}
            className="w-full h-full border-0 rounded-lg"
            title={file?.displayName || file?.name}
            style={{ minHeight: fullscreen ? '100%' : '24rem' }}
          />
        </div>
      );
    }

    // Text preview
    if (['txt', 'md'].includes(extension || '')) {
      return (
        <div className={`${containerHeight} overflow-auto ${fullscreen ? 'p-4' : 'p-2'}`}>
          <iframe
            src={previewUrl}
            className="w-full h-full border-0 rounded-lg bg-white dark:bg-gray-900"
            title={file?.displayName || file?.name}
          />
        </div>
      );
    }

    return (
      <div className={`flex flex-col items-center justify-center ${containerHeight} text-center`}>
        {getFileIcon()}
        <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">{file?.displayName || file?.name}</h3>
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Preview not supported for this file type</p>
      </div>
    );
  };

  if (!isOpen || !file) return null;

  const modalClasses = fullscreen 
    ? "fixed inset-0 bg-black z-50 flex flex-col"
    : "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4";

  const contentClasses = fullscreen
    ? "bg-white dark:bg-gray-900 w-full h-full flex flex-col"
    : "bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[95vh] flex flex-col";

  return (
    <div className={modalClasses} onClick={fullscreen ? undefined : onClose}>
      <div className={contentClasses} onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className={`flex items-center justify-between border-b border-gray-200 dark:border-gray-700 flex-shrink-0 ${
          fullscreen ? 'p-3 bg-gray-50 dark:bg-gray-800' : 'p-4'
        }`}>
          <div className="flex items-center gap-3 min-w-0">
            <div className="min-w-0">
              <h2 className={`font-semibold text-gray-900 dark:text-gray-100 truncate ${
                fullscreen ? 'text-base' : 'text-lg'
              }`}>
                {file.displayName || file.name}
              </h2>
              <p className={`text-gray-500 dark:text-gray-400 truncate ${
                fullscreen ? 'text-xs' : 'text-sm'
              }`}>
                {file.mimeType} • {Math.round(file.fileSize / 1024)} KB
              </p>
            </div>
          </div>
          
          {/* Controls */}
          <div className="flex items-center gap-1 flex-shrink-0">
            {previewUrl && ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(file.name.split('.').pop()?.toLowerCase() || '') && (
              <>
                <button
                  onClick={handleZoomOut}
                  className={`text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg ${
                    fullscreen ? 'p-1' : 'p-2'
                  }`}
                  title="Zoom Out"
                >
                  <MagnifyingGlassMinusIcon className={fullscreen ? 'w-4 h-4' : 'w-5 h-5'} />
                </button>
                <span className={`text-gray-600 dark:text-gray-300 min-w-[3rem] text-center ${
                  fullscreen ? 'text-xs' : 'text-sm'
                }`}>
                  {zoom}%
                </span>
                <button
                  onClick={handleZoomIn}
                  className={`text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg ${
                    fullscreen ? 'p-1' : 'p-2'
                  }`}
                  title="Zoom In"
                >
                  <MagnifyingGlassPlusIcon className={fullscreen ? 'w-4 h-4' : 'w-5 h-5'} />
                </button>
                <button
                  onClick={handleZoomReset}
                  className={`text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 border border-gray-300 dark:border-gray-600 rounded ${
                    fullscreen ? 'px-2 py-0.5 text-xs' : 'px-3 py-1 text-sm'
                  }`}
                  title="Reset Zoom"
                >
                  Reset
                </button>
              </>
            )}
            
            <button
              onClick={toggleFullscreen}
              className={`text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg ${
                fullscreen ? 'p-1' : 'p-2'
              }`}
              title={fullscreen ? "Exit Fullscreen (Esc)" : "Fullscreen"}
            >
              {fullscreen ? (
                <ArrowsPointingInIcon className={fullscreen ? 'w-4 h-4' : 'w-5 h-5'} />
              ) : (
                <ArrowsPointingOutIcon className={fullscreen ? 'w-4 h-4' : 'w-5 h-5'} />
              )}
            </button>
            
            <button
              onClick={handleDownload}
              className={`text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg ${
                fullscreen ? 'p-1' : 'p-2'
              }`}
              title="Download"
            >
              <ArrowDownTrayIcon className={fullscreen ? 'w-4 h-4' : 'w-5 h-5'} />
            </button>
            
            <button
              onClick={onClose}
              className={`text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg ${
                fullscreen ? 'p-1' : 'p-2'
              }`}
              title={fullscreen ? "Close (Esc)" : "Close"}
            >
              <XMarkIcon className={fullscreen ? 'w-4 h-4' : 'w-5 h-5'} />
            </button>
          </div>
        </div>

        {/* Preview Content */}
        <div className="flex-1 overflow-hidden">
          {renderPreview()}
        </div>
      </div>
    </div>
  );
}
