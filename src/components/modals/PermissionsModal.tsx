"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  XMarkIcon,
  UserIcon,
  ShieldCheckIcon,
  PlusIcon,
  TrashIcon,
  PencilIcon,
  ClockIcon,
} from "@heroicons/react/24/outline";
import { PermissionService } from "@/api/services/permissionService";
import { ApiClient } from "@/api/core/apiClient";
import { useAuth } from "@/contexts/AuthContext";
import { showToast } from "@/components/ui/ToastProvider";
import { PermissionDto, PermissionType, PermissionRequest } from "@/api/types/interfaces";

interface PermissionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: string;
  itemType: "file" | "folder";
  itemName: string;
}

interface PermissionsState {
  permissions: PermissionDto[];
  availablePermissionTypes: Array<{
    value: PermissionType;
    name: string;
    description: string;
    applicableToFiles: boolean;
    applicableToFolders: boolean;
  }>;
  loading: boolean;
  error: string | null;
  showAddPermissionForm: boolean;
  newPermission: {
    userId: string;
    roleId: string;
    permission: PermissionType | "";
    expiresAt: string;
    inheritToChildren: boolean;
  };
}

export function PermissionsModal({
  isOpen,
  onClose,
  itemId,
  itemType,
  itemName,
}: PermissionsModalProps) {
  const { user } = useAuth();
  const [state, setState] = useState<PermissionsState>({
    permissions: [],
    availablePermissionTypes: [],
    loading: false,
    error: null,
    showAddPermissionForm: false,
    newPermission: {
      userId: "",
      roleId: "",
      permission: "",
      expiresAt: "",
      inheritToChildren: itemType === "folder",
    },
  });

  // Initialize API client and service
  const apiClient = React.useMemo(
    () =>
      new ApiClient(
        process.env.NEXT_PUBLIC_API_BASE_URL || "https://localhost:7040",
        user?.access_token || ""
      ),
    [user?.access_token]
  );

  const permissionService = React.useMemo(
    () => new PermissionService(apiClient),
    [apiClient]
  );

  // Load permissions
  const loadPermissions = useCallback(async () => {
    if (!user?.access_token || !itemId) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      let permissions: PermissionDto[] = [];
      
      if (itemType === "file") {
        permissions = await permissionService.getFilePermissions(itemId);
      } else {
        permissions = await permissionService.getFolderPermissions(itemId);
      }

      setState((prev) => ({
        ...prev,
        permissions: permissions || [],
        loading: false,
      }));
    } catch (error: any) {
      console.error("Failed to load permissions:", error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error: "Không thể tải danh sách quyền. Vui lòng thử lại.",
      }));
    }
  }, [user?.access_token, permissionService, itemId, itemType]);

  // Load available permission types
  const loadPermissionTypes = useCallback(async () => {
    if (!user?.access_token) return;

    try {
      const types = await permissionService.getAvailablePermissionTypes(itemType);
      setState((prev) => ({
        ...prev,
        availablePermissionTypes: types || [],
      }));
    } catch (error: any) {
      console.error("Failed to load permission types:", error);
    }
  }, [user?.access_token, permissionService, itemType]);

  // Load data when modal opens
  useEffect(() => {
    if (isOpen && user?.access_token) {
      loadPermissions();
      loadPermissionTypes();
    }
  }, [isOpen, user?.access_token, loadPermissions, loadPermissionTypes]);

  // Grant permission
  const handleGrantPermission = async () => {
    if (!user?.access_token || !state.newPermission.permission) return;

    try {
      const request: PermissionRequest = {
        userId: state.newPermission.userId || undefined,
        roleId: state.newPermission.roleId || undefined,
        permission: state.newPermission.permission as PermissionType,
        expiresAt: state.newPermission.expiresAt || undefined,
        inheritToChildren: state.newPermission.inheritToChildren,
      };

      if (itemType === "file") {
        await permissionService.grantFilePermission(itemId, request);
      } else {
        await permissionService.grantFolderPermission(itemId, request);
      }

      showToast.success("Đã cấp quyền thành công!");
      
      // Reset form and reload permissions
      setState((prev) => ({
        ...prev,
        showAddPermissionForm: false,
        newPermission: {
          userId: "",
          roleId: "",
          permission: "",
          expiresAt: "",
          inheritToChildren: itemType === "folder",
        },
      }));

      loadPermissions();
    } catch (error: any) {
      console.error("Failed to grant permission:", error);
      showToast.error("Không thể cấp quyền. Vui lòng thử lại.");
    }
  };

  // Revoke permission
  const handleRevokePermission = async (permissionId: string) => {
    if (!user?.access_token) return;

    try {
      if (itemType === "file") {
        await permissionService.revokeFilePermission(itemId, permissionId);
      } else {
        await permissionService.revokeFolderPermission(itemId, permissionId);
      }

      showToast.success("Đã thu hồi quyền thành công!");
      loadPermissions();
    } catch (error: any) {
      console.error("Failed to revoke permission:", error);
      showToast.error("Không thể thu hồi quyền. Vui lòng thử lại.");
    }
  };

  // Format permission type
  const formatPermissionType = (permission: PermissionType) => {
    const type = state.availablePermissionTypes.find(t => t.value === permission);
    return type ? type.name : permission.toString();
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(dateString));
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <ShieldCheckIcon className="w-6 h-6 text-blue-500" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Quản lý Quyền Truy Cập
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {itemType === "file" ? "File" : "Folder"}: {itemName}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Add Permission Button */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setState((prev) => ({ ...prev, showAddPermissionForm: !prev.showAddPermissionForm }))}
              className="btn-primary flex items-center gap-2"
            >
              <PlusIcon className="w-4 h-4" />
              Thêm Quyền Mới
            </button>
          </div>

          {/* Add Permission Form */}
          {state.showAddPermissionForm && (
            <div className="p-6 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Thêm Quyền Mới
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    User ID
                  </label>
                  <input
                    type="text"
                    value={state.newPermission.userId}
                    onChange={(e) =>
                      setState((prev) => ({
                        ...prev,
                        newPermission: { ...prev.newPermission, userId: e.target.value },
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Nhập User ID"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Role ID (tùy chọn)
                  </label>
                  <input
                    type="text"
                    value={state.newPermission.roleId}
                    onChange={(e) =>
                      setState((prev) => ({
                        ...prev,
                        newPermission: { ...prev.newPermission, roleId: e.target.value },
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Nhập Role ID"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Loại Quyền
                  </label>
                  <select
                    value={state.newPermission.permission}
                    onChange={(e) =>
                      setState((prev) => ({
                        ...prev,
                        newPermission: { ...prev.newPermission, permission: e.target.value as PermissionType },
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="">Chọn loại quyền</option>
                    {state.availablePermissionTypes
                      .filter(type =>
                        itemType === "file" ? type.applicableToFiles : type.applicableToFolders
                      )
                      .map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.name} - {type.description}
                        </option>
                      ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Ngày Hết Hạn (tùy chọn)
                  </label>
                  <input
                    type="datetime-local"
                    value={state.newPermission.expiresAt}
                    onChange={(e) =>
                      setState((prev) => ({
                        ...prev,
                        newPermission: { ...prev.newPermission, expiresAt: e.target.value },
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>

              {itemType === "folder" && (
                <div className="mt-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={state.newPermission.inheritToChildren}
                      onChange={(e) =>
                        setState((prev) => ({
                          ...prev,
                          newPermission: { ...prev.newPermission, inheritToChildren: e.target.checked },
                        }))
                      }
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Áp dụng cho các file và folder con
                    </span>
                  </label>
                </div>
              )}

              <div className="flex gap-3 mt-6">
                <button
                  onClick={handleGrantPermission}
                  disabled={!state.newPermission.permission || (!state.newPermission.userId && !state.newPermission.roleId)}
                  className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cấp Quyền
                </button>
                <button
                  onClick={() => setState((prev) => ({ ...prev, showAddPermissionForm: false }))}
                  className="btn-secondary"
                >
                  Hủy
                </button>
              </div>
            </div>
          )}

          {/* Permissions List */}
          <div className="flex-1 overflow-auto">
            {state.loading ? (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-3 text-gray-600 dark:text-gray-400">Đang tải...</span>
              </div>
            ) : state.error ? (
              <div className="p-8 text-center">
                <p className="text-red-600 dark:text-red-400">{state.error}</p>
                <button
                  onClick={loadPermissions}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Thử lại
                </button>
              </div>
            ) : state.permissions.length === 0 ? (
              <div className="p-8 text-center">
                <ShieldCheckIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">Chưa có quyền nào được cấp</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {state.permissions?.map((permission) => (
                  <div
                    key={permission.id}
                    className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <div className="flex-shrink-0">
                          <UserIcon className="w-8 h-8 text-blue-500" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {permission.userName || permission.userId}
                          </h3>
                          <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                            <span className="font-medium text-blue-600">
                              {formatPermissionType(permission.permission)}
                            </span>
                            {permission.expiresAt && (
                              <span className="flex items-center gap-1">
                                <ClockIcon className="w-4 h-4" />
                                Hết hạn: {formatDate(permission.expiresAt)}
                              </span>
                            )}
                            {permission.inheritToChildren && itemType === "folder" && (
                              <span className="text-green-600">Áp dụng cho con</span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <button
                          onClick={() => handleRevokePermission(permission.id)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                          title="Thu hồi quyền"
                        >
                          <TrashIcon className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
