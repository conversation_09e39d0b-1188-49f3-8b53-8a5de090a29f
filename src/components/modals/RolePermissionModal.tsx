"use client";

import React, { useState, useEffect } from "react";
import { Modal } from "@/components/ui/Modal";
import {
  UserGroupIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  PencilIcon,
  EyeIcon,
} from "@heroicons/react/24/outline";

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  actions: string[];
}

interface Role {
  id?: string;
  name: string;
  description: string;
  permissions: string[];
  color: string;
  isActive: boolean;
  userCount?: number;
}

interface RolePermissionModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: "add" | "edit" | "view";
  role?: Role;
  availablePermissions: Permission[];
  onSubmit?: (role: Role) => void;
}

const ROLE_COLORS = [
  {
    value: "red",
    label: "Đỏ",
    class: "bg-red-100 text-red-800 border-red-200",
  },
  {
    value: "blue",
    label: "<PERSON><PERSON><PERSON> dương",
    class: "bg-blue-100 text-blue-800 border-blue-200",
  },
  {
    value: "green",
    label: "Xanh lá",
    class: "bg-green-100 text-green-800 border-green-200",
  },
  {
    value: "purple",
    label: "Tím",
    class: "bg-purple-100 text-purple-800 border-purple-200",
  },
  {
    value: "yellow",
    label: "Vàng",
    class: "bg-yellow-100 text-yellow-800 border-yellow-200",
  },
  {
    value: "gray",
    label: "Xám",
    class: "bg-gray-100 text-gray-800 border-gray-200",
  },
];

export function RolePermissionModal({
  isOpen,
  onClose,
  mode,
  role,
  availablePermissions,
  onSubmit,
}: RolePermissionModalProps) {
  const [formData, setFormData] = useState<Role>({
    name: "",
    description: "",
    permissions: [],
    color: "blue",
    isActive: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");

  // Reset form when modal opens/closes or role changes
  useEffect(() => {
    if (isOpen && role && (mode === "edit" || mode === "view")) {
      setFormData(role);
    } else if (isOpen && mode === "add") {
      setFormData({
        name: "",
        description: "",
        permissions: [],
        color: "blue",
        isActive: true,
      });
    }
    setErrors({});
    setSearchTerm("");
    setSelectedCategory("");
  }, [isOpen, role, mode]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handlePermissionToggle = (permissionId: string) => {
    if (mode === "view") return;

    setFormData((prev) => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter((p) => p !== permissionId)
        : [...prev.permissions, permissionId],
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = "Tên vai trò là bắt buộc";
    if (!formData.description.trim())
      newErrors.description = "Mô tả là bắt buộc";
    if (formData.permissions.length === 0)
      newErrors.permissions = "Phải chọn ít nhất một quyền";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (mode === "view") return;
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      if (onSubmit) {
        onSubmit({
          ...formData,
          id: role?.id || `role_${Date.now()}`,
        });
      }

      onClose();
    } catch (error) {
      console.error("Error saving role:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case "add":
        return "Thêm vai trò mới";
      case "edit":
        return "Chỉnh sửa vai trò";
      case "view":
        return "Chi tiết vai trò";
      default:
        return "Vai trò";
    }
  };

  const getModalIcon = () => {
    switch (mode) {
      case "add":
        return PlusIcon;
      case "edit":
        return PencilIcon;
      case "view":
        return EyeIcon;
      default:
        return UserGroupIcon;
    }
  };

  const ModalIcon = getModalIcon();
  const isReadOnly = mode === "view";
  const selectedRoleColor = ROLE_COLORS.find(
    (color) => color.value === formData.color
  );

  // Filter permissions based on search and category
  const filteredPermissions = availablePermissions.filter((permission) => {
    const matchesSearch =
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      !selectedCategory || permission.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  // Group permissions by category
  const permissionsByCategory = filteredPermissions.reduce(
    (acc, permission) => {
      if (!acc[permission.category]) {
        acc[permission.category] = [];
      }
      acc[permission.category].push(permission);
      return acc;
    },
    {} as Record<string, Permission[]>
  );

  const categories = Array.from(
    new Set(availablePermissions.map((p) => p.category))
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={getModalTitle()} size="2xl">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Role Badge Preview */}
        <div className="flex justify-center">
          <div
            className={`inline-flex px-4 py-2 text-sm font-semibold rounded-full ${
              selectedRoleColor?.class ||
              "bg-blue-100 text-blue-800 border-blue-200"
            }`}
          >
            <UserGroupIcon className="h-4 w-4 mr-2" />
            {formData.name || "Tên vai trò"}
          </div>
        </div>

        {/* Basic Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <UserGroupIcon className="h-5 w-5 text-blue-600" />
            </div>
            Thông tin vai trò
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Tên vai trò *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                readOnly={isReadOnly}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.name ? "border-red-300 bg-red-50" : "border-gray-300"
                } ${isReadOnly ? "bg-gray-50 cursor-not-allowed" : ""}`}
                placeholder="Nhập tên vai trò"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  {errors.name}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="color"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Màu sắc
              </label>
              <select
                id="color"
                name="color"
                value={formData.color}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  isReadOnly ? "bg-gray-50 cursor-not-allowed" : ""
                }`}
              >
                {ROLE_COLORS.map((color) => (
                  <option key={color.value} value={color.value}>
                    {color.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Mô tả *
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              value={formData.description}
              onChange={handleInputChange}
              readOnly={isReadOnly}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                errors.description
                  ? "border-red-300 bg-red-50"
                  : "border-gray-300"
              } ${isReadOnly ? "bg-gray-50 cursor-not-allowed" : ""}`}
              placeholder="Mô tả chi tiết về vai trò này"
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                {errors.description}
              </p>
            )}
          </div>
        </div>

        {/* Permissions Selection */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-semibold text-gray-900 flex items-center">
              <div className="p-2 bg-green-100 rounded-lg mr-3">
                <ShieldCheckIcon className="h-5 w-5 text-green-600" />
              </div>
              Phân quyền ({formData.permissions.length} đã chọn)
            </h4>
          </div>

          {/* Search and Filter */}
          {!isReadOnly && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <input
                  type="text"
                  placeholder="Tìm kiếm quyền..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Tất cả danh mục</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category === "User"
                        ? "Quản lý người dùng"
                        : category === "Role"
                        ? "Quản lý vai trò"
                        : category === "Folder/File"
                        ? "Quản lý tệp tin"
                        : category === "user"
                        ? "Quản lý người dùng"
                        : category === "file"
                        ? "Quản lý file"
                        : category === "system"
                        ? "Quản lý hệ thống"
                        : category === "report"
                        ? "Báo cáo"
                        : category}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}

          {/* Permissions List */}
          <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
            {Object.entries(permissionsByCategory).map(
              ([category, permissions]) => (
                <div
                  key={category}
                  className="border-b border-gray-200 last:border-b-0"
                >
                  <div className="bg-gray-50 px-4 py-2 text-sm font-medium text-gray-700">
                    {category === "User"
                      ? "Quản lý người dùng"
                      : category === "Role"
                      ? "Quản lý vai trò"
                      : category === "Folder/File"
                      ? "Quản lý tệp tin"
                      : category === "user"
                      ? "Quản lý người dùng"
                      : category === "file"
                      ? "Quản lý file"
                      : category === "system"
                      ? "Quản lý hệ thống"
                      : category === "report"
                      ? "Báo cáo"
                      : category}
                  </div>
                  <div className="p-2 space-y-2">
                    {permissions.map((permission) => (
                      <div
                        key={permission.id}
                        className={`flex items-start space-x-3 p-2 rounded cursor-pointer transition-colors ${
                          formData.permissions.includes(permission.id)
                            ? "bg-blue-50 border border-blue-200"
                            : "hover:bg-gray-50"
                        } ${isReadOnly ? "cursor-not-allowed opacity-75" : ""}`}
                        onClick={() => handlePermissionToggle(permission.id)}
                      >
                        <div
                          className={`w-5 h-5 rounded border-2 flex items-center justify-center mt-0.5 ${
                            formData.permissions.includes(permission.id)
                              ? "border-blue-500 bg-blue-500"
                              : "border-gray-300"
                          }`}
                        >
                          {formData.permissions.includes(permission.id) && (
                            <CheckCircleIcon className="h-3 w-3 text-white" />
                          )}
                        </div>
                        <div className="flex-1">
                          <h5 className="text-sm font-medium text-gray-900">
                            {permission.name}
                          </h5>
                          <p className="text-xs text-gray-500 mt-1">
                            {permission.description}
                          </p>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {permission.actions.map((action) => (
                              <span
                                key={action}
                                className="inline-flex px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded"
                              >
                                {action}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            )}
          </div>
          {errors.permissions && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              {errors.permissions}
            </p>
          )}
        </div>

        {/* Status */}
        {!isReadOnly && (
          <div className="flex items-center">
            <input
              id="isActive"
              name="isActive"
              type="checkbox"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
              Kích hoạt vai trò này
            </label>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {isReadOnly ? "Đóng" : "Hủy"}
          </button>
          {!isReadOnly && (
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>
                    {mode === "add" ? "Đang tạo..." : "Đang cập nhật..."}
                  </span>
                </>
              ) : (
                <>
                  <ModalIcon className="h-4 w-4" />
                  <span>{mode === "add" ? "Tạo vai trò" : "Cập nhật"}</span>
                </>
              )}
            </button>
          )}
        </div>
      </form>
    </Modal>
  );
}
