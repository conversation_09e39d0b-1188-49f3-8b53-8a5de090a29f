"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  XMarkIcon,
  CloudIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  PlayIcon,
  StopIcon,
} from "@heroicons/react/24/outline";
import { SyncService } from "@/api/services/syncService";
import { GoogleDriveService } from "@/api/services/googleDriveService";
import { ApiClient } from "@/api/core/apiClient";
import { useAuth } from "@/contexts/AuthContext";
import { showToast } from "@/components/ui/ToastProvider";
import { SyncStatusDto, SyncStatus } from "@/api/types/interfaces";

interface SyncModalProps {
  isOpen: boolean;
  onClose: () => void;
  fileId?: string;
  folderId?: string;
}

interface SyncState {
  syncStatuses: SyncStatusDto[];
  googleDriveFiles: any[];
  loading: boolean;
  error: string | null;
  syncing: boolean;
  syncProgress: {
    current: number;
    total: number;
    currentFile?: string;
  };
}

export function SyncModal({
  isOpen,
  onClose,
  fileId,
  folderId,
}: SyncModalProps) {
  const { user } = useAuth();
  const [state, setState] = useState<SyncState>({
    syncStatuses: [],
    googleDriveFiles: [],
    loading: false,
    error: null,
    syncing: false,
    syncProgress: {
      current: 0,
      total: 0,
    },
  });

  // Initialize API client and services
  const apiClient = React.useMemo(
    () =>
      new ApiClient(
        process.env.NEXT_PUBLIC_API_BASE_URL || "https://localhost:7040",
        user?.access_token || ""
      ),
    [user?.access_token]
  );

  const syncService = React.useMemo(
    () => new SyncService(apiClient),
    [apiClient]
  );

  const googleDriveService = React.useMemo(
    () => new GoogleDriveService(apiClient),
    [apiClient]
  );

  // Load sync status
  const loadSyncStatus = useCallback(async () => {
    if (!user?.access_token) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const statuses = await syncService.getSyncStatus({
        fileId: fileId,
        provider: "GoogleDrive",
      });

      setState((prev) => ({
        ...prev,
        syncStatuses: statuses || [],
        loading: false,
      }));
    } catch (error: any) {
      console.error("Failed to load sync status:", error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error: "Không thể tải trạng thái đồng bộ. Vui lòng thử lại.",
      }));
    }
  }, [user?.access_token, syncService, fileId]);

  // Load Google Drive files
  const loadGoogleDriveFiles = useCallback(async () => {
    if (!user?.access_token) return;

    try {
      const files = await googleDriveService.getFiles();
      setState((prev) => ({
        ...prev,
        googleDriveFiles: files || [],
      }));
    } catch (error: any) {
      console.error("Failed to load Google Drive files:", error);
    }
  }, [user?.access_token, googleDriveService]);

  // Load data when modal opens
  useEffect(() => {
    if (isOpen && user?.access_token) {
      loadSyncStatus();
      loadGoogleDriveFiles();
    }
  }, [isOpen, user?.access_token, loadSyncStatus, loadGoogleDriveFiles]);

  // Trigger sync
  const handleTriggerSync = async (forceSync: boolean = false) => {
    if (!user?.access_token) return;

    setState((prev) => ({ ...prev, syncing: true, error: null }));

    try {
      await syncService.triggerGoogleDriveSync({
        fileId: fileId,
        forceSync,
      });

      showToast.success("Đã bắt đầu đồng bộ!");
      
      // Reload sync status after a short delay
      setTimeout(() => {
        loadSyncStatus();
        setState((prev) => ({ ...prev, syncing: false }));
      }, 2000);
    } catch (error: any) {
      console.error("Failed to trigger sync:", error);
      setState((prev) => ({
        ...prev,
        syncing: false,
        error: "Không thể bắt đầu đồng bộ. Vui lòng thử lại.",
      }));
      showToast.error("Không thể bắt đầu đồng bộ. Vui lòng thử lại.");
    }
  };

  // Move file to/from Google Drive
  const handleMoveFile = async (fileId: string, direction: "to-gdrive" | "from-gdrive", targetFolderId?: string) => {
    if (!user?.access_token) return;

    setState((prev) => ({ ...prev, syncing: true, error: null }));

    try {
      await googleDriveService.moveFile({
        fileId,
        direction,
        targetFolderId,
      });

      showToast.success(
        direction === "to-gdrive" 
          ? "Đã chuyển file lên Google Drive!" 
          : "Đã tải file về từ Google Drive!"
      );
      
      // Reload data
      loadSyncStatus();
      loadGoogleDriveFiles();
      setState((prev) => ({ ...prev, syncing: false }));
    } catch (error: any) {
      console.error("Failed to move file:", error);
      setState((prev) => ({
        ...prev,
        syncing: false,
        error: "Không thể di chuyển file. Vui lòng thử lại.",
      }));
      showToast.error("Không thể di chuyển file. Vui lòng thử lại.");
    }
  };

  // Format sync status
  const formatSyncStatus = (status: SyncStatus) => {
    switch (status) {
      case SyncStatus.Pending:
        return { text: "Chờ đồng bộ", color: "text-yellow-600", icon: ClockIcon };
      case SyncStatus.Syncing:
        return { text: "Đang đồng bộ", color: "text-blue-600", icon: ArrowPathIcon };
      case SyncStatus.Synced:
        return { text: "Đã đồng bộ", color: "text-green-600", icon: CheckCircleIcon };
      case SyncStatus.Failed:
        return { text: "Lỗi đồng bộ", color: "text-red-600", icon: ExclamationTriangleIcon };
      default:
        return { text: "Không xác định", color: "text-gray-600", icon: ClockIcon };
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(dateString));
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <CloudIcon className="w-6 h-6 text-blue-500" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Đồng Bộ Google Drive
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Quản lý đồng bộ hóa với Google Drive
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>

          {/* Sync Actions */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex gap-3">
              <button
                onClick={() => handleTriggerSync(false)}
                disabled={state.syncing}
                className="btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <PlayIcon className="w-4 h-4" />
                {state.syncing ? "Đang đồng bộ..." : "Bắt đầu đồng bộ"}
              </button>
              <button
                onClick={() => handleTriggerSync(true)}
                disabled={state.syncing}
                className="btn-secondary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowPathIcon className="w-4 h-4" />
                Đồng bộ cưỡng chế
              </button>
              <button
                onClick={loadSyncStatus}
                disabled={state.loading}
                className="btn-secondary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowPathIcon className="w-4 h-4" />
                Làm mới
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto">
            {state.loading ? (
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-3 text-gray-600 dark:text-gray-400">Đang tải...</span>
              </div>
            ) : state.error ? (
              <div className="p-8 text-center">
                <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
                <p className="text-red-600 dark:text-red-400">{state.error}</p>
                <button
                  onClick={loadSyncStatus}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Thử lại
                </button>
              </div>
            ) : (
              <div className="p-6">
                {/* Sync Status Section */}
                <div className="mb-8">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Trạng Thái Đồng Bộ
                  </h3>
                  {state.syncStatuses.length === 0 ? (
                    <div className="text-center py-8">
                      <CloudIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 dark:text-gray-400">Chưa có dữ liệu đồng bộ</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {state.syncStatuses.map((status, index) => {
                        const statusInfo = formatSyncStatus(status.status);
                        const StatusIcon = statusInfo.icon;

                        return (
                          <div
                            key={index}
                            className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                          >
                            <div className="flex items-center gap-3">
                              <StatusIcon className={`w-6 h-6 ${statusInfo.color}`} />
                              <div>
                                <div className="font-medium text-gray-900 dark:text-white">
                                  {status.fileName || status.fileId}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                  Provider: {status.provider}
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className={`font-medium ${statusInfo.color}`}>
                                {statusInfo.text}
                              </div>
                              {status.lastSyncAt && (
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                  {formatDate(status.lastSyncAt)}
                                </div>
                              )}
                              {status.errorMessage && (
                                <div className="text-sm text-red-600 dark:text-red-400">
                                  {status.errorMessage}
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>

                {/* Google Drive Files Section */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Files trên Google Drive
                  </h3>
                  {state.googleDriveFiles.length === 0 ? (
                    <div className="text-center py-8">
                      <CloudIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 dark:text-gray-400">Không có file nào trên Google Drive</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {state.googleDriveFiles.slice(0, 10).map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            <CloudIcon className="w-6 h-6 text-blue-500" />
                            <div>
                              <div className="font-medium text-gray-900 dark:text-white">
                                {file.name}
                              </div>
                              <div className="text-sm text-gray-600 dark:text-gray-400">
                                {file.mimeType} • {file.size ? `${Math.round(file.size / 1024)} KB` : 'Unknown size'}
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <button
                              onClick={() => handleMoveFile(file.id, "from-gdrive")}
                              disabled={state.syncing}
                              className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                              title="Tải về từ Google Drive"
                            >
                              Tải về
                            </button>
                          </div>
                        </div>
                      ))}
                      {state.googleDriveFiles.length > 10 && (
                        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
                          Và {state.googleDriveFiles.length - 10} file khác...
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Sync Progress */}
          {state.syncing && (
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20">
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-blue-900 dark:text-blue-100">
                    Đang đồng bộ...
                  </div>
                  {state.syncProgress.currentFile && (
                    <div className="text-xs text-blue-700 dark:text-blue-300">
                      {state.syncProgress.currentFile}
                    </div>
                  )}
                </div>
                {state.syncProgress.total > 0 && (
                  <div className="text-sm text-blue-700 dark:text-blue-300">
                    {state.syncProgress.current}/{state.syncProgress.total}
                  </div>
                )}
              </div>
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
