import React from 'react';
import { useAlertModal } from './AlertModal';
import { showToast } from '../ui/ToastProvider';

/**
 * Example component showing how to use AlertModal and Toast notifications
 * instead of browser alert() calls
 */
export function ExampleAlertUsage() {
  const { showAlert, AlertModal } = useAlertModal();

  // Example 1: Simple info alert (can use toast instead)
  const handleSimpleInfo = () => {
    showToast.info('This is a simple information message');
  };

  // Example 2: Success notification (typically use toast)
  const handleSuccess = () => {
    showToast.success('Operation completed successfully!');
  };

  // Example 3: Error notification (typically use toast)
  const handleError = () => {
    showToast.error('Something went wrong. Please try again.');
  };

  // Example 4: Warning that needs user attention (can use modal)
  const handleWarningModal = () => {
    showAlert({
      type: 'warning',
      title: 'Important Warning',
      message: 'This action cannot be undone. Are you sure you want to continue?',
      confirmText: 'Yes, Continue',
      cancelText: 'Cancel',
      showCancel: true,
      onConfirm: () => {
        showToast.success('Action confirmed!');
      }
    });
  };

  // Example 5: Error that needs user acknowledgment (use modal)
  const handleCriticalError = () => {
    showAlert({
      type: 'error',
      title: 'Critical Error',
      message: 'A critical error occurred while processing your request. Please contact support if this continues.',
      confirmText: 'I Understand',
    });
  };

  // Example 6: Multi-line validation errors (use modal for better readability)
  const handleValidationErrors = () => {
    const errors = [
      'File size must be less than 50MB',
      'Only PDF files are allowed',
      'File name cannot contain special characters'
    ];
    
    showAlert({
      type: 'warning',
      title: 'Validation Errors',
      message: errors.join('\n'),
      confirmText: 'OK'
    });
  };

  // Example 7: Download started (use toast)
  const handleDownload = () => {
    showToast.success('Download started successfully!');
  };

  return (
    <div className="p-6 space-y-4 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
        Alert & Toast Examples
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Toast Notifications (Recommended)
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Use for simple, non-blocking notifications
          </p>
          
          <button
            onClick={handleSimpleInfo}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Show Info Toast
          </button>
          
          <button
            onClick={handleSuccess}
            className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            Show Success Toast
          </button>
          
          <button
            onClick={handleError}
            className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Show Error Toast
          </button>
          
          <button
            onClick={handleDownload}
            className="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
          >
            Download Started
          </button>
        </div>
        
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Modal Alerts
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Use when user interaction or detailed info is needed
          </p>
          
          <button
            onClick={handleWarningModal}
            className="w-full px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors"
          >
            Warning with Confirmation
          </button>
          
          <button
            onClick={handleCriticalError}
            className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Critical Error Modal
          </button>
          
          <button
            onClick={handleValidationErrors}
            className="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
          >
            Validation Errors
          </button>
        </div>
      </div>
      
      <div className="mt-8 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
          Guidelines:
        </h4>
        <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          <li>• Use <strong>toasts</strong> for simple notifications (success, error, info)</li>
          <li>• Use <strong>modals</strong> when user interaction is required</li>
          <li>• Use <strong>modals</strong> for complex error messages or validation</li>
          <li>• Use <strong>modals</strong> for warnings that need confirmation</li>
          <li>• Never use browser <code>alert()</code> - it blocks the UI and looks unprofessional</li>
        </ul>
      </div>
      
      {/* This renders the modal when showAlert is called */}
      <AlertModal />
    </div>
  );
} 