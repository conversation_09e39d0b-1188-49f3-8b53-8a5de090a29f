"use client";

import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { UnauthorizedMessage } from "@/components/ui/UnauthorizedMessage";

interface RoleGuardProps {
  requiredRole?: string;
  requiredRoles?: string[];
  requireAll?: boolean; // true = must have ALL roles, false = must have ANY role
  fallback?: React.ReactNode;
  showError?: boolean;
  children: React.ReactNode;
}

/**
 * RoleGuard component để protect UI elements dựa trên user roles
 * 
 * @param requiredRole - Single role required (e.g., "Admin")
 * @param requiredRoles - Array of roles (e.g., ["Admin", "Manager"])
 * @param requireAll - If true, user must have ALL roles. If false, user must have ANY role (default: false)
 * @param fallback - Custom component to show when access denied
 * @param showError - Show error message when access denied (default: true)
 * @param children - Content to protect
 */
export const RoleGuard: React.FC<RoleGuardProps> = ({ 
  requiredRole,
  requiredRoles,
  requireAll = false,
  fallback,
  showError = true,
  children 
}) => {
  const { isAuthenticated, hasRole, hasAnyRole, getUserRoles } = useAuth();

  // Determine which roles to check
  const rolesToCheck = requiredRoles || (requiredRole ? [requiredRole] : []);
  
  if (rolesToCheck.length === 0) {
    console.warn('RoleGuard: No roles specified');
    return <>{children}</>;
  }

  // Check if user is authenticated
  if (!isAuthenticated) {
    if (fallback) return <>{fallback}</>;
    if (!showError) return null;
    
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">
          ⚠️ Authentication required to access this content
        </p>
      </div>
    );
  }

  // Check roles
  let hasAccess = false;
  
  if (requireAll) {
    // User must have ALL specified roles
    hasAccess = rolesToCheck.every(role => hasRole(role));
  } else {
    // User must have ANY of the specified roles
    hasAccess = hasAnyRole(rolesToCheck);
  }

  if (hasAccess) {
    return <>{children}</>;
  }

  // Access denied
  if (fallback) {
    return <>{fallback}</>;
  }

  if (!showError) {
    return null;
  }

  const userRoles = getUserRoles();
  
  return (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            Access Denied
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <p>
              {requireAll 
                ? `You need ALL of these roles: ${rolesToCheck.join(', ')}`
                : `You need at least ONE of these roles: ${rolesToCheck.join(', ')}`
              }
            </p>
            {userRoles.length > 0 ? (
              <p className="mt-1">Your current roles: {userRoles.join(', ')}</p>
            ) : (
              <p className="mt-1">You have no roles assigned.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Convenience components for common roles
export const AdminOnly: React.FC<Omit<RoleGuardProps, 'requiredRole'>> = (props) => (
  <RoleGuard {...props} requiredRole="Admin" />
);

export const ManagerOrAdmin: React.FC<Omit<RoleGuardProps, 'requiredRoles'>> = (props) => (
  <RoleGuard {...props} requiredRoles={["Manager", "Admin"]} />
);

export const UserOrAbove: React.FC<Omit<RoleGuardProps, 'requiredRoles'>> = (props) => (
  <RoleGuard {...props} requiredRoles={["User", "Manager", "Admin"]} />
);

// Hook for imperative role checking in components
export const useRoleGuard = () => {
  const { hasRole, hasAnyRole, getUserRoles } = useAuth();
  
  return {
    checkRole: (role: string) => hasRole(role),
    checkAnyRole: (roles: string[]) => hasAnyRole(roles),
    checkAllRoles: (roles: string[]) => roles.every(role => hasRole(role)),
    getCurrentRoles: () => getUserRoles(),
  };
};

// Utility component for checking a single role
interface SingleRoleGuardProps {
  children: React.ReactNode;
  requiredRole: string;
  fallback?: React.ReactNode;
  showUnauthorized?: boolean;
}

export const SingleRoleGuard: React.FC<SingleRoleGuardProps> = ({
  children,
  requiredRole,
  fallback,
  showUnauthorized = true,
}) => {
  return (
    <RoleGuard
      requiredRoles={[requiredRole]}
      fallback={fallback}
      showError={false}
    >
      {children}
    </RoleGuard>
  );
};

// Utility component for admin-only content
interface AdminGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUnauthorized?: boolean;
}

export const AdminGuard: React.FC<AdminGuardProps> = ({
  children,
  fallback,
  showUnauthorized = true,
}) => {
  return (
    <RoleGuard
      requiredRoles={["admin", "administrator"]}
      requireAll={false}
      fallback={fallback}
      showError={false}
    >
      {children}
    </RoleGuard>
  );
}; 