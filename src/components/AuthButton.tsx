"use client";
import React from "react";
import { useAuth } from "@/contexts/AuthContext";

const AuthButton: React.FC = () => {
  const { 
    isAuthenticated, 
    isLoading, 
    user, 
    userProfile, 
    isLoadingProfile, 
    login, 
    logout, 
    error,
    getUserRoles,
    isTokenExpired,
    clearAuthState
  } = useAuth();

  if (isLoading) {
    return (
      <button
        className="px-4 py-2 rounded bg-gray-300 text-gray-700 cursor-not-allowed"
        disabled
        aria-busy="true"
        aria-label="Checking authentication status"
      >
        Checking...
      </button>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col gap-2">
        <div className="text-red-600 text-sm" role="alert">
          Authentication error: {error.message}
        </div>
        <div className="flex gap-2">
          <button
            className="px-3 py-1 text-xs rounded bg-red-500 text-white hover:bg-red-600"
            onClick={() => {
              clearAuthState();
              login();
            }}
            aria-label="Retry authentication"
          >
            Retry
          </button>
          <button
            className="px-3 py-1 text-xs rounded bg-gray-500 text-white hover:bg-gray-600"
            onClick={clearAuthState}
            aria-label="Clear authentication state"
          >
            Clear State
          </button>
        </div>
      </div>
    );
  }

  if (isAuthenticated && user) {
    // Use userProfile if available, fallback to OIDC user profile
    const displayName = userProfile?.name || userProfile?.firstName || user.profile?.name || user.profile?.email;
    const displayEmail = userProfile?.email || user.profile?.email;
    const userRoles = getUserRoles();
    const tokenExpired = isTokenExpired();

    return (
      <div className="flex items-center gap-2">
        {/* Loading indicator for profile */}
        {isLoadingProfile && (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        )}
        
        {/* User information */}
        <div className="flex flex-col">
          <span
            className="text-sm text-gray-700"
            aria-label={`Hello ${displayName || displayEmail}`}
          >
            Hello, {displayName || displayEmail}
          </span>
          
          {/* Show roles in development mode */}
          {process.env.NODE_ENV === "development" && userRoles.length > 0 && (
            <span className="text-xs text-gray-500">
              Roles: {userRoles.join(", ")}
            </span>
          )}
          
          {/* Show token expiration warning */}
          {tokenExpired && (
            <span className="text-xs text-orange-500">
              Token expired - please log in again
            </span>
          )}
        </div>

        {/* Logout button */}
        <button
          className="px-4 py-2 rounded bg-red-500 text-white hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400"
          onClick={logout}
          aria-label="Sign out"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              logout();
            }
          }}
        >
          Sign Out
        </button>
      </div>
    );
  }

  return (
    <button
      className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400"
      onClick={login}
      aria-label="Sign in with SSO"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          login();
        }
      }}
    >
      Sign In with SSO
    </button>
  );
};

export default AuthButton;
