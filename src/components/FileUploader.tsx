import React, { useState, useRef, useEffect } from "react";
import VeasyFileManagerAPI from "../api";
import { useFileUpload } from "../api/hooks/useFileUpload";
import { FileDto, UploadOptions } from "../api/types/interfaces";
import { getUserFriendlyErrorMessage, isValidationError, isApiError } from "../api";

interface FileUploaderProps {
  api: VeasyFileManagerAPI;
  onUploadComplete?: (files: FileDto[]) => void;
  onUploadError?: (error: Error) => void;
  parentFolderId?: string;
  acceptedFileTypes?: string;
  maxFileSize?: number; // In bytes
  maxFiles?: number;
  multiple?: boolean;
  className?: string;
  buttonText?: string;
  showFileList?: boolean;
  autoUpload?: boolean;
  enableChunkedUpload?: boolean;
  chunkSize?: number;
  enableDragDrop?: boolean;
}

export const FileUploader: React.FC<FileUploaderProps> = ({
  api,
  onUploadComplete,
  onUploadError,
  parentFolderId,
  acceptedFileTypes,
  maxFileSize = 100 * 1024 * 1024, // 100MB default
  maxFiles = 10,
  multiple = false,
  className = "",
  buttonText = "Select Files",
  showFileList = true,
  autoUpload = true,
  enableChunkedUpload = true,
  chunkSize = 10 * 1024 * 1024, // 10MB chunks
  enableDragDrop = true,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<FileDto[]>([]);
  const [errors, setErrors] = useState<{ fileName: string; message: string }[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Use our custom upload hook with enhanced options
  const {
    upload,
    uploadMultiple,
    chunkedUpload,
    progress,
    isUploading,
    error,
    cancelUpload,
  } = useFileUpload(api, {
    onUploadComplete: (file) => {
      setUploadedFiles((prev) => [...prev, file]);
      onUploadComplete?.([...uploadedFiles, file]);
    },
    onUploadError: (error) => {
      // Enhanced error handling
      const friendlyMessage = getUserFriendlyErrorMessage(error);
      setErrors(prev => [...prev, { fileName: "Upload Error", message: friendlyMessage }]);
      onUploadError?.(error);
    },
    autoChunkedThreshold: enableChunkedUpload ? maxFileSize / 2 : Infinity,
  });

  // Enhanced file validation
  const validateFile = (file: File): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Size validation
    if (file.size > maxFileSize) {
      errors.push(`File size exceeds maximum allowed (${formatFileSize(maxFileSize)})`);
    }

    if (file.size === 0) {
      errors.push("File cannot be empty");
    }

    // Enhanced file type validation
    if (acceptedFileTypes) {
      const isValidType = validateFileType(file, acceptedFileTypes);
      if (!isValidType) {
        errors.push("File type not allowed");
      }
    }

    // Security validation - block dangerous file types
    const dangerousExtensions = ['.exe', '.bat', '.cmd', '.com', '.scr', '.vbs', '.js', '.jar'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    if (dangerousExtensions.includes(fileExtension)) {
      errors.push(`File type ${fileExtension} is not allowed for security reasons`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  // Enhanced file type validation
  const validateFileType = (file: File, acceptedTypes: string): boolean => {
    const fileTypes = acceptedTypes.split(",").map(type => type.trim());
    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    const mimeType = file.type;

    return fileTypes.some((type) => {
      if (type.startsWith(".")) {
        // Extension check
        return `.${fileExtension}` === type.toLowerCase();
      } else if (type.includes("/")) {
        // MIME type check
        return mimeType.match(new RegExp(type.replace("*", ".*")));
      } else {
        // Fallback extension check without dot
        return fileExtension === type.toLowerCase();
      }
    });
  };

  // Automatically upload files when selected if autoUpload is true
  useEffect(() => {
    if (autoUpload && selectedFiles.length > 0 && !isUploading) {
      handleUpload();
    }
  }, [selectedFiles, autoUpload, isUploading]);

  // Process selected files with enhanced validation
  const processFiles = (fileList: FileList) => {
    const newFiles: File[] = [];
    const newErrors: { fileName: string; message: string }[] = [];

    // Process each selected file
    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const validation = validateFile(file);

      if (!validation.isValid) {
        validation.errors.forEach(error => {
          newErrors.push({
            fileName: file.name,
            message: error
          });
        });
        continue;
      }

      // Add valid file to list
      newFiles.push(file);
    }

    // Check maximum number of files
    if (selectedFiles.length + newFiles.length > maxFiles) {
      newErrors.push({
        fileName: "Multiple files",
        message: `Cannot upload more than ${maxFiles} files at once`,
      });

      // Only take the first N files that would bring us to the max
      const allowedNewFiles = newFiles.slice(0, maxFiles - selectedFiles.length);
      setSelectedFiles((prev) => [...prev, ...allowedNewFiles]);
    } else {
      setSelectedFiles((prev) => [...prev, ...newFiles]);
    }

    // Set any validation errors
    if (newErrors.length > 0) {
      setErrors((prev) => [...prev, ...newErrors]);
    }
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = event.target.files;
    if (!fileList) return;

    processFiles(fileList);

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Drag and drop handlers
  const handleDragOver = (event: React.DragEvent) => {
    if (!enableDragDrop) return;
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    if (!enableDragDrop) return;
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    if (!enableDragDrop) return;
    event.preventDefault();
    setIsDragOver(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      processFiles(files);
    }
  };

  // Enhanced upload handling with chunked upload support
  const handleUpload = async () => {
    if (selectedFiles.length === 0 || isUploading) return;

    setErrors([]);

    try {
      const uploadOptions: UploadOptions = {
        parentFolderId,
        syncToGoogleDrive: true,
        tags: [], // Could be configurable
      };

      let results: FileDto[];

      if (selectedFiles.length === 1) {
        const file = selectedFiles[0];

        // Use chunked upload for large files
        if (enableChunkedUpload && file.size > chunkSize * 2) {
          const result = await chunkedUpload(file, {
            ...uploadOptions,
            chunkSize,
            onProgress: (progressData) => {
              // Progress is already handled by the hook
            }
          });
          results = [result];
        } else {
          const result = await upload(file, uploadOptions);
          results = [result];
        }
      } else {
        // Multiple files upload - handle each individually for better progress tracking
        results = [];
        for (const file of selectedFiles) {
          try {
            let result: FileDto;
            if (enableChunkedUpload && file.size > chunkSize * 2) {
              result = await chunkedUpload(file, {
                ...uploadOptions,
                chunkSize
              });
            } else {
              result = await upload(file, uploadOptions);
            }
            results.push(result);
          } catch (fileError) {
            const friendlyMessage = getUserFriendlyErrorMessage(fileError);
            setErrors(prev => [...prev, {
              fileName: file.name,
              message: friendlyMessage
            }]);
          }
        }
      }

      setUploadedFiles((prev) => [...prev, ...results]);
      setSelectedFiles([]);

      if (results.length > 0) {
        onUploadComplete?.(results);
      }
    } catch (err) {
      const friendlyMessage = getUserFriendlyErrorMessage(err);
      setErrors(prev => [...prev, { fileName: "Upload Error", message: friendlyMessage }]);
      onUploadError?.(err as Error);
    }
  };

  // Handle file removal from the list
  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // Clear errors
  const clearErrors = () => {
    setErrors([]);
  };

  // Format file size for display
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  };

  return (
    <div className={`file-uploader ${className}`}>
      {/* Enhanced upload area with drag and drop */}
      <div
        className={`file-uploader__upload-area ${isDragOver ? 'file-uploader__upload-area--drag-over' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        style={{
          border: '2px dashed #ccc',
          borderRadius: '8px',
          padding: '20px',
          textAlign: 'center',
          backgroundColor: isDragOver ? '#f0f8ff' : '#fafafa',
          transition: 'all 0.3s ease',
          cursor: enableDragDrop ? 'pointer' : 'default'
        }}
      >
        <div className="file-uploader__input">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileSelect}
            accept={acceptedFileTypes}
            multiple={multiple}
            disabled={isUploading}
            className="file-uploader__input-field"
            id="file-upload-input"
            aria-label="File upload input"
            tabIndex={0}
            style={{ display: 'none' }}
          />
          <label
            htmlFor="file-upload-input"
            className="file-uploader__button"
            style={{
              display: 'inline-block',
              padding: '10px 20px',
              backgroundColor: '#4CAF50',
              color: 'white',
              borderRadius: '4px',
              cursor: 'pointer',
              border: 'none',
              fontSize: '16px'
            }}
          >
            {buttonText}
          </label>

          {enableDragDrop && (
            <p style={{ margin: '10px 0 0 0', color: '#666', fontSize: '14px' }}>
              or drag and drop files here
            </p>
          )}

          {!autoUpload && selectedFiles.length > 0 && (
            <button
              onClick={handleUpload}
              disabled={isUploading}
              className="file-uploader__upload-button"
              aria-label="Upload files"
              tabIndex={0}
              style={{
                marginLeft: '10px',
                padding: '10px 20px',
                backgroundColor: '#2196F3',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: isUploading ? 'not-allowed' : 'pointer'
              }}
            >
              {isUploading ? "Uploading..." : "Upload"}
            </button>
          )}

          {isUploading && (
            <button
              onClick={cancelUpload}
              className="file-uploader__cancel-button"
              aria-label="Cancel upload"
              tabIndex={0}
              style={{
                marginLeft: '10px',
                padding: '10px 20px',
                backgroundColor: '#f44336',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
          )}
        </div>

        {/* Upload limits info */}
        <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
          Max file size: {formatFileSize(maxFileSize)} | Max files: {maxFiles}
          {acceptedFileTypes && <><br/>Accepted types: {acceptedFileTypes}</>}
        </div>
      </div>

      {/* Enhanced progress bar */}
      {isUploading && (
        <div className="file-uploader__progress" style={{ marginTop: '15px' }}>
          <div
            style={{
              width: '100%',
              height: '20px',
              backgroundColor: '#e0e0e0',
              borderRadius: '10px',
              overflow: 'hidden',
              position: 'relative'
            }}
          >
            <div
              className="file-uploader__progress-bar"
              style={{
                width: `${progress}%`,
                height: '100%',
                backgroundColor: '#4CAF50',
                transition: 'width 0.3s ease'
              }}
              aria-valuemin={0}
              aria-valuemax={100}
              aria-valuenow={progress}
              role="progressbar"
            />
            <span
              className="file-uploader__progress-text"
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                fontSize: '12px',
                fontWeight: 'bold',
                color: progress > 50 ? 'white' : 'black'
              }}
            >
              {progress.toFixed(0)}%
            </span>
          </div>
        </div>
      )}

      {/* Enhanced error display */}
      {errors.length > 0 && (
        <div className="file-uploader__errors" style={{ marginTop: '15px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h4 style={{ margin: 0, color: '#f44336' }}>Errors ({errors.length}):</h4>
            <button
              onClick={clearErrors}
              style={{
                padding: '4px 8px',
                backgroundColor: 'transparent',
                border: '1px solid #f44336',
                color: '#f44336',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: 'pointer'
              }}
            >
              Clear
            </button>
          </div>
          <ul className="file-uploader__error-list" style={{ margin: '10px 0', padding: 0 }}>
            {errors.map((error, index) => (
              <li
                key={index}
                className="file-uploader__error-item"
                style={{
                  listStyle: 'none',
                  padding: '8px',
                  marginBottom: '4px',
                  backgroundColor: '#ffebee',
                  border: '1px solid #ffcdd2',
                  borderRadius: '4px',
                  color: '#c62828'
                }}
              >
                <strong>{error.fileName}</strong>: {error.message}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Rest of the component remains the same... */}
      {showFileList && selectedFiles.length > 0 && (
        <div className="file-uploader__selected-files" style={{ marginTop: '15px' }}>
          <h4>Selected Files ({selectedFiles.length}):</h4>
          <ul className="file-uploader__file-list" style={{ margin: 0, padding: 0 }}>
            {selectedFiles.map((file, index) => (
              <li
                key={index}
                className="file-uploader__file-item"
                style={{
                  listStyle: 'none',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px',
                  marginBottom: '4px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '4px'
                }}
              >
                <div>
                  <span className="file-uploader__file-name">{file.name}</span>
                  <span className="file-uploader__file-size" style={{ color: '#666', marginLeft: '8px' }}>
                    ({formatFileSize(file.size)})
                  </span>
                </div>
                <button
                  onClick={() => handleRemoveFile(index)}
                  className="file-uploader__file-remove"
                  disabled={isUploading}
                  aria-label={`Remove ${file.name}`}
                  tabIndex={0}
                  style={{
                    backgroundColor: '#f44336',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    width: '24px',
                    height: '24px',
                    cursor: isUploading ? 'not-allowed' : 'pointer',
                    fontSize: '12px'
                  }}
                >
                  ✕
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}

      {showFileList && uploadedFiles.length > 0 && (
        <div className="file-uploader__uploaded-files" style={{ marginTop: '15px' }}>
          <h4>Uploaded Files ({uploadedFiles.length}):</h4>
          <ul className="file-uploader__file-list" style={{ margin: 0, padding: 0 }}>
            {uploadedFiles.map((file, index) => (
              <li
                key={index}
                className="file-uploader__file-item file-uploader__file-item--uploaded"
                style={{
                  listStyle: 'none',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px',
                  marginBottom: '4px',
                  backgroundColor: '#e8f5e8',
                  borderRadius: '4px',
                  border: '1px solid #4CAF50'
                }}
              >
                <div>
                  <span className="file-uploader__file-name">
                    {file.displayName || file.name}
                  </span>
                  <span className="file-uploader__file-size" style={{ color: '#666', marginLeft: '8px' }}>
                    ({formatFileSize(file.fileSize)})
                  </span>
                </div>
                <span style={{ color: '#4CAF50', fontWeight: 'bold', fontSize: '12px' }}>
                  ✓ UPLOADED
                </span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
