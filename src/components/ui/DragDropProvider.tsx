"use client";

import React from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { motion } from 'framer-motion';
import { FolderIcon, DocumentIcon } from '@heroicons/react/24/outline';

export interface DragDropItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  parentId?: string;
  [key: string]: any;
}

interface DragDropProviderProps {
  children: React.ReactNode;
  onDragEnd: (result: DropResult) => void;
  isDropDisabled?: boolean;
}

export function DragDropProvider({ 
  children, 
  onDragEnd, 
  isDropDisabled = false 
}: DragDropProviderProps) {
  return (
    <DragDropContext onDragEnd={onDragEnd}>
      {children}
    </DragDropContext>
  );
}

interface DroppableAreaProps {
  droppableId: string;
  children: React.ReactNode;
  className?: string;
  isDropDisabled?: boolean;
}

export function DroppableArea({ 
  droppableId, 
  children, 
  className = '',
  isDropDisabled = false 
}: DroppableAreaProps) {
  return (
    <Droppable droppableId={droppableId} isDropDisabled={isDropDisabled}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.droppableProps}
          className={`
            ${className}
            ${snapshot.isDraggingOver ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-700' : ''}
            transition-colors duration-200
          `}
        >
          {children}
          {provided.placeholder}
        </div>
      )}
    </Droppable>
  );
}

interface DraggableItemProps {
  item: DragDropItem;
  index: number;
  children: React.ReactNode;
  isDragDisabled?: boolean;
  className?: string;
}

export function DraggableItem({ 
  item, 
  index, 
  children, 
  isDragDisabled = false,
  className = '' 
}: DraggableItemProps) {
  return (
    <Draggable 
      draggableId={item.id} 
      index={index} 
      isDragDisabled={isDragDisabled}
    >
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`
            ${className}
            ${snapshot.isDragging ? 'shadow-lg rotate-2 scale-105' : ''}
            ${snapshot.isDragging ? 'z-50' : ''}
            transition-all duration-200
          `}
        >
          {children}
        </div>
      )}
    </Draggable>
  );
}

interface FolderDropZoneProps {
  folderId: string;
  folderName: string;
  isOver?: boolean;
  canDrop?: boolean;
  children?: React.ReactNode;
  className?: string;
}

export function FolderDropZone({ 
  folderId, 
  folderName, 
  isOver = false, 
  canDrop = true,
  children,
  className = '' 
}: FolderDropZoneProps) {
  return (
    <DroppableArea 
      droppableId={folderId}
      className={`
        ${className}
        relative border-2 border-dashed rounded-lg p-4
        ${isOver && canDrop ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600'}
        ${!canDrop ? 'border-red-300 bg-red-50 dark:bg-red-900/20' : ''}
        transition-all duration-200
      `}
    >
      <div className="flex items-center justify-center space-x-2 text-gray-500 dark:text-gray-400">
        <FolderIcon className="w-6 h-6" />
        <span className="text-sm font-medium">
          {isOver 
            ? canDrop 
              ? `Thả vào "${folderName}"` 
              : "Không thể thả vào đây"
            : `Kéo thả vào "${folderName}"`
          }
        </span>
      </div>
      {children}
    </DroppableArea>
  );
}

interface DragPreviewProps {
  item: DragDropItem;
  isDragging: boolean;
}

export function DragPreview({ item, isDragging }: DragPreviewProps) {
  if (!isDragging) return null;

  return (
    <motion.div
      className="fixed top-0 left-0 pointer-events-none z-50"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
    >
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xl p-3 flex items-center space-x-2">
        {item.type === 'folder' ? (
          <FolderIcon className="w-5 h-5 text-blue-500" />
        ) : (
          <DocumentIcon className="w-5 h-5 text-gray-500" />
        )}
        <span className="text-sm font-medium text-gray-900 dark:text-white">
          {item.name}
        </span>
      </div>
    </motion.div>
  );
}

// Hook for drag and drop logic
export function useDragDrop(
  items: DragDropItem[],
  onMove: (itemId: string, newParentId: string | null) => Promise<void>
) {
  const handleDragEnd = async (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // No destination or same position
    if (!destination || 
        (destination.droppableId === source.droppableId && 
         destination.index === source.index)) {
      return;
    }

    const draggedItem = items.find(item => item.id === draggableId);
    if (!draggedItem) return;

    // Prevent dropping folder into itself or its children
    if (draggedItem.type === 'folder' && destination.droppableId === draggableId) {
      return;
    }

    // Check if destination is a child of the dragged folder
    const isChildOfDraggedFolder = (folderId: string, parentId: string): boolean => {
      const folder = items.find(item => item.id === folderId);
      if (!folder || !folder.parentId) return false;
      if (folder.parentId === parentId) return true;
      return isChildOfDraggedFolder(folder.parentId, parentId);
    };

    if (draggedItem.type === 'folder' && 
        isChildOfDraggedFolder(destination.droppableId, draggableId)) {
      return;
    }

    // Determine new parent ID
    const newParentId = destination.droppableId === 'root' ? null : destination.droppableId;

    try {
      await onMove(draggableId, newParentId);
    } catch (error) {
      console.error('Failed to move item:', error);
    }
  };

  return { handleDragEnd };
}

// Utility function to check if drop is allowed
export function canDropItem(
  draggedItem: DragDropItem,
  targetFolderId: string,
  allItems: DragDropItem[]
): boolean {
  // Can't drop folder into itself
  if (draggedItem.type === 'folder' && draggedItem.id === targetFolderId) {
    return false;
  }

  // Can't drop folder into its children
  if (draggedItem.type === 'folder') {
    const isChild = (folderId: string): boolean => {
      const folder = allItems.find(item => item.id === folderId);
      if (!folder || !folder.parentId) return false;
      if (folder.parentId === draggedItem.id) return true;
      return isChild(folder.parentId);
    };

    if (isChild(targetFolderId)) {
      return false;
    }
  }

  return true;
}
