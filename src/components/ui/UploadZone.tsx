"use client";

import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useDropzone } from "react-dropzone";
import { useUpload } from "@/hooks/useUpload";
import { UploadZoneProps } from "@/types";
import { formatFileSize } from "@/lib/utils";
import { FileService } from "@/api/services/fileService";
import { ApiClient } from "@/api/core/apiClient";
import { useAuth } from "@/contexts/AuthContext";
import {
  CloudArrowUpIcon,
  DocumentIcon,
  XMarkIcon,
  ExclamationCircleIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";

export function UploadZone({
  onFilesSelected,
  accept = "application/pdf",
  maxSize = 50 * 1024 * 1024, // 50MB
  multiple = true,
  disabled = false,
}: UploadZoneProps) {
  const { user } = useAuth();
  
  // Don't render if user is not authenticated
  if (!user?.access_token) {
    return (
      <div className="w-full p-8 text-center bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg">
        <p className="text-gray-500">Please log in to upload files</p>
      </div>
    );
  }
  
  // Initialize API client and file service
  const apiClient = new ApiClient(
    process.env.NEXT_PUBLIC_API_BASE_URL || 'https://localhost:7040',
    user.access_token
  );
  const fileService = new FileService(apiClient);

  // State for upload process
  const [uploadQueue, setUploadQueue] = useState<File[]>([]);
  const [errors, setErrors] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {}
  );
  const [successfulUploads, setSuccessfulUploads] = useState<string[]>([]);

  // Memoize the accept types for better performance
  const acceptTypes = useMemo(() => ({ [accept]: [".pdf"] }), [accept]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      // Add new files to queue
      setUploadQueue((prevQueue) => [...prevQueue, ...acceptedFiles]);
    }
  }, []);

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragReject,
    fileRejections,
  } = useDropzone({
    onDrop,
    accept: acceptTypes,
    maxSize,
    multiple,
    disabled: disabled || isUploading,
  });

  // Handle file removal from queue
  const handleRemoveFile = (index: number) => {
    setUploadQueue((prevQueue) => prevQueue.filter((_, i) => i !== index));
  };

  // Clear all errors
  const clearErrors = () => {
    setErrors([]);
  };

  // Clear queue
  const clearQueue = () => {
    setUploadQueue([]);
  };

  // Handle upload of all files in queue
  const handleUpload = async () => {
    if (uploadQueue.length === 0 || isUploading || !user?.access_token) return;

    setIsUploading(true);
    setErrors([]);
    const newSuccessfulUploads: string[] = [];

    for (let i = 0; i < uploadQueue.length; i++) {
      const file = uploadQueue[i];
      const fileId = file.name; // Use name as temporary ID

      try {
        // Set initial progress
        setUploadProgress((prev) => ({ ...prev, [fileId]: 0 }));

        // Upload file using the new file service
        const uploadedFile = await fileService.upload(file, {
          displayName: file.name,
          description: `Uploaded via UploadZone: ${file.name}`,
        });

        // Simulate progress completion since the new API doesn't provide progress callbacks
        setUploadProgress((prev) => ({ ...prev, [fileId]: 100 }));

        // Add to successful uploads
        newSuccessfulUploads.push(file.name);
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : `Failed to upload ${file.name}`;

        setErrors((prev) => [...prev, `${file.name}: ${errorMessage}`]);
        console.error(`Error uploading ${file.name}:`, error);
      }
    }

    setIsUploading(false);
    setSuccessfulUploads((prev) => [...prev, ...newSuccessfulUploads]);

    // Notify parent component
    if (newSuccessfulUploads.length > 0) {
      const successfulFiles = uploadQueue.filter((file) =>
        newSuccessfulUploads.includes(file.name)
      );
      onFilesSelected(successfulFiles);

      // Clear successfully uploaded files from queue
      setUploadQueue((prev) =>
        prev.filter((file) => !newSuccessfulUploads.includes(file.name))
      );
    }
  };

  // Calculate overall progress percentage
  const calculateOverallProgress = () => {
    if (uploadQueue.length === 0) return 0;

    const totalProgress = Object.values(uploadProgress).reduce(
      (sum, progress) => sum + progress,
      0
    );
    const maxPossibleProgress = uploadQueue.length * 100;

    return Math.round((totalProgress / maxPossibleProgress) * 100);
  };

  return (
    <div className="w-full">
  
      <div
        {...getRootProps()}
        className="relative border-2 border-dashed p-8 transition-all duration-200 cursor-pointer animate-fade-in"
        style={{
          borderColor:
            isDragActive && !isDragReject
              ? "var(--primary-400)"
              : isDragReject
              ? "var(--accent-red)"
              : "var(--border)",
          backgroundColor:
            isDragActive && !isDragReject
              ? "var(--primary-50)"
              : isDragReject
              ? "rgb(239 68 68 / 0.1)"
              : "var(--surface-elevated)",
          borderRadius: "var(--radius-lg)",
          opacity: disabled || isUploading ? 0.5 : 1,
          cursor: disabled || isUploading ? "not-allowed" : "pointer",
        }}
        onMouseEnter={(e) => {
          if (!disabled && !isUploading && !isDragActive) {
            e.currentTarget.style.borderColor = "var(--primary-300)";
            e.currentTarget.style.transform = "scale(1.01)";
          }
        }}
        onMouseLeave={(e) => {
          if (!disabled && !isUploading && !isDragActive) {
            e.currentTarget.style.borderColor = "var(--border)";
            e.currentTarget.style.transform = "scale(1)";
          }
        }}
      >
        <input {...getInputProps()} />

        <CloudArrowUpIcon
          className="mx-auto h-16 w-16 mb-4"
          style={{
            color:
              isDragActive && !isDragReject
                ? "var(--primary-500)"
                : "var(--text-tertiary)",
          }}
        />
        <div>
          <p className="text-heading-3 mb-2">
            {isDragActive
              ? isDragReject
                ? "Invalid file type"
                : "Drop files here"
              : "Upload PDF files"}
          </p>
          <p
            className="text-body mb-2"
            style={{ color: "var(--text-secondary)" }}
          >
            {isDragActive
              ? "Release to upload"
              : `Drag and drop PDF files here, or click to select files`}
          </p>
          <p className="text-caption" style={{ color: "var(--text-tertiary)" }}>
            Max file size: {formatFileSize(maxSize)}
            {multiple && " • Multiple files allowed"}
          </p>
        </div>

        {isUploading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex flex-col items-center justify-center rounded-lg">
            <div className="text-center mb-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <div className="mt-2 text-sm text-gray-600">Uploading...</div>
              <div className="text-sm text-gray-500 font-medium mt-1">
                {calculateOverallProgress()}% complete
              </div>
            </div>
            <div className="w-64 bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-600 h-2.5 rounded-full"
                style={{ width: `${calculateOverallProgress()}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>

      {/* File Queue */}
      {uploadQueue.length > 0 && (
        <div className="mt-4 bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-900">
              Files to upload ({uploadQueue.length})
            </h3>
            <div className="flex gap-2">
              <button
                onClick={handleUpload}
                disabled={isUploading}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Upload All
              </button>
              <button
                onClick={clearQueue}
                disabled={isUploading}
                className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50"
              >
                Clear
              </button>
            </div>
          </div>

          <div className="space-y-2">
            {uploadQueue.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-2 bg-gray-50 rounded"
              >
                <div className="flex items-center">
                  <DocumentIcon className="h-5 w-5 text-gray-400 mr-2" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {file.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatFileSize(file.size)}
                    </div>
                  </div>
                </div>
                {uploadProgress[file.name] > 0 &&
                uploadProgress[file.name] < 100 ? (
                  <div className="w-24 bg-gray-200 rounded-full h-1.5">
                    <div
                      className="bg-blue-600 h-1.5 rounded-full"
                      style={{ width: `${uploadProgress[file.name]}%` }}
                    ></div>
                  </div>
                ) : (
                  <button
                    onClick={() => handleRemoveFile(index)}
                    disabled={isUploading}
                    className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Successful Uploads */}
      {successfulUploads.length > 0 && (
        <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-green-900">
              Successfully Uploaded ({successfulUploads.length})
            </h3>
          </div>

          <div className="space-y-2">
            {successfulUploads.map((fileName, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-2 bg-green-50 rounded"
              >
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                  <div className="text-sm font-medium text-green-900">
                    {fileName}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Errors */}
      {(errors.length > 0 || fileRejections.length > 0) && (
        <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-red-800">Upload Errors</h3>
            <button
              onClick={clearErrors}
              className="text-xs text-red-600 hover:text-red-800"
            >
              Clear
            </button>
          </div>

          <div className="space-y-1">
            {errors.map((error, index) => (
              <div key={index} className="flex items-center">
                <ExclamationCircleIcon className="h-4 w-4 text-red-500 mr-1" />
                <div className="text-sm text-red-700">{error}</div>
              </div>
            ))}
            {fileRejections.map(({ file, errors: rejectionErrors }) => (
              <div key={file.name} className="flex items-start">
                <ExclamationCircleIcon className="h-4 w-4 text-red-500 mr-1 mt-0.5" />
                <div>
                  <div className="text-sm font-medium text-red-800">
                    {file.name}
                  </div>
                  <ul className="list-disc pl-5 space-y-1">
                    {rejectionErrors.map((e, i) => (
                      <li key={i} className="text-xs text-red-700">
                        {e.message}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
