"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTheme } from '@/contexts/ThemeContext';

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  return (
    <motion.button
      onClick={toggleTheme}
      className="relative p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label="Toggle theme"
    >
      <motion.div
        className="relative w-6 h-6"
        initial={false}
        animate={{
          rotate: theme === 'dark' ? 180 : 0,
        }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
      >
        <motion.div
          className="absolute inset-0"
          initial={false}
          animate={{
            opacity: theme === 'light' ? 1 : 0,
            scale: theme === 'light' ? 1 : 0.5,
          }}
          transition={{ duration: 0.2 }}
        >
          <SunIcon className="w-6 h-6 text-yellow-500" />
        </motion.div>

        <motion.div
          className="absolute inset-0"
          initial={false}
          animate={{
            opacity: theme === 'dark' ? 1 : 0,
            scale: theme === 'dark' ? 1 : 0.5,
          }}
          transition={{ duration: 0.2 }}
        >
          <MoonIcon className="w-6 h-6 text-blue-400" />
        </motion.div>
      </motion.div>
    </motion.button>
  );
}

export function ThemeToggleSwitch() {
  const { theme, toggleTheme } = useTheme();
  const isDark = theme === 'dark';

  return (
    <motion.button
      onClick={toggleTheme}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
        isDark ? 'bg-blue-600' : 'bg-gray-200'
      }`}
      whileTap={{ scale: 0.95 }}
    >
      <motion.span
        className={`inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform ${
          isDark ? 'translate-x-6' : 'translate-x-1'
        }`}
        layout
        transition={{ type: "spring", stiffness: 700, damping: 30 }}
      />

      {/* Icons inside the switch */}
      <motion.div
        className="absolute left-1 top-1"
        animate={{ opacity: isDark ? 0 : 1 }}
        transition={{ duration: 0.2 }}
      >
        <SunIcon className="w-4 h-4 text-yellow-500" />
      </motion.div>

      <motion.div
        className="absolute right-1 top-1"
        animate={{ opacity: isDark ? 1 : 0 }}
        transition={{ duration: 0.2 }}
      >
        <MoonIcon className="w-4 h-4 text-white" />
      </motion.div>
    </motion.button>
  );
}