"use client";

import React from 'react';
import { Toaster, toast } from 'react-hot-toast';
import { motion } from 'framer-motion';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  XCircleIcon, 
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

// Custom toast component
const CustomToast = ({ 
  type, 
  message, 
  onDismiss 
}: { 
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  onDismiss: () => void;
}) => {
  const icons = {
    success: CheckCircleIcon,
    error: XCircleIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon,
  };

  const colors = {
    success: 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800',
    error: 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800',
    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800',
    info: 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800',
  };

  const Icon = icons[type];

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.3 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, scale: 0.5, transition: { duration: 0.2 } }}
      className={`
        flex items-center p-4 rounded-lg border shadow-lg backdrop-blur-sm
        ${colors[type]}
        max-w-md w-full
      `}
    >
      <Icon className="w-5 h-5 mr-3 flex-shrink-0" />
      <p className="text-sm font-medium flex-1">{message}</p>
      <button
        onClick={onDismiss}
        className="ml-3 flex-shrink-0 rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 transition-colors"
      >
        <XMarkIcon className="w-4 h-4" />
      </button>
    </motion.div>
  );
};

export function ToastProvider() {
  return (
    <Toaster
      position="top-right"
      gutter={8}
      containerClassName="!top-4 !right-4"
      toastOptions={{
        duration: 4000,
        style: {
          background: 'transparent',
          boxShadow: 'none',
          padding: 0,
          margin: 0,
        },
      }}
    />
  );
}

// Toast utility functions
export const showToast = {
  success: (message: string) => {
    toast.custom((t) => (
      <CustomToast
        type="success"
        message={message}
        onDismiss={() => toast.dismiss(t.id)}
      />
    ));
  },

  error: (message: string) => {
    toast.custom((t) => (
      <CustomToast
        type="error"
        message={message}
        onDismiss={() => toast.dismiss(t.id)}
      />
    ));
  },

  warning: (message: string) => {
    toast.custom((t) => (
      <CustomToast
        type="warning"
        message={message}
        onDismiss={() => toast.dismiss(t.id)}
      />
    ));
  },

  info: (message: string) => {
    toast.custom((t) => (
      <CustomToast
        type="info"
        message={message}
        onDismiss={() => toast.dismiss(t.id)}
      />
    ));
  },

  promise: <T,>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ) => {
    return toast.promise(promise, {
      loading: (
        <div className="flex items-center">
          <motion.div
            className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full mr-3"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
          {loading}
        </div>
      ),
      success: (data) => {
        const message = typeof success === 'function' ? success(data) : success;
        return (
          <CustomToast
            type="success"
            message={message}
            onDismiss={() => {}}
          />
        );
      },
      error: (err) => {
        const message = typeof error === 'function' ? error(err) : error;
        return (
          <CustomToast
            type="error"
            message={message}
            onDismiss={() => {}}
          />
        );
      },
    });
  },
};

// Progress toast for file uploads
export const showProgressToast = (
  message: string,
  progress: number,
  onCancel?: () => void
) => {
  return toast.custom((t) => (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.3 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, scale: 0.5, transition: { duration: 0.2 } }}
      className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-md w-full"
    >
      <div className="flex items-center justify-between mb-2">
        <p className="text-sm font-medium text-gray-900 dark:text-white">
          {message}
        </p>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {Math.round(progress)}%
          </span>
          {onCancel && (
            <button
              onClick={() => {
                onCancel();
                toast.dismiss(t.id);
              }}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
      
      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <motion.div
          className="bg-blue-600 h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </motion.div>
  ), {
    duration: Infinity,
  });
};
