"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface SkeletonProps {
  className?: string;
  width?: string | number;
  height?: string | number;
  rounded?: boolean;
  animate?: boolean;
}

export function Skeleton({ 
  className = '', 
  width, 
  height, 
  rounded = false,
  animate = true 
}: SkeletonProps) {
  const baseClasses = `bg-gray-200 dark:bg-gray-700 ${rounded ? 'rounded-full' : 'rounded'} ${className}`;
  
  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  if (animate) {
    return (
      <motion.div
        className={`${baseClasses} animate-shimmer`}
        style={{
          ...style,
          background: 'linear-gradient(90deg, var(--surface) 25%, var(--border-light) 50%, var(--surface) 75%)',
          backgroundSize: '200px 100%',
        }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      />
    );
  }

  return <div className={baseClasses} style={style} />;
}

export function FolderListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="divide-y divide-gray-200 dark:divide-gray-700">
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          className="px-6 py-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <div className="grid grid-cols-12 gap-4 items-center">
            {/* Checkbox */}
            <div className="col-span-1">
              <Skeleton width={16} height={16} rounded />
            </div>
            
            {/* Icon */}
            <div className="col-span-1">
              <Skeleton width={24} height={24} rounded />
            </div>
            
            {/* Name and description */}
            <div className="col-span-3 space-y-2">
              <Skeleton height={20} width="80%" />
              <Skeleton height={14} width="60%" />
              <Skeleton height={12} width="40%" />
            </div>
            
            {/* Owner */}
            <div className="col-span-2 space-y-1">
              <Skeleton height={16} width="70%" />
              <Skeleton height={12} width="50%" />
            </div>
            
            {/* Date */}
            <div className="col-span-2">
              <Skeleton height={16} width="80%" />
            </div>
            
            {/* Size */}
            <div className="col-span-1">
              <Skeleton height={16} width="30%" />
            </div>
            
            {/* Actions */}
            <div className="col-span-2 flex gap-2">
              <Skeleton width={24} height={24} rounded />
              <Skeleton width={24} height={24} rounded />
              <Skeleton width={24} height={24} rounded />
              <Skeleton width={24} height={24} rounded />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export function FolderGridSkeleton({ count = 12 }: { count?: number }) {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          className="card p-4"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: index * 0.05 }}
        >
          <div className="text-center space-y-3">
            {/* Icon */}
            <div className="flex justify-center">
              <Skeleton width={48} height={48} rounded />
            </div>
            
            {/* Name */}
            <Skeleton height={16} width="80%" className="mx-auto" />
            
            {/* Stats */}
            <Skeleton height={12} width="60%" className="mx-auto" />
            
            {/* Owner */}
            <Skeleton height={12} width="50%" className="mx-auto" />
            
            {/* Checkbox */}
            <div className="flex justify-center">
              <Skeleton width={16} height={16} rounded />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}

export function LoadingSpinner({ size = 'md', className = '' }: { 
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  return (
    <motion.div
      className={`${sizeClasses[size]} ${className}`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    >
      <svg
        className="w-full h-full text-blue-600"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </motion.div>
  );
}

export function LoadingOverlay({ message = "Đang tải..." }: { message?: string }) {
  return (
    <motion.div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-xl"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
      >
        <div className="flex items-center space-x-4">
          <LoadingSpinner size="lg" />
          <p className="text-lg font-medium text-gray-900 dark:text-white">
            {message}
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
}

export function EmptyState({ 
  icon: Icon,
  title,
  description,
  action
}: {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  action?: React.ReactNode;
}) {
  return (
    <motion.div
      className="text-center py-12"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="mx-auto w-24 h-24 text-gray-400 dark:text-gray-600 mb-4"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Icon className="w-full h-full" />
      </motion.div>
      
      <motion.h3
        className="text-lg font-medium text-gray-900 dark:text-white mb-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        {title}
      </motion.h3>
      
      <motion.p
        className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        {description}
      </motion.p>
      
      {action && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          {action}
        </motion.div>
      )}
    </motion.div>
  );
}
