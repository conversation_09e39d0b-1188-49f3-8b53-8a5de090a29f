"use client";

import React, { useState, useRef, useEffect } from "react";
import { PencilIcon, CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";

interface InlineEditableTextProps {
  value: string;
  onSave: (newValue: string) => Promise<void>;
  className?: string;
  textClassName?: string;
  inputClassName?: string;
  showEditIcon?: boolean;
  iconClassName?: string;
  placeholder?: string;
  disabled?: boolean;
  multiline?: boolean;
  maxLength?: number;
  validateInput?: (value: string) => string | null; // Return error message or null
}

export function InlineEditableText({
  value,
  onSave,
  className = "",
  textClassName = "",
  inputClassName = "",
  showEditIcon = true,
  iconClassName = "",
  placeholder = "Enter text...",
  disabled = false,
  multiline = false,
  maxLength,
  validateInput,
}: InlineEditableTextProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);

  useEffect(() => {
    setEditValue(value);
  }, [value]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      if (inputRef.current instanceof HTMLInputElement) {
        inputRef.current.select();
      } else {
        // For textarea
        inputRef.current.setSelectionRange(0, inputRef.current.value.length);
      }
    }
  }, [isEditing]);

  const handleDoubleClick = () => {
    if (!disabled) {
      setIsEditing(true);
      setError(null);
    }
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!disabled) {
      setIsEditing(true);
      setError(null);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(value);
    setError(null);
  };

  const handleSave = async () => {
    if (editValue.trim() === value.trim()) {
      setIsEditing(false);
      return;
    }

    if (validateInput) {
      const validationError = validateInput(editValue.trim());
      if (validationError) {
        setError(validationError);
        return;
      }
    }

    setIsSaving(true);
    setError(null);

    try {
      await onSave(editValue.trim());
      setIsEditing(false);
    } catch (error: any) {
      setError(error.message || "Failed to save changes");
    } finally {
      setIsSaving(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !multiline) {
      e.preventDefault();
      handleSave();
    } else if (e.key === "Enter" && multiline && e.ctrlKey) {
      e.preventDefault();
      handleSave();
    } else if (e.key === "Escape") {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    if (maxLength && newValue.length > maxLength) {
      return;
    }
    setEditValue(newValue);
    if (error) setError(null);
  };

  if (isEditing) {
    const InputComponent = multiline ? "textarea" : "input";
    return (
      <div className={`inline-flex items-start gap-2 ${className}`}>
        <div className="flex-1">
          <InputComponent
            ref={inputRef as any}
            type={multiline ? undefined : "text"}
            value={editValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            className={`w-full px-2 py-1 border border-blue-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${inputClassName} ${
              error ? "border-red-300 focus:ring-red-500 focus:border-red-500" : ""
            }`}
            placeholder={placeholder}
            disabled={isSaving}
            rows={multiline ? 3 : undefined}
          />
          {error && (
            <p className="text-xs text-red-600 mt-1">{error}</p>
          )}
          {maxLength && (
            <p className="text-xs text-gray-500 mt-1">
              {editValue.length}/{maxLength}
            </p>
          )}
        </div>
        <div className="flex items-center gap-1 mt-1">
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="p-1 text-green-600 hover:text-green-800 hover:bg-green-50 rounded transition-colors disabled:opacity-50"
            title="Save"
          >
            <CheckIcon className="w-4 h-4" />
          </button>
          <button
            onClick={handleCancel}
            disabled={isSaving}
            className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors disabled:opacity-50"
            title="Cancel"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`inline-flex items-center gap-2 group ${className}`}>
      <span
        onDoubleClick={handleDoubleClick}
        className={`cursor-pointer ${textClassName} ${disabled ? "opacity-50" : ""}`}
        title="Double-click to edit"
      >
        {value || placeholder}
      </span>
      {showEditIcon && !disabled && (
        <button
          onClick={handleEditClick}
          className={`opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-all ${iconClassName}`}
          title="Edit"
        >
          <PencilIcon className="w-4 h-4" />
        </button>
      )}
    </div>
  );
} 