"use client";

import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from './Button';
import { Input } from './Input';

export function DarkModeGuide() {
  const { theme } = useTheme();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Dark Mode Implementation Guide
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Current theme: <span className="font-semibold">{theme}</span>
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Button Examples */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Button Components
          </h2>
          <div className="space-y-3">
            <Button variant="primary">Primary Button</Button>
            <Button variant="secondary">Secondary Button</Button>
            <Button variant="outline">Outline Button</Button>
            <Button variant="ghost">Ghost Button</Button>
            <Button variant="destructive">Destructive Button</Button>
          </div>
        </div>

        {/* Input Examples */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Input Components
          </h2>
          <div className="space-y-3">
            <Input 
              label="Default Input"
              placeholder="Enter text here..."
              helperText="This is a helper text"
            />
            <Input 
              label="Error Input"
              variant="error"
              placeholder="Error state..."
              errorMessage="This field has an error"
            />
            <Input 
              label="Success Input"
              variant="success"
              placeholder="Success state..."
              helperText="This field is valid"
            />
          </div>
        </div>

        {/* Color Examples */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Text Colors
          </h2>
          <div className="space-y-2">
            <p className="text-gray-900 dark:text-white">Primary text color</p>
            <p className="text-gray-700 dark:text-gray-300">Secondary text color</p>
            <p className="text-gray-500 dark:text-gray-400">Tertiary text color</p>
            <p className="text-primary-600 dark:text-primary-400">Accent text color</p>
          </div>
        </div>

        {/* Background Examples */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Background Colors
          </h2>
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-600">
              <p className="text-sm text-gray-600 dark:text-gray-300">Surface background</p>
            </div>
            <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600">
              <p className="text-sm text-gray-600 dark:text-gray-300">Elevated surface</p>
            </div>
          </div>
        </div>
      </div>

      {/* Implementation Code */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Implementation Example
        </h2>
        <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-lg overflow-x-auto text-sm">
          <code className="text-gray-800 dark:text-gray-200">
{`// Using Tailwind CSS dark mode classes
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
  <h1 className="text-2xl font-bold">Title</h1>
  <p className="text-gray-600 dark:text-gray-300">Description</p>
</div>

// Using CSS custom properties
<div style={{
  background: 'var(--surface-elevated)',
  color: 'var(--text-primary)',
  border: '1px solid var(--border)'
}}>
  Content
</div>`}
          </code>
        </pre>
      </div>
    </div>
  );
} 