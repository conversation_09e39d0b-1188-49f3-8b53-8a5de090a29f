"use client";

import React, { useState, useRef, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { UserStorage } from "@/lib/userStorage";
import {
  Bars3Icon,
  BellIcon,
  ArrowRightStartOnRectangleIcon,
  ChevronDownIcon,
  UserIcon,
  Cog6ToothIcon,
} from "@heroicons/react/24/outline";
import { ThemeToggle } from "@/components/ui/ThemeToggle";

interface HeaderProps {
  onMenuClick?: () => void;
}

/**
 * Application Header Component
 * Provides navigation, search, notifications, and user profile
 */
export function Header({ onMenuClick }: HeaderProps) {
  const { user, logout, isLoading, getCurrentUserId } = useAuth();
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowProfileDropdown(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      setShowProfileDropdown(false);
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const navigateToProfile = () => {
    setShowProfileDropdown(false);
    window.location.href = '/user-management/profiles';
  };

  const navigateToSettings = () => {
    setShowProfileDropdown(false);
    window.location.href = '/user-management';
  };

  // Get user display information
  const userDisplay = UserStorage.getUserDisplayInfo();
  const userId = getCurrentUserId();

  return (
    <div
      className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 px-4 sm:gap-x-6 sm:px-6 lg:px-8 animate-fade-in"
      style={{
        background: "var(--surface-elevated)",
        borderBottom: "1px solid var(--border-light)",
        boxShadow: "var(--shadow-sm)",
      }}
    >
      {/* Mobile menu button */}
      <button
        type="button"
        className="-m-2.5 p-2.5 lg:hidden btn-secondary"
        style={{
          background: "transparent",
          border: "none",
          color: "var(--text-secondary)",
          borderRadius: "var(--radius-md)",
          transition: "all 0.2s ease",
        }}
        onClick={onMenuClick}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = "var(--surface)";
          e.currentTarget.style.color = "var(--text-primary)";
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = "transparent";
          e.currentTarget.style.color = "var(--text-secondary)";
        }}
      >
        <span className="sr-only">Open sidebar</span>
        <Bars3Icon className="h-6 w-6" aria-hidden="true" />
      </button>

      {/* Separator */}
      <div
        className="h-6 w-px lg:hidden"
        style={{ background: "var(--border)" }}
        aria-hidden="true"
      />

      <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
        {/* Search */}
        <div className="relative flex flex-1">
          {/* <form action="#" method="GET">
            <label htmlFor="search-field" className="sr-only">
              Search
            </label>
            <input
              id="search-field"
              className="block h-full w-full border-0 py-0 pl-8 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm"
              placeholder="Search files..."
              type="search"
              name="search"
            />
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <svg
                className="h-5 w-5 text-gray-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </form> */}
        </div>
        <div className="flex items-center gap-x-4 lg:gap-x-6">
          {/* Theme Toggle */}
          <ThemeToggle />

          {/* Notifications */}
          <button
            type="button"
            className="-m-2.5 p-2.5 rounded-lg transition-all duration-200"
            style={{
              color: "var(--text-tertiary)",
              background: "transparent",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "var(--surface)";
              e.currentTarget.style.color = "var(--text-secondary)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "transparent";
              e.currentTarget.style.color = "var(--text-tertiary)";
            }}
          >
            <span className="sr-only">View notifications</span>
            <BellIcon className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Separator */}
          <div
            className="hidden lg:block lg:h-6 lg:w-px"
            style={{ background: "var(--border)" }}
            aria-hidden="true"
          />

          {/* Profile dropdown section */}
          <div className="relative" ref={dropdownRef}>
            <button
              type="button"
              className="flex items-center gap-x-3 rounded-lg px-3 py-2 transition-all duration-200"
              style={{
                background: "transparent",
                border: "none",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = "var(--surface)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = "transparent";
              }}
              onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              aria-expanded={showProfileDropdown}
              aria-haspopup="true"
            >
              <div
                className="h-8 w-8 rounded-full flex items-center justify-center"
                style={{
                  background:
                    "linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%)",
                  color: "var(--text-inverse)",
                }}
              >
                <span className="text-sm font-semibold">
                  {userDisplay?.name?.charAt(0)?.toUpperCase() || user?.name?.charAt(0)?.toUpperCase() || "U"}
                </span>
              </div>
              <div className="hidden lg:block text-left">
                <p
                  className="text-sm font-semibold leading-6"
                  style={{ color: "var(--text-primary)" }}
                >
                  {userDisplay?.name || user?.name || "Demo User"}
                </p>
                <p
                  className="text-xs leading-5"
                  style={{ color: "var(--text-tertiary)" }}
                >
                  {userDisplay?.email || user?.email || "<EMAIL>"}
                </p>
              </div>
              <ChevronDownIcon
                className={`h-4 w-4 transition-transform duration-200 ${showProfileDropdown ? 'rotate-180' : ''}`}
                style={{ color: "var(--text-tertiary)" }}
                aria-hidden="true"
              />
            </button>

            {/* Dropdown menu */}
            {showProfileDropdown && (
              <div
                className="absolute right-0 z-50 mt-2 w-80 origin-top-right rounded-md py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none animate-fade-in"
                style={{
                  background: "var(--surface-elevated)",
                  border: "1px solid var(--border-light)",
                }}
                role="menu"
                aria-orientation="vertical"
                aria-labelledby="user-menu-button"
                tabIndex={-1}
              >
                {/* User Info Section */}
                <div className="px-4 py-3 border-b" style={{ borderColor: "var(--border-light)" }}>
                  <div className="flex items-center gap-3">
                    <div
                      className="h-12 w-12 rounded-full flex items-center justify-center"
                      style={{
                        background:
                          "linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%)",
                        color: "var(--text-inverse)",
                      }}
                    >
                      <span className="text-lg font-semibold">
                        {userDisplay?.name?.charAt(0)?.toUpperCase() || user?.name?.charAt(0)?.toUpperCase() || "U"}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
                        {userDisplay?.name || user?.name || "Demo User"}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                        {userDisplay?.email || user?.email || "<EMAIL>"}
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        <span className="font-medium">Role:</span> {userDisplay?.role || "User"}
                      </p>
                      {userId && (
                        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                          <span className="font-medium">ID:</span> 
                          <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded text-xs ml-1">{userId}</code>
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Menu Items */}
                <div className="py-1">
                  <button
                    type="button"
                    className="flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                    role="menuitem"
                    tabIndex={-1}
                    onClick={navigateToProfile}
                  >
                    <UserIcon className="h-4 w-4 mr-3 text-gray-400 dark:text-gray-500" aria-hidden="true" />
                    View Profile
                  </button>
                  
                  <button
                    type="button"
                    className="flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                    role="menuitem"
                    tabIndex={-1}
                    onClick={navigateToSettings}
                  >
                    <Cog6ToothIcon className="h-4 w-4 mr-3 text-gray-400 dark:text-gray-500" aria-hidden="true" />
                    User Management
                  </button>

                  <div className="border-t" style={{ borderColor: "var(--border-light)" }}>
                    <button
                      type="button"
                      onClick={handleLogout}
                      disabled={isLoading}
                      className="flex w-full items-center px-4 py-2 text-sm text-red-700 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      role="menuitem"
                      tabIndex={-1}
                    >
                      <ArrowRightStartOnRectangleIcon className="h-4 w-4 mr-3 text-red-400 dark:text-red-500" aria-hidden="true" />
                      {isLoading ? 'Signing out...' : 'Sign out'}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
