"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { usePathname } from "next/navigation";
import {
  FolderIcon,
  DocumentIcon,
  CogIcon,
  HomeIcon,
  ClockIcon,
  UsersIcon,
  ShieldCheckIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon,
  PlayIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";

interface NavigationItem {
  name: string;
  href?: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  children?: NavigationItem[];
}

const navigation: NavigationItem[] = [
  { name: "Dashboard", href: "/", icon: HomeIcon },

  {
    name: "Quản lý User",
    icon: UsersIcon,
    children: [
      {
        name: "Quản lý user",
        href: "/user-management/list",
        icon: ClipboardDocumentListIcon,
      },
      {
        name: "<PERSON>uản lý phân quyền",
        href: "/permission-management",
        icon: ShieldCheckIcon,
      },
    ],
  },
  {
    name: "<PERSON><PERSON>ản lý hồ sơ",
    icon: FolderIcon,
    children: [
      {
        name: "Quản lý folder",
        href: "/manager-folders",
        icon: UserGroupIcon,
      },
      {
        name: "Thùng rác",
        href: "/recycle-bin",
        icon: TrashIcon,
      },
      {
        name: "Auto detach",
        href: "/processing/auto",
        icon: ClockIcon,
      },
      {
        name: "Manual detach",
        href: "/processing/manual",
        icon: PlayIcon,
      },
    ],
  },
  { name: "Settings", href: "/settings", icon: CogIcon },
];

interface NavigationItemProps {
  item: NavigationItem;
  pathname: string;
  expandedItems: Set<string>;
  toggleExpanded: (name: string) => void;
  level?: number;
}

function NavigationItemComponent({
  item,
  pathname,
  expandedItems,
  toggleExpanded,
  level = 0,
}: NavigationItemProps) {
  const hasChildren = item.children && item.children.length > 0;
  const isExpanded = expandedItems.has(item.name);
  const isActive = pathname === item.href;
  const isChildActive = item.children?.some((child) => pathname === child.href);

  const handleClick = () => {
    if (hasChildren) {
      toggleExpanded(item.name);
    }
  };

  const getItemStyle = () => ({
    background:
      isActive || isChildActive
        ? "linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%)"
        : "transparent",
    color:
      isActive || isChildActive
        ? "var(--text-inverse)"
        : "var(--text-secondary)",
    borderRadius: "var(--radius-md)",
    transition: "all 0.2s ease",
    marginLeft: level > 0 ? "1.5rem" : "0",
  });

  const itemClasses = `
    group flex gap-x-3 p-3 text-sm leading-6 font-medium cursor-pointer
    transition-all duration-200 hover:scale-[1.02]
  `;

  return (
    <li>
      {hasChildren ? (
        <div
          onClick={handleClick}
          className={itemClasses}
          style={getItemStyle()}
          onMouseEnter={(e) => {
            if (!isActive && !isChildActive) {
              e.currentTarget.style.background = "var(--surface)";
              e.currentTarget.style.color = "var(--text-primary)";
            }
          }}
          onMouseLeave={(e) => {
            if (!isActive && !isChildActive) {
              e.currentTarget.style.background = "transparent";
              e.currentTarget.style.color = "var(--text-secondary)";
            }
          }}
        >
          <item.icon className="h-5 w-5 shrink-0" aria-hidden="true" />
          <span className="flex-1">{item.name}</span>
          {isExpanded ? (
            <ChevronDownIcon className="h-4 w-4 shrink-0" />
          ) : (
            <ChevronRightIcon className="h-4 w-4 shrink-0" />
          )}
        </div>
      ) : (
        <Link
          href={item.href || "#"}
          className={itemClasses}
          style={getItemStyle()}
          onMouseEnter={(e) => {
            if (!isActive && !isChildActive) {
              e.currentTarget.style.background = "var(--surface)";
              e.currentTarget.style.color = "var(--text-primary)";
            }
          }}
          onMouseLeave={(e) => {
            if (!isActive && !isChildActive) {
              e.currentTarget.style.background = "transparent";
              e.currentTarget.style.color = "var(--text-secondary)";
            }
          }}
        >
          <item.icon className="h-5 w-5 shrink-0" aria-hidden="true" />
          {item.name}
        </Link>
      )}

      {hasChildren && isExpanded && (
        <ul className="mt-1 space-y-1">
          {(item.children || []).map((child) => (
            <NavigationItemComponent
              key={child.name}
              item={child}
              pathname={pathname}
              expandedItems={expandedItems}
              toggleExpanded={toggleExpanded}
              level={level + 1}
            />
          ))}
        </ul>
      )}
    </li>
  );
}

export function Sidebar() {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleExpanded = (name: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(name)) {
        newSet.delete(name);
      } else {
        newSet.add(name);
      }
      return newSet;
    });
  };

  return (
    <div
      className="flex h-full w-64 flex-col overflow-hidden animate-slide-in"
      style={{
        background: "var(--surface-elevated)",
        borderRight: "1px solid var(--border-light)",
        boxShadow: "var(--shadow-md)",
      }}
    >
      {/* Logo */}
      <div
        className="flex h-16 shrink-0 items-center px-6"
        style={{
          borderBottom: "1px solid var(--border-light)",
          background:
            "linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%)",
        }}
      >
        <DocumentIcon
          className="h-8 w-8"
          style={{ color: "var(--text-inverse)" }}
        />
        <span
          className="ml-3 text-xl font-bold tracking-tight"
          style={{ color: "var(--text-inverse)" }}
        >
          PDF OCR
        </span>
      </div>

      {/* Navigation */}
      <nav className="flex flex-1 flex-col px-4 py-6 overflow-hidden">
        <ul role="list" className="flex flex-1 flex-col gap-y-2">
          <li>
            <ul role="list" className="space-y-2">
              {navigation.map((item) => (
                <NavigationItemComponent
                  key={item.name}
                  item={item}
                  pathname={pathname || ""}
                  expandedItems={expandedItems}
                  toggleExpanded={toggleExpanded}
                />
              ))}
            </ul>
          </li>

          {/* Quick Actions */}
          {/* <li className="mt-auto">
            <div className="text-xs font-semibold leading-6 text-gray-400">
              Quick Actions
            </div>
            <ul role="list" className="-mx-2 mt-2 space-y-1">
              <li>
                <button className="group flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-gray-400 hover:text-white hover:bg-gray-800">
                  <DocumentIcon
                    className="h-6 w-6 shrink-0"
                    aria-hidden="true"
                  />
                  Upload PDF
                </button>
              </li>
              <li>
                <button className="group flex w-full gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-gray-400 hover:text-white hover:bg-gray-800">
                  <EyeIcon className="h-6 w-6 shrink-0" aria-hidden="true" />
                  Quick View
                </button>
              </li>
            </ul>
          </li> */}
        </ul>
      </nav>
    </div>
  );
}
