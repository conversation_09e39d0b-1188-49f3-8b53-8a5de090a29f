"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  FolderIcon, 
  DocumentIcon, 
  ArrowPathIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  Bars3Icon,
  ViewColumnsIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { RecycleBinService, RecycleBinItem } from '@/api/services/recycleBinService';
import { ApiClient } from '@/api/core/apiClient';
import {
  SortDirection,
  PaginationInfo,
  DeletedItemType
} from '@/api/types/interfaces';

interface RecycleBinManagerProps {
  apiClient: ApiClient;
}

interface RecycleBinState {
  deletedItems: RecycleBinItem[];
  selectedItems: Set<string>;
  loading: boolean;
  error: string | null;
  searchTerm: string;
  sorting: {
    field: 'Name' | 'DeletedAt' | 'ExpiresAt' | 'FileSize';
    direction: SortDirection;
  };
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
  };
  viewMode: 'list' | 'grid';
  showBulkActions: boolean;
}

export function RecycleBinManager({ apiClient }: RecycleBinManagerProps) {
  const [state, setState] = useState<RecycleBinState>({
    deletedItems: [],
    selectedItems: new Set(),
    loading: false,
    error: null,
    searchTerm: "",
    sorting: {
      field: 'DeletedAt',
      direction: SortDirection.DESC
    },
    pagination: {
      page: 1,
      pageSize: 20,
      totalItems: 0,
      totalPages: 0
    },
    viewMode: 'list',
    showBulkActions: false
  });

  const recycleBinService = useRef(new RecycleBinService(apiClient)).current;

  // Load deleted items with stable dependencies
  const loadDeletedItems = useCallback(async (options?: {
    page?: number;
    pageSize?: number;
    sortField?: 'Name' | 'DeletedAt' | 'ExpiresAt' | 'FileSize';
    sortDirection?: SortDirection;
    searchTerm?: string;
  }) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    const currentOptions = {
      page: options?.page ?? state.pagination.page,
      pageSize: options?.pageSize ?? state.pagination.pageSize,
      sortField: options?.sortField ?? state.sorting.field,
      sortDirection: options?.sortDirection ?? state.sorting.direction,
      searchTerm: options?.searchTerm ?? state.searchTerm
    };

    try {
      // Use unified getDeletedItems API
      const response = await recycleBinService.getDeletedItems({
        page: currentOptions.page,
        pageSize: currentOptions.pageSize,
        sortBy: currentOptions.sortField,
        sortDirection: currentOptions.sortDirection,
        search: currentOptions.searchTerm || undefined
      });

      // Transform the response to RecycleBinItem format expected by the component
      const transformedItems: RecycleBinItem[] = response.items.map(item => ({
        id: item.id,
        name: item.originalName,
        type: item.itemType === 1 ? 'folder' as const : 'file' as const, // 1 = Folder, 0 = File
        originalPath: item.originalPath || '',
        deletedAt: item.deletedAt,
        deletedBy: item.deletedByName || item.deletedBy,
        expiresAt: item.expiresAt || '',
        size: item.size,
        mimeType: item.mimeType
      }));

      setState(prev => ({
        ...prev,
        deletedItems: transformedItems,
        pagination: {
          ...prev.pagination,
          totalItems: response.totalCount,
          totalPages: response.totalPages,
          page: response.page,
          pageSize: response.pageSize
        },
        loading: false
      }));
    } catch (error) {
      console.error('Load deleted items error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load deleted items',
        loading: false
      }));
      // DO NOT automatically retry - let user manually retry
    }
  }, [recycleBinService]); // Only depend on stable service reference

  // Initialize component - only run once
  useEffect(() => {
    loadDeletedItems();
  }, []); // Empty dependency array - only run on mount

  // Handle search
  const handleSearch = useCallback(() => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 }
    }));
    loadDeletedItems({
      page: 1,
      searchTerm: state.searchTerm
    });
  }, [loadDeletedItems, state.searchTerm]);

  // Handle sorting
  const handleSort = (field: 'Name' | 'DeletedAt' | 'ExpiresAt' | 'FileSize') => {
    const newDirection = state.sorting.field === field && state.sorting.direction === SortDirection.ASC
      ? SortDirection.DESC
      : SortDirection.ASC;
    
    setState(prev => ({
      ...prev,
      sorting: {
        field,
        direction: newDirection
      },
      pagination: { ...prev.pagination, page: 1 }
    }));

    loadDeletedItems({
      page: 1,
      sortField: field,
      sortDirection: newDirection
    });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setState(prev => ({
      ...prev,
      pagination: { ...prev.pagination, page }
    }));

    loadDeletedItems({ page });
  };

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    setState(prev => {
      const newSelection = new Set(prev.selectedItems);
      if (newSelection.has(itemId)) {
        newSelection.delete(itemId);
      } else {
        newSelection.add(itemId);
      }
      return { 
        ...prev, 
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0
      };
    });
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    setState(prev => {
      const newSelection = new Set<string>();
      if (checked) {
        prev.deletedItems.forEach(item => newSelection.add(item.id));
      }
      return { 
        ...prev, 
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0
      };
    });
  };

  // Restore items
  const handleRestore = async (itemIds: string[]) => {
    try {
      // Separate folders and files
      const folders: string[] = [];
      const files: string[] = [];
      
      itemIds.forEach(id => {
        const item = state.deletedItems.find(item => item.id === id);
        if (item?.type === 'folder') {
          folders.push(id);
        } else if (item?.type === 'file') {
          files.push(id);
        }
      });

      // Use bulk restore for multiple items
      if (itemIds.length > 1) {
        await recycleBinService.bulkRestore({ folders, files });
      } else {
        // Use single item restore for one item
        const item = state.deletedItems.find(item => item.id === itemIds[0]);
        if (item?.type === 'folder') {
          await recycleBinService.restoreFolder(itemIds[0]);
        } else {
          await recycleBinService.restoreFile(itemIds[0]);
        }
      }

      setState(prev => ({ 
        ...prev, 
        selectedItems: new Set(),
        showBulkActions: false
      }));
      // Reload with current state parameters
      loadDeletedItems();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to restore items'
      }));
    }
  };

  // Permanently delete items
  const handlePermanentDelete = async (itemIds: string[]) => {
    try {
      // Separate folders and files
      const folders: string[] = [];
      const files: string[] = [];
      
      itemIds.forEach(id => {
        const item = state.deletedItems.find(item => item.id === id);
        if (item?.type === 'folder') {
          folders.push(id);
        } else if (item?.type === 'file') {
          files.push(id);
        }
      });

      // Use bulk delete for multiple items
      if (itemIds.length > 1) {
        await recycleBinService.bulkPermanentDelete({ folders, files });
      } else {
        // Use single item delete for one item
        const item = state.deletedItems.find(item => item.id === itemIds[0]);
        if (item?.type === 'folder') {
          await recycleBinService.permanentDeleteFolder(itemIds[0]);
        } else {
          await recycleBinService.permanentDeleteFile(itemIds[0]);
        }
      }

      setState(prev => ({ 
        ...prev, 
        selectedItems: new Set(),
        showBulkActions: false
      }));
      // Reload with current state parameters
      loadDeletedItems();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to permanently delete items'
      }));
    }
  };

  // Format file size
  const formatFileSize = (bytes?: number) => {
    if (!bytes || bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  return (
    <div className="min-h-screen bg-gray-50 animate-fade-in">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-heading-2 text-gray-900">Thùng Rác</h1>
              <p className="text-body text-gray-600 mt-1">
                Quản lý các files và folders đã xóa
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => {
                  setState(prev => ({ ...prev, error: null }));
                  loadDeletedItems();
                }}
                className="btn-secondary flex items-center gap-2"
              >
                <ArrowPathIcon className="w-4 h-4" />
                Làm mới
              </button>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {state.error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
            <div className="flex">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{state.error}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setState(prev => ({ ...prev, error: null }))}
                  className="text-red-400 hover:text-red-600"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Search */}
        <div className="card p-6 mb-6 animate-fade-in">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm files và folders đã xóa"
                  value={state.searchTerm}
                  onChange={(e) => setState(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="input pl-10 w-full"
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <button 
              onClick={handleSearch}
              className="btn-primary"
            >
              Tìm kiếm
            </button>
          </div>
        </div>

        {/* Toolbar */}
        <div className="flex justify-between items-center mb-6 animate-fade-in">
          <div className="flex items-center gap-3">
            {state.showBulkActions && (
              <div className="flex items-center gap-2 animate-slide-in">
                <span className="text-sm text-gray-600">
                  {state.selectedItems.size} đã chọn
                </span>
                <button 
                  onClick={() => handleRestore(Array.from(state.selectedItems))}
                  className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
                  title="Khôi phục"
                >
                  <ArrowPathIcon className="w-4 h-4" />
                </button>
                <button 
                  onClick={() => handlePermanentDelete(Array.from(state.selectedItems))}
                  className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                  title="Xóa vĩnh viễn"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setState(prev => ({
                ...prev,
                viewMode: prev.viewMode === 'list' ? 'grid' : 'list'
              }))}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title={state.viewMode === 'list' ? 'Chế độ lưới' : 'Chế độ danh sách'}
            >
              {state.viewMode === 'list' ? 
                <ViewColumnsIcon className="w-4 h-4" /> : 
                <Bars3Icon className="w-4 h-4" />
              }
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="card animate-fade-in">
          {state.viewMode === 'list' ? (
            <>
              {/* Table Header */}
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                  <div className="col-span-1">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      checked={state.selectedItems.size > 0 && state.selectedItems.size === state.deletedItems.length}
                    />
                  </div>
                  <div className="col-span-1">Loại</div>
                  <div className="col-span-3">
                    <button
                      onClick={() => handleSort('Name')}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Tên
                      {state.sorting.field === 'Name' && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-2">Người xóa</div>
                  <div className="col-span-2">
                    <button
                      onClick={() => handleSort('DeletedAt')}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Ngày xóa
                      {state.sorting.field === 'DeletedAt' && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-1">Kích thước</div>
                  <div className="col-span-2">Thao tác</div>
                </div>
              </div>

              {/* Items */}
              <div className="divide-y divide-gray-200">
                {state.loading ? (
                  <div className="px-6 py-12 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-sm text-gray-600">Đang tải...</p>
                  </div>
                ) : (
                  <>
                    {/* Deleted Items */}
                    {state.deletedItems.map((item, index) => (
                      <div 
                        key={item.id} 
                        className="px-6 py-4 hover:bg-gray-50 animate-fade-in transition-colors"
                        style={{ animationDelay: `${index * 0.05}s` }}
                      >
                        <div className="grid grid-cols-12 gap-4 items-center">
                          <div className="col-span-1">
                            <input
                              type="checkbox"
                              checked={state.selectedItems.has(item.id)}
                              onChange={() => toggleItemSelection(item.id)}
                              className="rounded border-gray-300"
                            />
                          </div>
                          <div className="col-span-1">
                            {item.type === 'folder' ? (
                              <FolderIcon className="w-6 h-6 text-gray-400" />
                            ) : (
                              <DocumentIcon className="w-6 h-6 text-gray-400" />
                            )}
                          </div>
                          <div className="col-span-3">
                            <p className="font-medium text-gray-700">{item.name}</p>
                            <p className="text-sm text-gray-500 mt-1">{item.originalPath}</p>
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {item.deletedBy}
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {formatDate(item.deletedAt)}
                          </div>
                          <div className="col-span-1 text-sm text-gray-600">
                            {item.type === 'file' ? formatFileSize(item.size) : '--'}
                          </div>
                          <div className="col-span-2">
                            <div className="flex items-center gap-1">
                              <button
                                onClick={() => handleRestore([item.id])}
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                                title="Khôi phục"
                              >
                                <ArrowPathIcon className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handlePermanentDelete([item.id])}
                                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                title="Xóa vĩnh viễn"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Empty State */}
                    {!state.loading && state.deletedItems.length === 0 && (
                      <div className="px-6 py-12 text-center animate-fade-in">
                        <TrashIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          Thùng rác trống
                        </h3>
                        <p className="text-gray-500">
                          Không có files hoặc folders nào đã bị xóa.
                        </p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </>
          ) : (
            /* Grid View */
            <div className="p-6">
              {state.loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">Đang tải...</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {/* Deleted Items in Grid */}
                  {state.deletedItems.map((item, index) => (
                    <div 
                      key={item.id} 
                      className="card p-4 hover:shadow-md transition-all animate-fade-in"
                      style={{ animationDelay: `${index * 0.05}s` }}
                    >
                      <div className="text-center">
                        {item.type === 'folder' ? (
                          <FolderIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                        ) : (
                          <DocumentIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                        )}
                        <p className="text-sm font-medium text-gray-700 truncate" title={item.name}>
                          {item.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {item.type === 'file' ? 
                            formatFileSize(item.size) : 
                            'Folder'
                          }
                        </p>
                      </div>
                      <div className="mt-2 flex justify-center">
                        <input
                          type="checkbox"
                          checked={state.selectedItems.has(item.id)}
                          onChange={() => toggleItemSelection(item.id)}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  ))}

                  {/* Empty State for Grid */}
                  {!state.loading && state.deletedItems.length === 0 && (
                    <div className="col-span-full text-center py-12 animate-fade-in">
                      <TrashIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Thùng rác trống
                      </h3>
                      <p className="text-gray-500">
                        Không có files hoặc folders nào đã bị xóa.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Pagination */}
        {state.pagination.totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 animate-fade-in">
            <div className="text-sm text-gray-700">
              Hiển thị {((state.pagination.page - 1) * state.pagination.pageSize) + 1} đến{' '}
              {Math.min(state.pagination.page * state.pagination.pageSize, state.pagination.totalItems)} của{' '}
              {state.pagination.totalItems} kết quả
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => handlePageChange(state.pagination.page - 1)}
                disabled={state.pagination.page === 1}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeftIcon className="w-4 h-4" />
              </button>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, state.pagination.totalPages) }, (_, i) => {
                const pageNumber = Math.max(1, state.pagination.page - 2) + i;
                if (pageNumber <= state.pagination.totalPages) {
                  return (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        pageNumber === state.pagination.page
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  );
                }
                return null;
              })}

              <button
                onClick={() => handlePageChange(state.pagination.page + 1)}
                disabled={state.pagination.page === state.pagination.totalPages}
                className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRightIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 