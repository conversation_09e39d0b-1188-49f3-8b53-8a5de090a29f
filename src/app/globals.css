@import "tailwindcss";
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap");

:root {
  /* Modern Color Palette */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* Secondary Colors */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  /* Accent Colors */
  --accent-purple: #8b5cf6;
  --accent-green: #10b981;
  --accent-orange: #f59e0b;
  --accent-red: #ef4444;
  --accent-pink: #ec4899;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Light Theme (default) */
:root,
:root.light {
  /* Background & Surface */
  --background: #ffffff;
  --surface: #f8fafc;
  --surface-elevated: #ffffff;
  --surface-hover: #f1f5f9;
  --border: #e2e8f0;
  --border-light: #f1f5f9;

  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #94a3b8;
  --text-inverse: #ffffff;

  /* Status Colors - Light Mode */
  --status-success: #059669;
  --status-success-bg: #ecfdf5;
  --status-warning: #d97706;
  --status-warning-bg: #fffbeb;
  --status-error: #dc2626;
  --status-error-bg: #fef2f2;
  --status-info: #0284c7;
  --status-info-bg: #f0f9ff;

  /* Interactive Colors */
  --hover-bg: #f8fafc;
  --active-bg: #e2e8f0;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark Theme */
:root.dark {
  /* Background & Surface */
  --background: #0f172a;
  --surface: #1e293b;
  --surface-elevated: #334155;
  --surface-hover: #475569;
  --border: #475569;
  --border-light: #64748b;

  /* Text Colors */
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #94a3b8;
  --text-inverse: #0f172a;

  /* Status Colors - Dark Mode */
  --status-success: #10b981;
  --status-success-bg: #064e3b;
  --status-warning: #f59e0b;
  --status-warning-bg: #78350f;
  --status-error: #ef4444;
  --status-error-bg: #7f1d1d;
  --status-info: #3b82f6;
  --status-info-bg: #1e3a8a;

  /* Interactive Colors */
  --hover-bg: #334155;
  --active-bg: #475569;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4),
    0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.5),
    0 8px 10px -6px rgb(0 0 0 / 0.5);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--text-primary);
  --color-surface: var(--surface);
  --color-surface-elevated: var(--surface-elevated);
  --color-border: var(--border);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-tertiary: var(--text-tertiary);
  --font-sans: "Inter", system-ui, -apple-system, sans-serif;
  --font-mono: "JetBrains Mono", "Fira Code", monospace;
}

/* Base Styles */
html {
  background: var(--background);
  color: var(--text-primary);
}

body {
  background: var(--background);
  color: var(--text-primary);
  font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Prevent unwanted scrolling */
html,
body {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* Theme-aware utility classes */
.theme-bg-primary {
  background: var(--background);
}

.theme-bg-surface {
  background: var(--surface);
}

.theme-bg-surface-elevated {
  background: var(--surface-elevated);
}

.theme-bg-hover {
  background: var(--hover-bg);
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-text-tertiary {
  color: var(--text-tertiary);
}

.theme-border {
  border-color: var(--border);
}

.theme-border-light {
  border-color: var(--border-light);
}

/* Interactive utilities */
.theme-hover:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.theme-hover-subtle:hover {
  background: var(--surface-hover);
}

/* Override problematic Tailwind classes */
.bg-white {
  background: var(--surface-elevated) !important;
}

.bg-gray-50 {
  background: var(--surface) !important;
}

.bg-gray-100 {
  background: var(--hover-bg) !important;
}

.bg-gray-200 {
  background: var(--border) !important;
}

.bg-gray-800 {
  background: var(--surface-elevated) !important;
}

.bg-gray-900 {
  background: var(--surface) !important;
}

.text-gray-900 {
  color: var(--text-primary) !important;
}

.text-gray-700 {
  color: var(--text-secondary) !important;
}

.text-gray-600 {
  color: var(--text-secondary) !important;
}

.text-gray-500 {
  color: var(--text-tertiary) !important;
}

.text-gray-400 {
  color: var(--text-tertiary) !important;
}

.text-gray-300 {
  color: var(--text-secondary) !important;
}

.text-gray-100 {
  color: var(--text-primary) !important;
}

.text-white {
  color: var(--text-primary) !important;
}

.border-gray-200 {
  border-color: var(--border) !important;
}

.border-gray-300 {
  border-color: var(--border) !important;
}

.border-gray-700 {
  border-color: var(--border) !important;
}

.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  border-color: var(--border) !important;
}

.divide-gray-700 > :not([hidden]) ~ :not([hidden]) {
  border-color: var(--border) !important;
}

/* Hover state overrides */
.hover\\:bg-gray-50:hover {
  background: var(--hover-bg) !important;
}

.hover\\:bg-gray-100:hover {
  background: var(--hover-bg) !important;
}

.hover\\:bg-gray-700:hover {
  background: var(--hover-bg) !important;
}

.hover\\:text-gray-800:hover {
  color: var(--text-primary) !important;
}

.hover\\:text-gray-100:hover {
  color: var(--text-primary) !important;
}

.hover\\:text-gray-300:hover {
  color: var(--text-secondary) !important;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: var(--radius-sm);
  border: 2px solid var(--surface);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* For specific overflow containers */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: var(--surface);
  border-radius: var(--radius-sm);
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: var(--radius-sm);
  border: 2px solid var(--surface);
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Typography Classes */
.text-heading-1 {
  font-size: 2.25rem;
  font-weight: 800;
  line-height: 1.2;
  letter-spacing: -0.025em;
  color: var(--text-primary);
}

.text-heading-2 {
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 1.3;
  letter-spacing: -0.025em;
  color: var(--text-primary);
}

.text-heading-3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  color: var(--text-primary);
}

.text-body-large {
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-secondary);
}

.text-body {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-secondary);
}

.text-body-small {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--text-tertiary);
}

.text-caption {
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.4;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Button Styles */
.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-500) 0%,
    var(--primary-600) 100%
  );
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(
    135deg,
    var(--primary-600) 0%,
    var(--primary-700) 100%
  );
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--surface-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--surface);
  border-color: var(--secondary-300);
  box-shadow: var(--shadow-sm);
}

/* Card Styles */
.card {
  background: var(--surface-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border);
}

.card-elevated {
  background: var(--surface-elevated);
  border: none;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

/* Input Styles */
.input {
  background: var(--surface-elevated);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(14 165 233 / 0.1);
}

.input::placeholder {
  color: var(--text-tertiary);
}

/* Status Colors */
.status-success {
  color: var(--status-success);
  background: var(--status-success-bg);
}

.status-warning {
  color: var(--status-warning);
  background: var(--status-warning-bg);
}

.status-error {
  color: var(--status-error);
  background: var(--status-error-bg);
}

.status-info {
  color: var(--status-info);
  background: var(--status-info-bg);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-blob {
  animation: blob 7s infinite;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}
