"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter, useParams } from "next/navigation";
import {
  FolderIcon,
  DocumentIcon,
  ArrowLeftIcon,
  CloudArrowUpIcon,
  TrashIcon,
  PencilIcon,
  ShareIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  EyeIcon,
  PhotoIcon,
  FilmIcon,
  DocumentTextIcon,
  ArchiveBoxIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  FolderPlusIcon,
  ChartBarIcon,
  CloudIcon,
  ClockIcon,
  DocumentArrowDownIcon,
  Cog6ToothIcon
} from "@heroicons/react/24/outline";
import { FolderService } from "@/api/services/folderService";
import { FileService } from "@/api/services/fileService";
import { ChunkedUploadService } from "@/api/services/chunkedUploadService";
import { ApiClient } from "@/api/core/apiClient";
import { useAuth } from "@/contexts/AuthContext";

import { RoleG<PERSON>, AdminOnly } from "@/components/auth/RoleGuard";
import { DeleteConfirmationModal } from "@/components/modals/DeleteConfirmationModal";
import { CreateFolderModal } from "@/components/modals/CreateFolderModal";
import { FileDetailModal } from "@/components/modals/FileDetailModal";
import { ShareModal } from "@/components/modals/ShareModal";
import { BulkOperationsModal } from "@/components/modals/BulkOperationsModal";
import { FilePreviewModal } from "@/components/modals/FilePreviewModal";
import { MultipleFileUploadModal } from "@/components/modals/MultipleFileUploadModal";
import { RecycleBinModal } from "@/components/modals/RecycleBinModal";
import { PermissionsModal } from "@/components/modals/PermissionsModal";
import { SyncModal } from "@/components/modals/SyncModal";

import { showToast } from "@/components/ui/ToastProvider";
import { useAlertModal } from "@/components/modals/AlertModal";
import {
  FolderDto,
  FileDto,
  FolderContentsResponse,
  UploadOptions,
  ChunkedUploadOptions,
  FolderCreateData,
  SortField,
  SortDirection,
} from "@/api/types/interfaces";

interface UploadProgress {
  fileId: string;
  fileName: string;
  progress: number;
  status: "uploading" | "completed" | "error";
  error?: string;
}

interface ContentItem {
  id: string;
  name: string;
  type: "folder" | "file";
  createdAt: string;
  updatedAt: string;
  ownerId: string;
  ownerName?: string;
  // Folder specific properties
  path?: string;
  fileCount?: number;
  subfolderCount?: number;
  // File specific properties
  displayName?: string;
  fileSize?: number;
  mimeType?: string;
  description?: string;
  storageProvider?: string;
  version?: number;
  hashMd5?: string;
  hashSha256?: string;
}

interface FolderDetailState {
  folder: FolderDto | null;
  contents: ContentItem[];
  loading: boolean;
  uploading: boolean;
  error: string | null;
  uploadProgress: UploadProgress[];
  selectedFiles: Set<string>;
  showUploadZone: boolean;
  showDeleteModal: boolean;
  deleting: boolean;
  showNewFolderModal: boolean;
  // New modal states
  showFileDetailModal: boolean;
  showShareModal: boolean;
  showBulkOperationsModal: boolean;
  showFilePreviewModal: boolean;
  selectedFile: FileDto | null;
  shareItem: { id: string; type: "file" | "folder"; name: string } | null;
  // New states for enhanced features
  folderStats: {
    totalSize: number;
    fileCount: number;
    folderCount: number;
    lastModified: string;
    sizeBreakdown: {
      documents: number;
      images: number;
      videos: number;
      others: number;
    };
  } | null;
  syncStatus: {
    googleDrive?: {
      status: "not_synced" | "syncing" | "synced" | "error";
      lastSync?: string;
      folderId?: string;
      errorMessage?: string;
    };
  } | null;
  showStatsModal: boolean;
  showSyncModal: boolean;
  showAuditModal: boolean;
  showExportModal: boolean;
  showMultipleFileUploadModal: boolean;
  auditLogs: Array<{
    id: string;
    action: string;
    userId: string;
    userName?: string;
    timestamp: string;
    details: any;
    ipAddress?: string;
    userAgent?: string;
  }>;
  loadingStats: boolean;
  loadingSync: boolean;
  loadingAudit: boolean;
  showRecycleBinModal: boolean;
  showPermissionsModal: boolean;
  permissionsItem: { id: string; type: "file" | "folder"; name: string } | null;
}

export default function FolderDetailPage() {
  const { user } = useAuth();
  const { showAlert, AlertModal } = useAlertModal();
  const router = useRouter();
  const params = useParams();
  const folderId = params.folderId as string;

  // API services
  const apiClient = useMemo(
    () =>
      new ApiClient(
        process.env.NEXT_PUBLIC_API_BASE_URL || "https://localhost:7040",
        user?.access_token || ""
      ),
    [user?.access_token]
  );

  const folderService = useMemo(
    () => new FolderService(apiClient),
    [apiClient]
  );
  const fileService = useMemo(() => new FileService(apiClient), [apiClient]);
  const chunkedUploadService = useMemo(
    () => new ChunkedUploadService(apiClient),
    [apiClient]
  );

  const [state, setState] = useState<FolderDetailState>({
    folder: null,
    contents: [],
    loading: true,
    uploading: false,
    error: null,
    uploadProgress: [],
    selectedFiles: new Set(),
    showUploadZone: false,
    showDeleteModal: false,
    deleting: false,
    showNewFolderModal: false,
    // Initialize new modal states
    showFileDetailModal: false,
    showShareModal: false,
    showBulkOperationsModal: false,
    showFilePreviewModal: false,
    selectedFile: null,
    shareItem: null,
    // New states for enhanced features
    folderStats: null,
    syncStatus: null,
    showStatsModal: false,
    showSyncModal: false,
    showAuditModal: false,
    showExportModal: false,
    showMultipleFileUploadModal: false,
    auditLogs: [],
    loadingStats: false,
    loadingSync: false,
    loadingAudit: false,
    showRecycleBinModal: false,
    showPermissionsModal: false,
    permissionsItem: null,
  });

  // Load folder details and contents
  const loadFolderDetails = useCallback(async () => {
    if (!user?.access_token || !folderId) return;

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      // Load folder details
      const folderDetailsResponse: any = await folderService.getById(folderId);
      const folderDetails = folderDetailsResponse.data || folderDetailsResponse; // API might wrap in data object
      
      // Load folder contents (both folders and files)
      const contentsResponse: any =
        await folderService.getContents(folderId, {
          page: 1,
          pageSize: 100,
          sortBy: SortField.CreatedAt,
          sortDirection: SortDirection.DESC,
        });

      // Extract data from response - handle if wrapped in 'data' object
      const responseData = contentsResponse.data || contentsResponse;
      const subfolders = responseData.subfolders || [];
      const files = responseData.files?.items || responseData.files || [];

      // Transform subfolders and files into ContentItem format based on new API structure
      const folderItems: ContentItem[] = subfolders.map(
        (folder: any) => ({
          id: folder.id,
          name: folder.name,
          type: "folder" as const,
          createdAt: folder.createdAt,
          updatedAt: folder.updatedAt,
          ownerId: folder.ownerId,
          ownerName: folder.ownerName,
          path: folder.path,
          fileCount: folder.fileCount || 0,
          subfolderCount: folder.subfolderCount || 0,
        })
      );

      const fileItems: ContentItem[] = files.map((file: any) => ({
        id: file.id,
        name: file.name,
        type: "file" as const,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
        ownerId: file.ownerId,
        ownerName: file.ownerName,
        displayName: file.displayName || file.name,
        fileSize: file.fileSize || 0,
        mimeType: file.mimeType || "",
        description: file.description || "",
        storageProvider: file.storageProvider || "Unknown",
        version: file.version || 1,
        hashMd5: file.hashMd5,
        hashSha256: file.hashSha256,
      }));

      // Combine and sort by creation date (newest first)
      const allContents = [...folderItems, ...fileItems].sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      setState((prev) => ({
        ...prev,
        folder: folderDetails,
        contents: allContents,
        loading: false,
      }));
    } catch (error: any) {
      console.error("Failed to load folder details:", error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error: "Failed to load folder details. Please try again.",
      }));
    }
  }, [user?.access_token, folderId, folderService]);

  // Initial load
  useEffect(() => {
    loadFolderDetails();
  }, [loadFolderDetails]);



  // Handle file download
  const handleFileDownload = useCallback(
    async (item: ContentItem) => {
      if (item.type !== "file") return;

      try {
        await fileService.downloadWithFilename({
          id: item.id,
          name: item.name,
          displayName: item.displayName,
          mimeType: item.mimeType
        });
      } catch (error: any) {
        console.error("Failed to download file:", error);
        setState((prev) => ({
          ...prev,
          error: "Failed to download file. Please try again.",
        }));
      }
    },
    [fileService]
  );

  // Handle file delete
  const handleFileDelete = useCallback(
    async (fileId: string) => {
      showAlert({
        type: 'warning',
        title: 'Delete File',
        message: 'Are you sure you want to delete this file? This action cannot be undone.',
        confirmText: 'Delete',
        cancelText: 'Cancel',
        showCancel: true,
        onConfirm: async () => {
          try {
            await fileService.delete(fileId);
            loadFolderDetails(); // Reload contents
          } catch (error: any) {
            console.error("Failed to delete file:", error);
            setState((prev) => ({
              ...prev,
              error: "Failed to delete file. Please try again.",
            }));
          }
        }
      });
    },
    [fileService, loadFolderDetails, showAlert]
  );

  // Handle folder delete confirmation
  const handleFolderDeleteClick = useCallback(() => {
    setState((prev) => ({ ...prev, showDeleteModal: true }));
  }, []);

  // Handle folder delete
  const handleFolderDelete = useCallback(async () => {
    if (!state.folder) return;

    setState((prev) => ({ ...prev, deleting: true }));

    try {
      await folderService.delete(state.folder.id);
      router.push("/manager-folders");
    } catch (error: any) {
      console.error("Failed to delete folder:", error);
      setState((prev) => ({
        ...prev,
        deleting: false,
        showDeleteModal: false,
        error: "Failed to delete folder. Please try again.",
      }));
    }
  }, [state.folder, folderService, router]);

  // Handle create subfolder
  const handleCreateSubfolder = useCallback(
    async (name: string, description?: string) => {
      if (!user?.access_token || !folderId) {
        console.error("Missing required data for creating subfolder:", {
          hasToken: !!user?.access_token,
          folderId,
        });
        setState((prev) => ({
          ...prev,
          showNewFolderModal: false,
          error:
            "Cannot create subfolder: missing authentication or parent folder ID.",
        }));
        return;
      }

      try {
        const createData: FolderCreateData = {
          name,
          description,
          parentFolderId: folderId, // Use folderId from router params directly
        };

        console.log("Creating subfolder with data:", createData);

        const newFolder = await folderService.create(createData);

        console.log("Subfolder created successfully:", newFolder);

        setState((prev) => ({ ...prev, showNewFolderModal: false }));

        // Reload folder contents to show the new subfolder
        loadFolderDetails();
      } catch (error: any) {
        console.error("Failed to create subfolder:", error);
        setState((prev) => ({
          ...prev,
          showNewFolderModal: false,
          error:
            error?.message || "Failed to create subfolder. Please try again.",
        }));
      }
    },
    [user?.access_token, folderId, folderService, loadFolderDetails]
  );

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (
      !dateString ||
      dateString === "" ||
      dateString === "undefined" ||
      dateString === "null"
    ) {
      return "N/A";
    }

    try {
      const date = new Date(dateString);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return "N/A";
      }

      return new Intl.DateTimeFormat("vi-VN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      }).format(date);
    } catch (error) {
      console.warn("Invalid date string:", dateString);
      return "N/A";
    }
  };

  // Get file icon
  const getFileIcon = (item: ContentItem) => {
    if (item.type !== "file") return null;

    const extension = item.name.split(".").pop()?.toLowerCase();

    if (
      ["jpg", "jpeg", "png", "gif", "bmp", "svg", "webp"].includes(
        extension || ""
      )
    ) {
      return <PhotoIcon className="w-5 h-5 text-purple-500" />;
    }
    if (["mp4", "avi", "mov", "wmv", "flv", "webm"].includes(extension || "")) {
      return <FilmIcon className="w-5 h-5 text-red-500" />;
    }
    if (["pdf"].includes(extension || "")) {
      return <DocumentTextIcon className="w-5 h-5 text-red-600" />;
    }
    return <DocumentIcon className="w-5 h-5 text-gray-500" />;
  };

  // New handler functions for enhanced features
  const handleFileClick = (file: ContentItem) => {
    if (file.type !== "file") return;

    // Convert ContentItem to FileDto for the modal
    const fileDto: FileDto = {
      id: file.id,
      name: file.name,
      displayName: file.displayName || file.name,
      description: file.description || "",
      fileSize: file.fileSize || 0,
      mimeType: file.mimeType || "",
      createdAt: file.createdAt,
      updatedAt: file.updatedAt,
      ownerId: file.ownerId,
      ownerName: file.ownerName,
      tags: [],
      parentFolderId: folderId,
      isArchived: false,
      version: file.version || 1,
      filePath: "",
      storageProvider: file.storageProvider as any || "local" as any,
      permissions: [],
      isShared: false,
    };

    setState((prev) => ({
      ...prev,
      selectedFile: fileDto,
      showFileDetailModal: true,
    }));
  };

  const handleFilePreview = (file: ContentItem) => {
    if (file.type !== "file") return;

    // Convert ContentItem to FileDto for the modal
    const fileDto: FileDto = {
      id: file.id,
      name: file.name,
      displayName: file.displayName || file.name,
      description: file.description || "",
      fileSize: file.fileSize || 0,
      mimeType: file.mimeType || "",
      createdAt: file.createdAt,
      updatedAt: file.updatedAt,
      ownerId: file.ownerId,
      ownerName: file.ownerName,
      tags: [],
      parentFolderId: folderId,
      isArchived: false,
      version: file.version || 1,
      filePath: "",
      storageProvider: file.storageProvider as any || "local" as any,
      permissions: [],
      isShared: false,
    };

    setState((prev) => ({
      ...prev,
      selectedFile: fileDto,
      showFilePreviewModal: true,
    }));
  };

  const handleShare = (item: ContentItem) => {
    setState((prev) => ({
      ...prev,
      shareItem: {
        id: item.id,
        type: item.type,
        name: item.name,
      },
      showShareModal: true,
    }));
  };

  const handleBulkOperations = () => {
    setState((prev) => ({
      ...prev,
      showBulkOperationsModal: true,
    }));
  };

  const handleFileUpdated = (updatedFile: FileDto) => {
    console.log("File updated:", updatedFile);
    // Reload folder contents to reflect changes
    loadFolderDetails();
  };

  const handleFileDeleted = (fileId: string) => {
    console.log("File deleted:", fileId);
    // Reload folder contents to reflect changes
    loadFolderDetails();
  };

  const handleOperationComplete = () => {
    // Reload content and clear selections
    setState((prev) => ({
      ...prev,
      selectedFiles: new Set(),
      showBulkOperationsModal: false
    }));

    loadFolderDetails();
  };

  // Detect duplicate files
  const detectDuplicateFiles = useCallback(() => {
    const fileContents = state.contents.filter(item => item.type === "file");
    const duplicates: { [key: string]: ContentItem[] } = {};
    
    // Group files by name and size
    fileContents.forEach(file => {
      const key = `${file.name}_${file.fileSize}`;
      if (!duplicates[key]) {
        duplicates[key] = [];
      }
      duplicates[key].push(file);
    });
    
    // Filter out groups that have only one file (no duplicates)
    const actualDuplicates = Object.entries(duplicates)
      .filter(([_, files]) => files.length > 1)
      .reduce((acc, [key, files]) => {
        acc[key] = files;
        return acc;
      }, {} as { [key: string]: ContentItem[] });
    
    return actualDuplicates;
  }, [state.contents]);

  // Perform duplicate removal
  const performDuplicateRemoval = useCallback(async (filesToRemove: ContentItem[]) => {
    setState(prev => ({ ...prev, loading: true }));

    try {
      let deletedCount = 0;
      for (const file of filesToRemove) {
        try {
          await fileService.delete(file.id);
          deletedCount++;
        } catch (error) {
          console.error(`Failed to delete file ${file.name}:`, error);
        }
      }

      showToast.success(`Đã xóa ${deletedCount} file trùng lặp thành công`);
      loadFolderDetails(); // Reload to refresh the list
    } catch (error) {
      console.error("Failed to remove duplicates:", error);
      showToast.error("Có lỗi xảy ra khi xóa file trùng lặp");
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  }, [fileService, loadFolderDetails]);

  // Handle duplicate file removal
  const handleRemoveDuplicates = useCallback(async () => {
    const duplicates = detectDuplicateFiles();
    const duplicateGroups = Object.values(duplicates);
    
    if (duplicateGroups.length === 0) {
      showToast.info("Không tìm thấy file trùng lặp");
      return;
    }

    // Count total duplicates to remove (keep only the newest file in each group)
    let filesToRemove: ContentItem[] = [];
    duplicateGroups.forEach(group => {
      // Sort by creation date (newest first), keep the first one, remove others
      const sorted = group.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      filesToRemove.push(...sorted.slice(1)); // Remove all except the newest
    });

    showAlert({
      type: 'warning',
      title: 'Xóa File Trùng Lặp',
      message: `Tìm thấy ${filesToRemove.length} file trùng lặp. Bạn có muốn xóa chúng không?\n\nLưu ý: File mới nhất trong mỗi nhóm sẽ được giữ lại.`,
      confirmText: 'Xóa',
      cancelText: 'Hủy',
      showCancel: true,
      onConfirm: async () => {
        await performDuplicateRemoval(filesToRemove);
      }
    });
  }, [detectDuplicateFiles, showAlert, performDuplicateRemoval]);

  // Handle folder name update
  const handleFolderNameUpdate = useCallback(
    async (newName: string) => {
      if (!state.folder || !user?.access_token) {
        throw new Error("Cannot update folder name: missing folder or authentication");
      }

      try {
        const updateData = {
          name: newName,
          description: state.folder.description,
        };

        const updatedFolder = await folderService.update(state.folder.id, updateData);
        
        setState((prev) => ({
          ...prev,
          folder: updatedFolder,
        }));

        // Reload to get updated data
        loadFolderDetails();
      } catch (error: any) {
        console.error("Failed to update folder name:", error);
        throw new Error(error.message || "Failed to update folder name");
      }
    },
    [state.folder, user?.access_token, folderService, loadFolderDetails]
  );

  // Handle file name update
  const handleFileNameUpdate = useCallback(
    async (fileId: string, newName: string) => {
      if (!user?.access_token) {
        throw new Error("Cannot update file name: missing authentication");
      }

      try {
        const updateData = {
          displayName: newName,
        };

        await fileService.update(fileId, updateData);
        
        // Reload to get updated data
        loadFolderDetails();
      } catch (error: any) {
        console.error("Failed to update file name:", error);
        throw new Error(error.message || "Failed to update file name");
      }
    },
    [user?.access_token, fileService, loadFolderDetails]
  );

  // Handle folder name update (for subfolder in table)
  const handleSubfolderNameUpdate = useCallback(
    async (folderId: string, newName: string) => {
      if (!user?.access_token) {
        throw new Error("Cannot update folder name: missing authentication");
      }

      try {
        const updateData = {
          name: newName,
        };

        await folderService.update(folderId, updateData);
        
        // Reload to get updated data
        loadFolderDetails();
      } catch (error: any) {
        console.error("Failed to update folder name:", error);
        throw new Error(error.message || "Failed to update folder name");
      }
    },
    [user?.access_token, folderService, loadFolderDetails]
  );

  // New handler functions for enhanced APIs
  const loadFolderStatistics = useCallback(async () => {
    if (!user?.access_token || !folderId) return;

    setState(prev => ({ ...prev, loadingStats: true }));

    try {
      const stats = await folderService.getStatistics(folderId);
      setState(prev => ({
        ...prev,
        folderStats: stats,
        loadingStats: false
      }));
    } catch (error: any) {
      console.error('Failed to load folder statistics:', error);
      setState(prev => ({
        ...prev,
        loadingStats: false,
        error: 'Failed to load folder statistics: ' + (error.message || 'Unknown error')
      }));
    }
  }, [user?.access_token, folderId, folderService]);

  const loadSyncStatus = useCallback(async () => {
    if (!user?.access_token || !folderId) return;

    setState(prev => ({ ...prev, loadingSync: true }));

    try {
      const syncStatus = await folderService.getSyncStatus(folderId);
      setState(prev => ({
        ...prev,
        syncStatus,
        loadingSync: false
      }));
    } catch (error: any) {
      console.error('Failed to load sync status:', error);
      setState(prev => ({
        ...prev,
        loadingSync: false,
        error: 'Failed to load sync status: ' + (error.message || 'Unknown error')
      }));
    }
  }, [user?.access_token, folderId, folderService]);

  const loadAuditLogs = useCallback(async () => {
    if (!user?.access_token || !folderId) return;

    setState(prev => ({ ...prev, loadingAudit: true }));

    try {
      const response = await folderService.getAuditLogs(folderId, {
        page: 1,
        pageSize: 20
      });
      setState(prev => ({
        ...prev,
        auditLogs: response.items,
        loadingAudit: false
      }));
    } catch (error: any) {
      console.error('Failed to load audit logs:', error);
      setState(prev => ({
        ...prev,
        loadingAudit: false,
        error: 'Failed to load audit logs: ' + (error.message || 'Unknown error')
      }));
    }
  }, [user?.access_token, folderId, folderService]);

  const handleSyncToGoogleDrive = useCallback(async () => {
    if (!user?.access_token || !folderId) return;

    try {
      setState(prev => ({ ...prev, loadingSync: true }));
      
      const result = await folderService.syncToGoogleDrive(folderId, {
        syncSubfolders: true,
        conflictResolution: 'rename'
      });

      console.log('Sync initiated:', result);
      
      // Reload sync status after initiating sync
      loadSyncStatus();
      
      setState(prev => ({
        ...prev,
        showSyncModal: false,
        error: null
      }));
    } catch (error: any) {
      console.error('Failed to sync to Google Drive:', error);
      setState(prev => ({
        ...prev,
        loadingSync: false,
        error: 'Failed to sync to Google Drive: ' + (error.message || 'Unknown error')
      }));
    }
  }, [user?.access_token, folderId, folderService, loadSyncStatus]);

  const handleExportStructure = useCallback(async (format: 'json' | 'csv' | 'xml') => {
    if (!user?.access_token || !folderId) return;

    try {
      const response = await folderService.exportStructure({
        format,
        includeFiles: true,
        includePermissions: true,
        parentFolderId: folderId
      });

      // Create a download link
      const link = document.createElement('a');
      link.href = response.downloadUrl;
      link.download = `folder-structure-${state.folder?.name}-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setState(prev => ({
        ...prev,
        showExportModal: false,
        error: null
      }));
    } catch (error: any) {
      console.error('Failed to export folder structure:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to export folder structure: ' + (error.message || 'Unknown error')
      }));
    }
  }, [user?.access_token, folderId, folderService, state.folder?.name]);

  if (state.loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-body text-gray-600 dark:text-gray-300">Loading folder details...</p>
        </div>
      </div>
    );
  }


  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          {/* Back Button + Title Row */}
          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={() => router.push("/manager-folders")}
              className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex-shrink-0"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </button>
            
            {/* Folder Title */}
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <FolderIcon className="w-8 h-8 text-blue-500 dark:text-blue-400 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2">
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 truncate">
                    {state.folder?.name}
                  </h1>
                  <RoleGuard requiredRoles={["Manager", "Admin"]} showError={false}>
                    <button
                      onClick={() => {
                        const newName = prompt("Enter new folder name:", state.folder?.name || "");
                        if (newName && newName !== state.folder?.name) {
                          handleFolderNameUpdate(newName);
                        }
                      }}
                      className="p-1 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded transition-colors flex-shrink-0"
                      title="Edit folder name"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </button>
                  </RoleGuard>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{state.folder?.path}</p>
              </div>
            </div>
          </div>

          {/* Action Buttons Row */}
          <div className="flex flex-wrap items-center gap-2 mb-4">
            <button
              onClick={() => setState(prev => ({ ...prev, showNewFolderModal: true }))}
              className="btn-primary flex items-center gap-2"
              title="Create Subfolder"
            >
              <FolderPlusIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Create Subfolder</span>
              <span className="sm:hidden">New</span>
            </button>
            
            <button
              onClick={() => setState(prev => ({ ...prev, showMultipleFileUploadModal: true }))}
              className="btn-secondary flex items-center gap-2"
              title="Upload Multiple Files"
            >
              <ArrowUpTrayIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Upload Multiple Files</span>
              <span className="sm:hidden">Upload</span>
            </button>

            <button
              onClick={() => setState(prev => ({ ...prev, showRecycleBinModal: true }))}
              className="btn-secondary flex items-center gap-2"
              title="Thùng Rác"
            >
              <TrashIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Thùng Rác</span>
              <span className="sm:hidden">Trash</span>
            </button>

            {/* Icon-only buttons for smaller screens */}
            <div className="flex items-center gap-1">
              {/* Statistics Button */}
              <button
                onClick={() => {
                  setState(prev => ({ ...prev, showStatsModal: true }));
                  loadFolderStatistics();
                }}
                className="p-2 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
                title="View Statistics"
              >
                <ChartBarIcon className="w-5 h-5" />
              </button>

              {/* Sync Button */}
              <button
                onClick={() => {
                  setState(prev => ({ ...prev, showSyncModal: true }));
                  loadSyncStatus();
                }}
                className="p-2 text-gray-400 dark:text-gray-500 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/50 rounded-lg transition-colors"
                title="Google Drive Sync"
              >
                <CloudIcon className="w-5 h-5" />
              </button>

              {/* Audit Logs Button */}
              <button
                onClick={() => {
                  setState(prev => ({ ...prev, showAuditModal: true }));
                  loadAuditLogs();
                }}
                className="p-2 text-gray-400 dark:text-gray-500 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/50 rounded-lg transition-colors"
                title="View Audit Logs"
              >
                <ClockIcon className="w-5 h-5" />
              </button>

              {/* Export Button */}
              <button
                onClick={() => setState(prev => ({ ...prev, showExportModal: true }))}
                className="p-2 text-gray-400 dark:text-gray-500 hover:text-orange-600 dark:hover:text-orange-400 hover:bg-orange-50 dark:hover:bg-orange-900/50 rounded-lg transition-colors"
                title="Export Structure"
              >
                <DocumentArrowDownIcon className="w-5 h-5" />
              </button>

              {/* Remove Duplicates Button */}
              <button
                onClick={() => {
                  const duplicates = detectDuplicateFiles();
                  const duplicateCount = Object.values(duplicates).reduce((acc, group) => acc + group.length - 1, 0);
                  if (duplicateCount === 0) {
                    showToast.info("Không tìm thấy file trùng lặp");
                  } else {
                    handleRemoveDuplicates();
                  }
                }}
                disabled={state.loading}
                className="p-2 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/50 rounded-lg transition-colors disabled:opacity-50"
                title="Remove Duplicate Files"
              >
                <TrashIcon className="w-5 h-5" />
              </button>
            </div>
          </div>



          {state.folder?.description && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 dark:border-blue-500 p-4 mb-4">
              <p className="text-blue-800 dark:text-blue-200">{state.folder?.description}</p>
            </div>
          )}

          {/* Folder Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {state.folder?.fileCount || state.contents.filter((item) => item.type === "file").length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Files</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {state.folder?.subfolderCount || state.contents.filter((item) => item.type === "folder").length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Subfolders</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-sm text-gray-600 dark:text-gray-400">Created</div>
              <div className="font-medium text-gray-900 dark:text-gray-100">
                {formatDate(state.folder?.createdAt ?? "")}
              </div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-sm text-gray-600 dark:text-gray-400">Last Modified</div>
              <div className="font-medium text-gray-900 dark:text-gray-100">
                {formatDate(state.folder?.updatedAt ?? "")}
              </div>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {state.error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400 dark:text-red-500" />
              <div className="ml-3">
                <p className="text-sm text-red-800 dark:text-red-200">{state.error}</p>
              </div>
              <button
                onClick={() => setState((prev) => ({ ...prev, error: null }))}
                className="ml-auto text-red-400 dark:text-red-500 hover:text-red-600 dark:hover:text-red-300"
              >
                <XCircleIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}



        {/* Combined Folders and Files Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Contents ({state.contents.length} items)
              </h2>
              {state.selectedFiles.size > 0 && (
                <div className="flex items-center gap-3">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {state.selectedFiles.size} selected
                  </span>
                  <button
                    onClick={handleBulkOperations}
                    className="px-3 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    Bulk Actions
                  </button>
                </div>
              )}
            </div>
          </div>

          {state.contents.length === 0 ? (
            <div className="p-12 text-center">
              <DocumentIcon className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No content found
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                Upload files or create subfolders to get started.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-blue-600 focus:ring-blue-500"
                        onChange={(e) => {
                          if (e.target.checked) {
                            setState((prev) => ({
                              ...prev,
                              selectedFiles: new Set(
                                prev.contents.map((item) => item.id)
                              ),
                            }));
                          } else {
                            setState((prev) => ({
                              ...prev,
                              selectedFiles: new Set(),
                            }));
                          }
                        }}
                        checked={
                          state.selectedFiles.size > 0 &&
                          state.selectedFiles.size === state.contents.length
                        }
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Size
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Storage
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Created Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Owner
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {state.contents.map((item) => (
                    <tr
                      key={`${item.type}-${item.id}`}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      {/* Checkbox Column */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          className="rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-blue-600 focus:ring-blue-500"
                          checked={state.selectedFiles.has(item.id)}
                          onChange={(e) => {
                            setState((prev) => {
                              const newSelected = new Set(prev.selectedFiles);
                              if (e.target.checked) {
                                newSelected.add(item.id);
                              } else {
                                newSelected.delete(item.id);
                              }
                              return { ...prev, selectedFiles: newSelected };
                            });
                          }}
                        />
                      </td>

                      {/* Type Column */}
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {item.type === "folder" ? (
                            <div className="flex items-center gap-2">
                              <FolderIcon className="w-5 h-5 text-blue-500" />
                              <span className="text-sm font-medium text-blue-700">
                                Folder
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              {getFileIcon(item)}
                              <span className="text-sm font-medium text-gray-700">
                                File
                              </span>
                            </div>
                          )}
                        </div>
                      </td>

                      {/* Name Column */}
                      <td className="px-6 py-4">
                        <div className="max-w-xs relative group">
                          {item.type === "folder" ? (
                            <div>
                                                              <div className="flex items-center gap-2">
                                <div className="flex-1 min-w-0">
                                  <button
                                    onClick={() =>
                                      router.push(`/manager-folders/${item.id}`)
                                    }
                                    className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors truncate block w-full text-left"
                                    title={`View folder: ${item.name}`}
                                  >
                                    {item.name}
                                  </button>
                                </div>
                                <RoleGuard requiredRoles={["Manager", "Admin"]} showError={false}>
                                  <button
                                    onClick={() => {
                                      const newName = prompt("Enter new folder name:", item.name);
                                      if (newName && newName !== item.name) {
                                        handleSubfolderNameUpdate(item.id, newName);
                                      }
                                    }}
                                    className="p-1 text-gray-400 hover:text-yellow-600 hover:bg-yellow-50 rounded transition-colors"
                                    title="Edit folder name"
                                  >
                                    <PencilIcon className="w-4 h-4" />
                                  </button>
                                </RoleGuard>
                                <button
                                  onClick={() =>
                                    router.push(`/manager-folders/${item.id}`)
                                  }
                                  className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                  title="View folder"
                                >
                                  <ArrowLeftIcon className="w-4 h-4 rotate-180" />
                                </button>
                              </div>
                              {item.path && (
                                <div className="text-xs text-gray-400 dark:text-gray-500 truncate">
                                  {item.path}
                                </div>
                              )}

                            </div>
                          ) : (
                            <div>
                              <div className="flex items-center gap-2">
                                <div className="flex-1 min-w-0">
                                  <button
                                    onClick={() => handleFileClick(item)}
                                    className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors truncate block text-left w-full"
                                  >
                                    {item.displayName || item.name}
                                  </button>
                                </div>
                              </div>
                              {item.description && (
                                <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                                  {item.description}
                                </div>
                              )}
                              {item.mimeType && (
                                <div className="text-xs text-gray-400 dark:text-gray-500 truncate">
                                  {item.mimeType}
                                </div>
                              )}

                            </div>
                          )}
                        </div>
                      </td>

                      {/* Size Column */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {item.type === "folder" ? (
                          <span>{item.fileCount || 0} items</span>
                        ) : (
                          <span>{formatFileSize(item.fileSize || 0)}</span>
                        )}
                      </td>

                      {/* Storage Column */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {item.type === "file" ? (
                          <div className="flex items-center gap-1">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              item.storageProvider === 'R2' ? 'bg-orange-100 text-orange-800' :
                              item.storageProvider === 'Local' ? 'bg-blue-100 text-blue-800' :
                              item.storageProvider === 'GoogleDrive' ? 'bg-green-100 text-green-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {item.storageProvider}
                            </span>
                            {item.version && item.version > 1 && (
                              <span className="text-xs text-gray-400">v{item.version}</span>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>

                      {/* Created Date Column */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(item.createdAt)}
                      </td>

                      {/* Owner Column */}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        <div className="max-w-24">
                          <div className="truncate">{item.ownerName || "Unknown"}</div>
                        </div>
                      </td>

                      {/* Actions Column */}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end gap-2">
                          {item.type === "file" ? (
                            <>
                              <button
                                onClick={() => handleFilePreview(item)}
                                className="p-2 text-gray-400 dark:text-gray-500 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/50 rounded-lg transition-colors"
                                title="Preview"
                              >
                                <EyeIcon className="w-4 h-4" />
                              </button>

                              <button
                                onClick={() => handleFileDownload(item)}
                                className="p-2 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
                                title="Download"
                              >
                                <ArrowDownTrayIcon className="w-4 h-4" />
                              </button>

                              <RoleGuard
                                requiredRoles={["Manager", "Admin"]}
                                showError={false}
                              >
                                <button
                                  onClick={() => {
                                    const newName = prompt("Enter new file name:", item.displayName || item.name);
                                    if (newName && newName !== (item.displayName || item.name)) {
                                      handleFileNameUpdate(item.id, newName);
                                    }
                                  }}
                                  className="p-2 text-gray-400 dark:text-gray-500 hover:text-yellow-600 dark:hover:text-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/50 rounded-lg transition-colors"
                                  title="Edit file name"
                                >
                                  <PencilIcon className="w-4 h-4" />
                                </button>
                                
                                <button
                                  onClick={() => handleShare(item)}
                                  className="p-2 text-gray-400 dark:text-gray-500 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/50 rounded-lg transition-colors"
                                  title="Share"
                                >
                                  <ShareIcon className="w-4 h-4" />
                                </button>
                              </RoleGuard>

                              <AdminOnly showError={false}>
                                <button
                                  onClick={() => handleFileDelete(item.id)}
                                  className="p-2 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/50 rounded-lg transition-colors"
                                  title="Delete"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </AdminOnly>
                            </>
                          ) : (
                            <>
                              <button
                                onClick={() =>
                                  router.push(`/manager-folders/${item.id}`)
                                }
                                className="p-2 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/50 rounded-lg transition-colors"
                                title="Open Folder"
                              >
                                <EyeIcon className="w-4 h-4" />
                              </button>

                              <RoleGuard
                                requiredRoles={["Manager", "Admin"]}
                                showError={false}
                              >
                                <button
                                  onClick={() => handleShare(item)}
                                  className="p-2 text-gray-400 dark:text-gray-500 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/50 rounded-lg transition-colors"
                                  title="Share Folder"
                                >
                                  <ShareIcon className="w-4 h-4" />
                                </button>
                              </RoleGuard>

                              <AdminOnly showError={false}>
                                <button
                                  onClick={() => {
                                    showAlert({
                                      type: 'warning',
                                      title: 'Delete Folder',
                                      message: `Are you sure you want to delete folder "${item.name}"? This action cannot be undone.`,
                                      confirmText: 'Delete',
                                      cancelText: 'Cancel',
                                      showCancel: true,
                                      onConfirm: () => {
                                        // Handle folder delete here
                                        console.log("Delete folder:", item.id);
                                      }
                                    });
                                  }}
                                  className="p-2 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/50 rounded-lg transition-colors"
                                  title="Delete Folder"
                                >
                                  <TrashIcon className="w-4 h-4" />
                                </button>
                              </AdminOnly>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={state.showDeleteModal}
        onClose={() =>
          setState((prev) => ({ ...prev, showDeleteModal: false }))
        }
        onConfirm={handleFolderDelete}
        title="Delete Folder"
        message="This action will permanently delete this folder and all its contents, including files and subfolders."
        itemName={state.folder?.name}
        itemType="Folder"
        danger={true}
        loading={state.deleting}
      />

      {/* Create Subfolder Modal */}
      <CreateFolderModal
        isOpen={state.showNewFolderModal}
        onClose={() =>
          setState((prev) => ({ ...prev, showNewFolderModal: false }))
        }
        onSubmit={handleCreateSubfolder}
        currentFolder={state.folder}
        loading={state.loading}
      />

      {/* File Detail Modal */}
      <FileDetailModal
        isOpen={state.showFileDetailModal}
        onClose={() =>
          setState((prev) => ({
            ...prev,
            showFileDetailModal: false,
            selectedFile: null,
          }))
        }
        file={state.selectedFile}
        fileService={fileService}
        onFileUpdated={handleFileUpdated}
        onFileDeleted={handleFileDeleted}
      />

      {/* Share Modal */}
      <ShareModal
        isOpen={state.showShareModal}
        onClose={() =>
          setState((prev) => ({
            ...prev,
            showShareModal: false,
            shareItem: null,
          }))
        }
        itemId={state.shareItem?.id || ""}
        itemType={state.shareItem?.type || "file"}
        itemName={state.shareItem?.name || ""}
        fileService={state.shareItem?.type === "file" ? fileService : undefined}
        folderService={
          state.shareItem?.type === "folder" ? folderService : undefined
        }
      />

      {/* Bulk Operations Modal */}
      <BulkOperationsModal
        isOpen={state.showBulkOperationsModal}
        onClose={() =>
          setState((prev) => ({ ...prev, showBulkOperationsModal: false }))
        }
        selectedItems={{
          files: state.contents
            .filter(
              (item) => item.type === "file" && state.selectedFiles.has(item.id)
            )
            .map((item) => item.id),
          folders: state.contents
            .filter(
              (item) =>
                item.type === "folder" && state.selectedFiles.has(item.id)
            )
            .map((item) => item.id),
        }}
        fileService={fileService}
        folderService={folderService}
        onOperationComplete={handleOperationComplete}
      />

      {/* File Preview Modal */}
      <FilePreviewModal
        isOpen={state.showFilePreviewModal}
        onClose={() => setState(prev => ({ ...prev, showFilePreviewModal: false, selectedFile: null }))}
        file={state.selectedFile}
        fileService={fileService}
      />

      {/* Multiple File Upload Modal */}
      <MultipleFileUploadModal
        isOpen={state.showMultipleFileUploadModal}
        onClose={() => setState(prev => ({ ...prev, showMultipleFileUploadModal: false }))}
        onUploadComplete={() => {
          setState(prev => ({ ...prev, showMultipleFileUploadModal: false }));
          loadFolderDetails();
        }}
        currentFolderId={folderId}
        fileService={fileService}
        chunkedUploadService={chunkedUploadService}
      />



      {/* Statistics Modal */}
      {state.showStatsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Folder Statistics</h2>
                <button
                  onClick={() => setState(prev => ({ ...prev, showStatsModal: false }))}
                  className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <XCircleIcon className="w-6 h-6" />
                </button>
              </div>
              
              {state.loadingStats ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600 dark:text-gray-300">Loading statistics...</p>
                </div>
              ) : state.folderStats ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{state.folderStats.fileCount}</div>
                      <div className="text-sm text-gray-600">Files</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{state.folderStats.folderCount}</div>
                      <div className="text-sm text-gray-600">Subfolders</div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{formatFileSize(state.folderStats.totalSize)}</div>
                      <div className="text-sm text-gray-600">Total Size</div>
                    </div>
                    <div className="bg-orange-50 p-4 rounded-lg">
                      <div className="text-sm font-medium text-orange-800">Last Modified</div>
                      <div className="text-xs text-gray-600">{formatDate(state.folderStats.lastModified)}</div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-medium text-gray-900 mb-3">Size Breakdown</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-800">{formatFileSize(state.folderStats.sizeBreakdown.documents)}</div>
                        <div className="text-xs text-gray-600">Documents</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-800">{formatFileSize(state.folderStats.sizeBreakdown.images)}</div>
                        <div className="text-xs text-gray-600">Images</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-800">{formatFileSize(state.folderStats.sizeBreakdown.videos)}</div>
                        <div className="text-xs text-gray-600">Videos</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-800">{formatFileSize(state.folderStats.sizeBreakdown.others)}</div>
                        <div className="text-xs text-gray-600">Others</div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600 dark:text-gray-300">Failed to load statistics</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Sync Modal */}
      {state.showSyncModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-lg w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Google Drive Sync</h2>
                <button
                  onClick={() => setState(prev => ({ ...prev, showSyncModal: false }))}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircleIcon className="w-6 h-6" />
                </button>
              </div>
              
              {state.loadingSync ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading sync status...</p>
                </div>
              ) : state.syncStatus?.googleDrive ? (
                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">Status:</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        state.syncStatus.googleDrive.status === 'synced' ? 'bg-green-100 text-green-800' :
                        state.syncStatus.googleDrive.status === 'syncing' ? 'bg-blue-100 text-blue-800' :
                        state.syncStatus.googleDrive.status === 'error' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {state.syncStatus.googleDrive.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    {state.syncStatus.googleDrive.lastSync && (
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-sm text-gray-600">Last Sync:</span>
                        <span className="text-sm text-gray-800">{formatDate(state.syncStatus.googleDrive.lastSync)}</span>
                      </div>
                    )}
                    {state.syncStatus.googleDrive.errorMessage && (
                      <div className="mt-2">
                        <span className="text-sm text-red-600">{state.syncStatus.googleDrive.errorMessage}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex gap-3">
                    <button
                      onClick={handleSyncToGoogleDrive}
                      disabled={state.syncStatus.googleDrive.status === 'syncing'}
                      className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {state.syncStatus.googleDrive.status === 'syncing' ? 'Syncing...' : 'Sync Now'}
                    </button>
                    <button
                      onClick={loadSyncStatus}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                      title="Refresh Status"
                    >
                      <Cog6ToothIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600 mb-4">Google Drive sync not configured</p>
                  <button
                    onClick={handleSyncToGoogleDrive}
                    className="btn-primary"
                  >
                    Setup Sync
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Audit Logs Modal */}
      {state.showAuditModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Audit Logs</h2>
                <button
                  onClick={() => setState(prev => ({ ...prev, showAuditModal: false }))}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircleIcon className="w-6 h-6" />
                </button>
              </div>
              
              {state.loadingAudit ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading audit logs...</p>
                </div>
              ) : state.auditLogs.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {state.auditLogs.map((log) => (
                        <tr key={log.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {log.action}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {log.userName || log.userId}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {formatDate(log.timestamp)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                            {log.ipAddress || 'N/A'}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-600">
                            <pre className="text-xs bg-gray-50 p-2 rounded overflow-x-auto">
                              {JSON.stringify(log.details, null, 2)}
                            </pre>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600">No audit logs found</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Export Modal */}
      {state.showExportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-lg w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Export Folder Structure</h2>
                <button
                  onClick={() => setState(prev => ({ ...prev, showExportModal: false }))}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircleIcon className="w-6 h-6" />
                </button>
              </div>
              
              <div className="space-y-4">
                <p className="text-gray-600 text-sm">
                  Choose the format to export the folder structure and metadata:
                </p>
                
                <div className="grid grid-cols-3 gap-3">
                  <button
                    onClick={() => handleExportStructure('json')}
                    className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-center"
                  >
                    <DocumentIcon className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                    <div className="font-medium text-sm">JSON</div>
                    <div className="text-xs text-gray-500">Structured data</div>
                  </button>
                  
                  <button
                    onClick={() => handleExportStructure('csv')}
                    className="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors text-center"
                  >
                    <DocumentTextIcon className="w-8 h-8 text-green-500 mx-auto mb-2" />
                    <div className="font-medium text-sm">CSV</div>
                    <div className="text-xs text-gray-500">Spreadsheet</div>
                  </button>
                  
                  <button
                    onClick={() => handleExportStructure('xml')}
                    className="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors text-center"
                  >
                    <DocumentIcon className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                    <div className="font-medium text-sm">XML</div>
                    <div className="text-xs text-gray-500">Markup</div>
                  </button>
                </div>
                
                <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                  <strong>Note:</strong> The export will include folder structure, file metadata, and permissions. 
                  File contents are not included in the export.
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Alert Modal */}
      <AlertModal />

      {/* Recycle Bin Modal */}
      <RecycleBinModal
        isOpen={state.showRecycleBinModal}
        onClose={() => setState((prev) => ({ ...prev, showRecycleBinModal: false }))}
        onItemRestored={() => {
          // Reload folder contents to reflect restored items
          loadFolderDetails();
        }}
        onItemDeleted={() => {
          // No need to reload as item is permanently deleted
        }}
      />

      {/* Permissions Modal */}
      {state.permissionsItem && (
        <PermissionsModal
          isOpen={state.showPermissionsModal}
          onClose={() => setState((prev) => ({ ...prev, showPermissionsModal: false, permissionsItem: null }))}
          itemId={state.permissionsItem.id}
          itemType={state.permissionsItem.type}
          itemName={state.permissionsItem.name}
        />
      )}
    </div>
  );
}
