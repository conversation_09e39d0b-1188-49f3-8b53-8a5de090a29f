"use client";

import React, {
  useState,
  useEffect,
  use<PERSON><PERSON>back,
  useMemo,
  useRef,
} from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  FolderIcon,
  DocumentIcon,
  MagnifyingGlassIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  Bars3Icon,
  ViewColumnsIcon,
  FolderPlusIcon,
  EyeIcon,
  AdjustmentsHorizontalIcon,
  DocumentArrowDownIcon,
  XMarkIcon,
  CloudIcon,
} from "@heroicons/react/24/outline";
import { CreateFolderModal } from "@/components/modals/CreateFolderModal";
import { FileDetailModal } from "@/components/modals/FileDetailModal";
import { ShareModal } from "@/components/modals/ShareModal";
import { BulkOperationsModal } from "@/components/modals/BulkOperationsModal";
import { FilePreviewModal } from "@/components/modals/FilePreviewModal";
import { RecycleBinModal } from "@/components/modals/RecycleBinModal";
import { PermissionsModal } from "@/components/modals/PermissionsModal";
import { SyncModal } from "@/components/modals/SyncModal";
import { FolderService } from "@/api/services/folderService";
import { FileService } from "@/api/services/fileService";

import {
  FolderListSkeleton,
  FolderGridSkeleton,
  EmptyState,
} from "@/components/ui/LoadingSkeleton";
import {
  DragDropProvider,
  DroppableArea,
  DraggableItem,
  useDragDrop,
} from "@/components/ui/DragDropProvider";
import { showToast } from "@/components/ui/ToastProvider";
import { ThemeToggle } from "@/components/ui/ThemeToggle";
import { motion, AnimatePresence } from "framer-motion";
import { AlertModal, useAlertModal } from "@/components/modals/AlertModal";

import { ApiClient } from "@/api/core/apiClient";
import { useAuth } from "@/contexts/AuthContext";
import {
  FolderDto,
  FileDto,
  SortField,
  SortDirection,
  FolderContentsResponse,
  FolderCreateData,
} from "@/api/types/interfaces";
import { RoleGuard, AdminOnly } from "@/components/auth/RoleGuard";


interface ContentItem {
  id: string;
  name: string;
  type: "folder" | "file";
  createdAt: string;
  updatedAt: string;
  ownerId: string;
  ownerName?: string;
  // Folder specific properties
  path?: string;
  fileCount?: number;
  subfolderCount?: number;
  description?: string;
  // File specific properties
  displayName?: string;
  fileSize?: number;
  mimeType?: string;
}

type ContentSortBy = "name" | "createdAt" | "updatedAt" | "size" | "type";

interface FolderManagerState {
  currentFolder: FolderDto | null;
  breadcrumb: FolderDto[];
  contents: ContentItem[]; // Combined folders and files
  folders: FolderDto[]; // Separate folders array for easier access
  files: FileDto[]; // Separate files array for easier access
  selectedItems: Set<string>;
  loading: boolean;
  loadingMessage: string; // Message to show while loading
  canCancelLoading: boolean; // Whether loading can be cancelled
  error: string | null;
  apiDisabled: boolean; // Prevent further API calls on repeated errors
  errorCount: number; // Track consecutive errors
  searchTerm: string;
  filters: {
    itemType: "all" | "folders" | "files"; // Filter by type like Google Drive
    folderType: "all" | "public" | "private" | "shared";
    uploaderEmail: string;
    mimeType: string; // Filter by file type
    dateRange: {
      from: string;
      to: string;
    };
  };
  sorting: {
    field: ContentSortBy; // Use content sort options
    direction: SortDirection;
  };
  pagination: {
    page: number;
    pageSize: number;
    totalItems: number;
    totalPages: number;
    folderCount: number;
    fileCount: number;
  };
  viewMode: "list" | "grid";
  showNewFolderModal: boolean;
  showBulkActions: boolean;
  // New modal states
  showFileDetailModal: boolean;
  showShareModal: boolean;
  showBulkOperationsModal: boolean;
  showFilePreviewModal: boolean;
  selectedFile: FileDto | null;
  selectedFolder: FolderDto | null;
  shareItem: { id: string; type: "file" | "folder"; name: string } | null;
  // Advanced search states
  showAdvancedSearch: boolean;
  advancedSearchFilters: {
    query: string;
    tags: string[];
    createdAfter: string;
    createdBefore: string;
    modifiedAfter: string;
    modifiedBefore: string;
    minSize: string;
    maxSize: string;
    ownerIds: string[];
    includeSubfolders: boolean;
  };
  showExportModal: boolean;
  showFolderUploadModal: boolean;
  showRecycleBinModal: boolean;
  showPermissionsModal: boolean;
  permissionsItem: { id: string; type: "file" | "folder"; name: string } | null;
  showSyncModal: boolean;
}

export default function FolderManagerPage() {
  const { user, getUserRoles } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { showAlert, AlertModal: AlertModalComponent } = useAlertModal();

  // AbortController for cancelling loading operations
  const abortControllerRef = useRef<AbortController | null>(null);

  // Stabilize API client and services with useMemo to prevent recreation
  const apiClient = useMemo(
    () =>
      new ApiClient(
        process.env.NEXT_PUBLIC_API_BASE_URL || "https://localhost:7040",
        user?.access_token || ""
      ),
    [user?.access_token]
  );

  const folderService = useMemo(
    () => new FolderService(apiClient),
    [apiClient]
  );
  const fileService = useMemo(() => new FileService(apiClient), [apiClient]);

  const [state, setState] = useState<FolderManagerState>({
    currentFolder: null,
    breadcrumb: [],
    contents: [],
    folders: [],
    files: [],
    selectedItems: new Set(),
    loading: false,
    loadingMessage: "Đang tải...",
    canCancelLoading: false,
    error: null,
    apiDisabled: false,
    errorCount: 0,
    searchTerm: "",
    filters: {
      itemType: "all",
      folderType: "all",
      uploaderEmail: "",
      mimeType: "",
      dateRange: { from: "", to: "" },
    },
    sorting: {
      field: "createdAt",
      direction: SortDirection.DESC,
    },
    pagination: {
      page: 1,
      pageSize: 20,
      totalItems: 0,
      totalPages: 0,
      folderCount: 0,
      fileCount: 0,
    },
    viewMode: "list",
    showNewFolderModal: false,
    showBulkActions: false,
    // Initialize new modal states
    showFileDetailModal: false,
    showShareModal: false,
    showBulkOperationsModal: false,
    showFilePreviewModal: false,
    selectedFile: null,
    selectedFolder: null,
    shareItem: null,
    // Advanced search states
    showAdvancedSearch: false,
    advancedSearchFilters: {
      query: "",
      tags: [],
      createdAfter: "",
      createdBefore: "",
      modifiedAfter: "",
      modifiedBefore: "",
      minSize: "",
      maxSize: "",
      ownerIds: [],
      includeSubfolders: false,
    },
    showExportModal: false,
    showFolderUploadModal: false,
    showRecycleBinModal: false,
    showPermissionsModal: false,
    permissionsItem: null,
    showSyncModal: false,
  });

  // Cancel loading function
  const cancelLoading = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setState((prev) => ({
      ...prev,
      loading: false,
      canCancelLoading: false,
      loadingMessage: "Đang tải...",
    }));
    showToast.info("Đã hủy tải dữ liệu");
  }, []);

  // Store stable references
  const userRef = useRef(user);
  const folderServiceRef = useRef(folderService);
  
  // Update refs when values change
  useEffect(() => {
    userRef.current = user;
  }, [user]);
  
  useEffect(() => {
    folderServiceRef.current = folderService;
  }, [folderService]);

  // Load root folders only (this page only shows root level)
  const loadContent = useCallback(
    async (
      options: {
        page?: number;
        pageSize?: number;
        sortField?: ContentSortBy;
        sortDirection?: SortDirection;
        searchTerm?: string;
        itemType?: "all" | "folders" | "files";
      } = {}
    ) => {
      // Don't call if user not authenticated
      if (!userRef.current?.access_token) {
        return;
      }

      // Prevent double calls by checking loading state
      let shouldProceed = false;
      setState((prev) => {
        if (prev.apiDisabled || prev.loading) {
          return prev; // Don't update state if disabled or loading
        }
        shouldProceed = true;
        return { 
          ...prev, 
          loading: true, 
          canCancelLoading: true,
          loadingMessage: "Đang tải danh sách folders...",
          error: null 
        };
      });

      if (!shouldProceed) {
        return; // Exit if already loading
      }

      // Create new AbortController for this request
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      try {
        // Get current state for pagination info
        let currentPagination = { page: 1, pageSize: 20 };
        let currentSorting = { direction: SortDirection.DESC };
        
        setState((prev) => {
          currentPagination = prev.pagination;
          currentSorting = prev.sorting;
          return prev; // No state change, just reading
        });

        // Always load root folders only
        const response = await folderServiceRef.current.getList({
          page: options.page || currentPagination.page,
          pageSize: options.pageSize || currentPagination.pageSize,
          sortBy: SortField.Name, // Use appropriate sort field
          sortDirection: options.sortDirection || currentSorting.direction,
        });

        // Add null safety for response
        if (!response) {
          throw new Error("Empty response from folder service");
        }

        const folders = response.items || [];
        const pagination = response.pagination || {
          totalItems: 0,
          totalPages: 0,
        };

        // Transform folders to ContentItem format
        const folderItems: ContentItem[] = folders.map((folder) => ({
          id: folder.id,
          name: folder.name,
          type: "folder" as const,
          createdAt: folder.createdAt,
          updatedAt: folder.updatedAt,
          ownerId: folder.ownerId,
          ownerName: folder.ownerName,
          path: folder.path,
          fileCount: folder.fileCount,
          subfolderCount: folder.subfolderCount,
          description: folder.description,
        }));

        setState((prev) => ({
          ...prev,
          currentFolder: null,
          breadcrumb: [],
          folders: folders,
          files: [],
          contents: folderItems,
          pagination: {
            ...prev.pagination,
            totalItems: pagination.totalItems || 0,
            totalPages: pagination.totalPages || 0,
            folderCount: pagination.totalItems || 0,
            fileCount: 0,
          },
          loading: false,
          canCancelLoading: false,
          loadingMessage: "Đang tải...",
          error: null,
          errorCount: 0, // Reset error count on success
        }));

        // Clear abort controller on success
        abortControllerRef.current = null;
      } catch (error: any) {
        // Check if operation was cancelled
        if (error.name === 'AbortError' || abortController.signal.aborted) {
          // Don't update state if cancelled - cancelLoading already handled it
          return;
        }

        console.error("API call failed:", error);
        setState((prev) => {
          const newErrorCount = prev.errorCount + 1;
          // Disable API after first error - no automatic retries
          const shouldDisableApi = true;

          return {
            ...prev,
            loading: false,
            canCancelLoading: false,
            loadingMessage: "Đang tải...",
            currentFolder: null,
            breadcrumb: [],
            folders: [],
            files: [],
            contents: [],
            errorCount: newErrorCount,
            apiDisabled: shouldDisableApi,
            error: 'API call failed. Click "Retry" to try again.',
          };
        });

        // Clear abort controller on error
        abortControllerRef.current = null;
      }
    },
    [] // No dependencies to prevent re-creation
  );

  // Track if initial load has been called to prevent double calls
  const initialLoadCalled = useRef(false);

  // Initial load - called only once when component mounts
  useEffect(() => {
    // Use a timeout to ensure component is fully mounted
    const timer = setTimeout(() => {
      if (userRef.current?.access_token && !initialLoadCalled.current) {
        initialLoadCalled.current = true;
        loadContent();
      }
    }, 0);

    return () => clearTimeout(timer);
  }, []); // No dependencies to prevent double calls

  // Cleanup - abort any ongoing requests when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);

  // Handle folder navigation
  const navigateToFolder = (folderId: string) => {
    if (state.apiDisabled) return;

    // Navigate to folder detail page [folderId]
    router.push(`/manager-folders/${folderId}`);
  };

  // This page only shows root folders, no breadcrumb navigation needed

  // Handle search
  const handleSearch = useCallback(async () => {
    if (!userRef.current?.access_token) return;

    // Check apiDisabled and get search params from current state
    let searchTerm = "";
    let itemType: "all" | "folders" | "files" = "all";
    let shouldProceed = false;

    setState((prev) => {
      if (prev.apiDisabled) return prev;
      
      shouldProceed = true;
      searchTerm = prev.searchTerm;
      itemType = prev.filters.itemType;
      
      return {
        ...prev,
        pagination: { ...prev.pagination, page: 1 },
      };
    });

    if (!shouldProceed) return;

    loadContent({
      page: 1,
      searchTerm,
      itemType,
    });
  }, [loadContent]);

  // Handle sorting
  const handleSort = (field: ContentSortBy) => {
    if (state.apiDisabled) return;

    const newDirection =
      state.sorting.field === field &&
      state.sorting.direction === SortDirection.ASC
        ? SortDirection.DESC
        : SortDirection.ASC;

    setState((prev) => ({
      ...prev,
      sorting: { field, direction: newDirection },
      pagination: { ...prev.pagination, page: 1 },
    }));

    loadContent({
      page: 1,
      sortField: field,
      sortDirection: newDirection,
    });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    if (state.apiDisabled) return;

    setState((prev) => ({
      ...prev,
      pagination: { ...prev.pagination, page },
    }));

    loadContent({ page });
  };

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    setState((prev) => {
      const newSelection = new Set(prev.selectedItems);
      if (newSelection.has(itemId)) {
        newSelection.delete(itemId);
      } else {
        newSelection.add(itemId);
      }
      return {
        ...prev,
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0,
      };
    });
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    setState((prev) => {
      const newSelection = new Set<string>();
      if (checked) {
        [...prev.folders, ...prev.files].forEach((item) =>
          newSelection.add(item.id)
        );
      }
      return {
        ...prev,
        selectedItems: newSelection,
        showBulkActions: newSelection.size > 0,
      };
    });
  };

  // Reset API state and retry
  const handleRetryApi = useCallback(() => {
    setState((prev) => ({
      ...prev,
      apiDisabled: false,
      errorCount: 0,
      error: null,
    }));

    // Reload current content
    loadContent();
  }, [loadContent]);

  // Track ongoing operations to prevent double calls
  const isCreatingFolderRef = useRef(false);
  const isUpdatingFolderRef = useRef(false);

  // Create new folder
  const handleCreateFolder = useCallback(
    async (name: string, description?: string) => {
      if (!userRef.current?.access_token || isCreatingFolderRef.current) return;

      // Get current folder and apiDisabled from state
      let currentFolderId: string | undefined;
      let shouldProceed = false;

      setState((prev) => {
        if (prev.apiDisabled) return prev;
        
        shouldProceed = true;
        currentFolderId = prev.currentFolder?.id;
        return prev;
      });

      if (!shouldProceed) return;

      isCreatingFolderRef.current = true;

      try {
        const createData: FolderCreateData = {
          name,
          description,
          parentFolderId: currentFolderId,
        };

        await folderServiceRef.current.create(createData);

        setState((prev) => ({ ...prev, showNewFolderModal: false }));
        showToast.success(`Folder "${name}" đã được tạo thành công!`);

        // Reload current content
        loadContent();
      } catch (error: any) {
        console.log("Failed to create folder:", error);
        showToast.error(`Không thể tạo folder "${name}". Vui lòng thử lại.`);
        setState((prev) => {
          const newErrorCount = prev.errorCount + 1;
          const shouldDisableApi =
            newErrorCount >= 3 || error?.isCircuitBreakerError?.();

          return {
            ...prev,
            showNewFolderModal: false,
            errorCount: newErrorCount,
            apiDisabled: shouldDisableApi,
            error: shouldDisableApi
              ? 'API temporarily disabled due to repeated errors. Click "Retry" to re-enable.'
              : "Failed to create folder. Try again later.",
          };
        });
      } finally {
        isCreatingFolderRef.current = false;
      }
    },
    [loadContent]
  );

  // Bulk delete
  const handleBulkDelete = useCallback(async () => {
    if (!userRef.current?.access_token) return;

    // Get selected items and check conditions
    let selectedIds: string[] = [];
    let shouldProceed = false;

    setState((prev) => {
      if (prev.apiDisabled || prev.selectedItems.size === 0) return prev;
      
      shouldProceed = true;
      selectedIds = Array.from(prev.selectedItems);
      return prev;
    });

    if (!shouldProceed) return;

    try {
      // Simple approach - just log for now
      console.log("Bulk delete items:", selectedIds);

      setState((prev) => ({
        ...prev,
        selectedItems: new Set(),
        showBulkActions: false,
      }));

      // Reload current content
      loadContent();
    } catch (error: any) {
      console.log("Failed to delete items:", error);
      setState((prev) => {
        const newErrorCount = prev.errorCount + 1;
        const shouldDisableApi =
          newErrorCount >= 3 || error?.isCircuitBreakerError?.();

        return {
          ...prev,
          selectedItems: new Set(),
          showBulkActions: false,
          errorCount: newErrorCount,
          apiDisabled: shouldDisableApi,
          error: shouldDisableApi
            ? 'API temporarily disabled due to repeated errors. Click "Retry" to re-enable.'
            : "Failed to delete items. Try again later.",
        };
      });
    }
  }, [loadContent]);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(dateString));
  };

  // New handler functions for enhanced features
  const handleFileClick = (file: FileDto) => {
    setState((prev) => ({
      ...prev,
      selectedFile: file,
      showFileDetailModal: true,
    }));
  };

  const handleFilePreview = (file: FileDto) => {
    setState((prev) => ({
      ...prev,
      selectedFile: file,
      showFilePreviewModal: true,
    }));
  };

  const handleShare = (item: ContentItem) => {
    setState((prev) => ({
      ...prev,
      shareItem: {
        id: item.id,
        type: item.type,
        name: item.name,
      },
      showShareModal: true,
    }));
  };

  const handlePermissions = (item: ContentItem) => {
    setState((prev) => ({
      ...prev,
      permissionsItem: {
        id: item.id,
        type: item.type,
        name: item.name,
      },
      showPermissionsModal: true,
    }));
  };

  const handleBulkOperations = () => {
    const selectedFiles = state.contents
      .filter(
        (item) => item.type === "file" && state.selectedItems.has(item.id)
      )
      .map((item) => item.id);

    const selectedFolders = state.contents
      .filter(
        (item) => item.type === "folder" && state.selectedItems.has(item.id)
      )
      .map((item) => item.id);

    console.log("Bulk operations for:", { selectedFiles, selectedFolders });

    setState((prev) => ({
      ...prev,
      showBulkOperationsModal: true,
    }));
  };

  const handleFileUpdated = (updatedFile: FileDto) => {
    console.log("File updated:", updatedFile);
    // Reload content to reflect changes
    loadContent();
  };

  const handleFileDeleted = (fileId: string) => {
    console.log("File deleted:", fileId);
    // Reload content to reflect changes
    loadContent();
  };

  const handleOperationComplete = () => {
    // Reload content and clear selections
    setState((prev) => ({
      ...prev,
      selectedItems: new Set(),
      showBulkActions: false,
      showBulkOperationsModal: false,
    }));

    loadContent();
  };

  // Detect duplicate files
  const detectDuplicateFiles = useCallback(() => {
    const fileContents = state.contents.filter(item => item.type === "file");
    const duplicates: { [key: string]: ContentItem[] } = {};
    
    // Group files by name and size
    fileContents.forEach(file => {
      const key = `${file.name}_${file.fileSize}`;
      if (!duplicates[key]) {
        duplicates[key] = [];
      }
      duplicates[key].push(file);
    });
    
    // Filter out groups that have only one file (no duplicates)
    const actualDuplicates = Object.entries(duplicates)
      .filter(([_, files]) => files.length > 1)
      .reduce((acc, [key, files]) => {
        acc[key] = files;
        return acc;
      }, {} as { [key: string]: ContentItem[] });
    
    return actualDuplicates;
  }, [state.contents]);

  // Perform duplicate removal
  const performDuplicateRemoval = useCallback(async (filesToRemove: ContentItem[]) => {
    setState(prev => ({ ...prev, loading: true }));

    try {
      let deletedCount = 0;
      for (const file of filesToRemove) {
        try {
          await fileService.delete(file.id);
          deletedCount++;
        } catch (error) {
          console.error(`Failed to delete file ${file.name}:`, error);
        }
      }

      showToast.success(`Đã xóa ${deletedCount} file trùng lặp thành công`);
      loadContent(); // Reload to refresh the list
    } catch (error) {
      console.error("Failed to remove duplicates:", error);
      showToast.error("Có lỗi xảy ra khi xóa file trùng lặp");
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  }, [fileService, loadContent]);

  // Handle duplicate file removal
  const handleRemoveDuplicates = useCallback(async () => {
    const duplicates = detectDuplicateFiles();
    const duplicateGroups = Object.values(duplicates);
    
    if (duplicateGroups.length === 0) {
      showToast.info("Không tìm thấy file trùng lặp");
      return;
    }

    // Count total duplicates to remove (keep only the newest file in each group)
    let filesToRemove: ContentItem[] = [];
    duplicateGroups.forEach(group => {
      // Sort by creation date (newest first), keep the first one, remove others
      const sorted = group.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      filesToRemove.push(...sorted.slice(1)); // Remove all except the newest
    });

    showAlert({
      type: 'warning',
      title: 'Xóa File Trùng Lặp',
      message: `Tìm thấy ${filesToRemove.length} file trùng lặp. Bạn có muốn xóa chúng không?\n\nLưu ý: File mới nhất trong mỗi nhóm sẽ được giữ lại.`,
      confirmText: 'Xóa',
      cancelText: 'Hủy',
      showCancel: true,
      onConfirm: async () => {
        await performDuplicateRemoval(filesToRemove);
      }
    });
  }, [detectDuplicateFiles, showAlert, performDuplicateRemoval]);

  // Handle folder name update
  const handleFolderNameUpdate = useCallback(
    async (folderId: string, newName: string) => {
      if (!userRef.current?.access_token || isUpdatingFolderRef.current) {
        if (!userRef.current?.access_token) {
          showToast.error("Vui lòng đăng nhập để chỉnh sửa tên folder");
        }
        throw new Error("Cannot update folder name: missing authentication or already updating");
      }

      isUpdatingFolderRef.current = true;

      try {
        const updateData = {
          name: newName,
        };

        await folderServiceRef.current.update(folderId, updateData);
        
        // Show success message
        showToast.success(`Đã cập nhật tên folder thành "${newName}"`);
        
        // Update local state immediately (optimistic update)
        setState((prev) => ({
          ...prev,
          folders: prev.folders.map(folder => 
            folder.id === folderId 
              ? { ...folder, name: newName }
              : folder
          ),
          contents: prev.contents.map(content => 
            content.id === folderId 
              ? { ...content, name: newName }
              : content
          )
        }));
        
        // Also reload to ensure data consistency
        loadContent();
      } catch (error: any) {
        console.error("Failed to update folder name:", error);
        
        // Show specific error messages
        if (error.message?.includes("already exists")) {
          showToast.error("Tên folder này đã tồn tại");
        } else if (error.message?.includes("permission")) {
          showToast.error("Bạn không có quyền chỉnh sửa folder này");
        } else if (error.message?.includes("not found")) {
          showToast.error("Folder không tồn tại hoặc đã bị xóa");
        } else {
          showToast.error("Không thể cập nhật tên folder. Vui lòng thử lại.");
        }
        
        throw new Error(error.message || "Failed to update folder name");
      } finally {
        isUpdatingFolderRef.current = false;
      }
    },
    [loadContent]
  );

  // Advanced search handler
  const handleAdvancedSearch = useCallback(async () => {
    if (!user?.access_token || state.apiDisabled) return;

    setState((prev) => ({ 
      ...prev, 
      loading: true, 
      canCancelLoading: true,
      loadingMessage: "Đang tìm kiếm...",
      error: null 
    }));

    // Create new AbortController for this request
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    try {
      const searchOptions = {
        query: state.advancedSearchFilters.query || undefined,
        tags:
          state.advancedSearchFilters.tags.length > 0
            ? state.advancedSearchFilters.tags
            : undefined,
        createdAfter: state.advancedSearchFilters.createdAfter || undefined,
        createdBefore: state.advancedSearchFilters.createdBefore || undefined,
        modifiedAfter: state.advancedSearchFilters.modifiedAfter || undefined,
        modifiedBefore: state.advancedSearchFilters.modifiedBefore || undefined,
        minSize: state.advancedSearchFilters.minSize
          ? parseInt(state.advancedSearchFilters.minSize)
          : undefined,
        maxSize: state.advancedSearchFilters.maxSize
          ? parseInt(state.advancedSearchFilters.maxSize)
          : undefined,
        ownerIds:
          state.advancedSearchFilters.ownerIds.length > 0
            ? state.advancedSearchFilters.ownerIds
            : undefined,
        includeSubfolders: state.advancedSearchFilters.includeSubfolders,
        page: 1,
        pageSize: state.pagination.pageSize,
      };

      const response = await folderService.search(searchOptions);

      if (!response) {
        throw new Error("Empty response from search");
      }

      const folders = response.items || [];
      const pagination = response.pagination || {
        totalItems: 0,
        totalPages: 0,
      };

      // Transform search results to ContentItem format
      const folderItems: ContentItem[] = folders.map((folder) => ({
        id: folder.id,
        name: folder.name,
        type: "folder" as const,
        createdAt: folder.createdAt,
        updatedAt: folder.updatedAt,
        ownerId: folder.ownerId,
        ownerName: folder.ownerName,
        path: folder.path,
        fileCount: folder.fileCount,
        subfolderCount: folder.subfolderCount,
        description: folder.description,
      }));

      setState((prev) => ({
        ...prev,
        folders: folders,
        files: [],
        contents: folderItems,
        pagination: {
          ...prev.pagination,
          totalItems: pagination.totalItems || 0,
          totalPages: pagination.totalPages || 0,
          folderCount: pagination.totalItems || 0,
          fileCount: 0,
          page: 1,
        },
        loading: false,
        canCancelLoading: false,
        loadingMessage: "Đang tải...",
        error: null,
        errorCount: 0,
        showAdvancedSearch: false,
      }));

      // Clear abort controller on success
      abortControllerRef.current = null;
    } catch (error: any) {
      // Check if operation was cancelled
      if (error.name === 'AbortError' || abortController.signal.aborted) {
        // Don't update state if cancelled - cancelLoading already handled it
        return;
      }

      console.error("Advanced search failed:", error);
      setState((prev) => {
        const newErrorCount = prev.errorCount + 1;
        const shouldDisableApi = true;

        return {
          ...prev,
          loading: false,
          canCancelLoading: false,
          loadingMessage: "Đang tải...",
          folders: [],
          files: [],
          contents: [],
          errorCount: newErrorCount,
          apiDisabled: shouldDisableApi,
          error: 'Search failed. Click "Retry" to try again.',
        };
      });

      // Clear abort controller on error
      abortControllerRef.current = null;
    }
  }, [
    user?.access_token,
    state.advancedSearchFilters,
    state.pagination.pageSize,
    state.apiDisabled,
    folderService,
  ]);

  // Export structure handler
  const handleExportStructure = useCallback(
    async (format: "json" | "csv" | "xml") => {
      if (!user?.access_token || state.apiDisabled) return;

      try {
        const response = await folderService.exportStructure({
          format,
          includeFiles: true,
          includePermissions: true,
          parentFolderId: state.currentFolder?.id,
        });

        // Create a download link
        const link = document.createElement("a");
        link.href = response.downloadUrl;
        link.download = `folder-structure-${
          new Date().toISOString().split("T")[0]
        }.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setState((prev) => ({
          ...prev,
          showExportModal: false,
          error: null,
        }));
      } catch (error: any) {
        console.error("Failed to export folder structure:", error);
        setState((prev) => ({
          ...prev,
          error:
            "Failed to export folder structure: " +
            (error.message || "Unknown error"),
        }));
      }
    },
    [
      user?.access_token,
      state.apiDisabled,
      state.currentFolder?.id,
      folderService,
    ]
  );

  // Navigate back to root
  const navigateToRoot = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete("folderId");
    const queryString = params.toString();
    router.push(`/manager-folders${queryString ? `?${queryString}` : ""}`);

    setState((prev) => ({
      ...prev,
      pagination: { ...prev.pagination, page: 1 },
    }));

    // Load root content
    loadContent();
  };



  return (
    <motion.div
      className="min-h-screen bg-gray-50 dark:bg-gray-900"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {state.currentFolder && (
                <button
                  onClick={navigateToRoot}
                  className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Về thư mục gốc"
                >
                  <ChevronLeftIcon className="w-5 h-5" />
                </button>
              )}
              <div>
                <h1 className="text-heading-2 text-gray-900">
                  {state.currentFolder
                    ? state.currentFolder.name
                    : "Quản lý Folder"}
                </h1>
                <p className="text-body text-gray-600 mt-1">
                  {state.currentFolder
                    ? `Đang xem folder: ${state.currentFolder.path}`
                    : "Quản lý và tổ chức folders của bạn"}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
            
              <button
                onClick={() =>
                  setState((prev) => ({ ...prev, showNewFolderModal: true }))
                }
                disabled={state.apiDisabled}
                className={`btn-primary flex items-center gap-2 ${
                  state.apiDisabled ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                <FolderPlusIcon className="w-4 h-4" />
                Tạo Folder Mới
              </button>

              {/* Upload Folder Button */}
        

              {/* Advanced Search Button */}
              <RoleGuard 
                requiredRoles={["User", "Manager", "Admin"]}
                showError={false}
              >
                <button
                  onClick={() =>
                    setState((prev) => ({ ...prev, showAdvancedSearch: true }))
                  }
                  disabled={state.apiDisabled}
                  className={`btn-secondary flex items-center gap-2 ${
                    state.apiDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  title="Advanced Search"
                >
                  <AdjustmentsHorizontalIcon className="w-4 h-4" />
                  Advanced Search
                </button>
              </RoleGuard>

              {/* Export Button */}
              <RoleGuard requiredRoles={["Manager", "Admin"]} showError={false}>
                <button
                  onClick={() =>
                    setState((prev) => ({ ...prev, showExportModal: true }))
                  }
                  disabled={state.apiDisabled}
                  className={`btn-secondary flex items-center gap-2 ${
                    state.apiDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  title="Export Structure"
                >
                  <DocumentArrowDownIcon className="w-4 h-4" />
                  Export
                </button>
              </RoleGuard>

              {/* Recycle Bin Button */}
              <RoleGuard requiredRoles={["User", "Manager", "Admin"]} showError={false}>
                <button
                  onClick={() =>
                    setState((prev) => ({ ...prev, showRecycleBinModal: true }))
                  }
                  disabled={state.apiDisabled}
                  className={`btn-secondary flex items-center gap-2 ${
                    state.apiDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  title="Thùng Rác"
                >
                  <TrashIcon className="w-4 h-4" />
                  Thùng Rác
                </button>
              </RoleGuard>

              {/* Sync Button */}
              <RoleGuard requiredRoles={["User", "Manager", "Admin"]} showError={false}>
                <button
                  onClick={() =>
                    setState((prev) => ({ ...prev, showSyncModal: true }))
                  }
                  disabled={state.apiDisabled}
                  className={`btn-secondary flex items-center gap-2 ${
                    state.apiDisabled ? "opacity-50 cursor-not-allowed" : ""
                  }`}
                  title="Đồng Bộ Google Drive"
                >
                  <CloudIcon className="w-4 h-4" />
                  Sync
                </button>
              </RoleGuard>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {state.error && (
          <div
            className={`mb-6 p-4 border rounded-lg animate-fade-in ${
              state.apiDisabled
                ? "bg-orange-50 border-orange-200"
                : "bg-red-50 border-red-200"
            }`}
          >
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className={`h-5 w-5 ${
                    state.apiDisabled ? "text-orange-400" : "text-red-400"
                  }`}
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3 flex-1">
                <p
                  className={`text-sm ${
                    state.apiDisabled ? "text-orange-800" : "text-red-800"
                  }`}
                >
                  {state.error}
                </p>
                {state.apiDisabled && (
                  <p className="text-xs text-orange-600 mt-1">
                    Error count: {state.errorCount}/3
                  </p>
                )}
              </div>
              <div className="ml-auto pl-3 flex gap-2">
                {state.apiDisabled && (
                  <button
                    onClick={handleRetryApi}
                    className="px-3 py-1 text-sm bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors"
                  >
                    Retry
                  </button>
                )}
                <button
                  onClick={() => setState((prev) => ({ ...prev, error: null }))}
                  className={
                    state.apiDisabled
                      ? "text-orange-400 hover:text-orange-600"
                      : "text-red-400 hover:text-red-600"
                  }
                >
                  <span className="sr-only">Dismiss</span>
                  <svg
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Root Folders - No breadcrumb needed */}

        {/* Filters and Search */}
        <motion.div
          className="card p-6 mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Tìm kiếm theo Tên Folder/ File"
                  value={state.searchTerm}
                  onChange={(e) =>
                    setState((prev) => ({
                      ...prev,
                      searchTerm: e.target.value,
                    }))
                  }
                  className="input pl-10 w-full"
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-3">
              <select
                value={state.filters.folderType}
                onChange={(e) =>
                  setState((prev) => ({
                    ...prev,
                    filters: {
                      ...prev.filters,
                      folderType: e.target.value as any,
                    },
                  }))
                }
                className="input"
              >
                <option value="all">Tất cả loại</option>
                <option value="public">Public</option>
                <option value="private">Private</option>
                <option value="shared">Shared</option>
              </select>

              <input
                type="text"
                placeholder="Người upload"
                value={state.filters.uploaderEmail}
                onChange={(e) =>
                  setState((prev) => ({
                    ...prev,
                    filters: { ...prev.filters, uploaderEmail: e.target.value },
                  }))
                }
                className="input min-w-[200px]"
              />

              <button
                onClick={handleSearch}
                disabled={state.apiDisabled}
                className={`btn-primary ${
                  state.apiDisabled ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                Tìm kiếm
              </button>
            </div>
          </div>
        </motion.div>

        {/* Quick Upload Zone - Only show when no content or few items */}
 
        {/* Toolbar */}
        <motion.div
          className="flex justify-between items-center mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <div className="flex items-center gap-3">
            {state.showBulkActions && (
              <div className="flex items-center gap-2 animate-slide-in">
                <span className="text-sm text-gray-600">
                  {state.selectedItems.size} đã chọn
                </span>
                <button
                  onClick={handleBulkOperations}
                  className="px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  title="Bulk Operations"
                >
                  Bulk Actions
                </button>
                <button
                  onClick={handleBulkDelete}
                  className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                  title="Xóa các mục đã chọn"
                >
                  <TrashIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => {
                    const firstSelectedItem = state.contents.find((item) =>
                      state.selectedItems.has(item.id)
                    );
                    if (firstSelectedItem) handleShare(firstSelectedItem);
                  }}
                  className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
                  title="Share selected items"
                >
                  <ShareIcon className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            {/* Remove Duplicates Button */}
            <AdminOnly showError={false}>
              <button
                onClick={() => {
                  const duplicates = detectDuplicateFiles();
                  const duplicateCount = Object.values(duplicates).reduce((acc, group) => acc + group.length - 1, 0);
                  if (duplicateCount === 0) {
                    showToast.info("Không tìm thấy file trùng lặp");
                  } else {
                    handleRemoveDuplicates();
                  }
                }}
                disabled={state.loading}
                className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50"
                title="Remove Duplicate Files"
              >
                <TrashIcon className="w-4 h-4" />
              </button>
            </AdminOnly>

            <button
              onClick={() =>
                setState((prev) => ({
                  ...prev,
                  viewMode: prev.viewMode === "list" ? "grid" : "list",
                }))
              }
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title={
                state.viewMode === "list" ? "Chế độ lưới" : "Chế độ danh sách"
              }
            >
              {state.viewMode === "list" ? (
                <ViewColumnsIcon className="w-4 h-4" />
              ) : (
                <Bars3Icon className="w-4 h-4" />
              )}
            </button>
          </div>
        </motion.div>

        {/* Loading Overlay with Cancel */}
        {state.loading && state.canCancelLoading && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm w-full mx-4">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {state.loadingMessage}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Vui lòng chờ trong giây lát...
                </p>
                <button
                  onClick={cancelLoading}
                  className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors"
                >
                  Hủy
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Content */}
        <motion.div
          className="card"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          {state.viewMode === "list" ? (
            <>
              {/* Table Header */}
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                  <div className="col-span-1">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      onChange={(e) => handleSelectAll(e.target.checked)}
                      checked={
                        state.selectedItems.size > 0 &&
                        state.selectedItems.size ===
                          state.folders.length + state.files.length
                      }
                    />
                  </div>
                  <div className="col-span-1">Loại</div>
                  <div className="col-span-3">
                    <button
                      onClick={() => handleSort("name")}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Tên
                      {state.sorting.field === "name" && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC
                            ? "↑"
                            : "↓"}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-2">Người sở hữu</div>
                  <div className="col-span-2">
                    <button
                      onClick={() => handleSort("createdAt")}
                      className="flex items-center gap-1 hover:text-gray-900 transition-colors"
                    >
                      Sửa đổi lần cuối
                      {state.sorting.field === "createdAt" && (
                        <span className="text-xs">
                          {state.sorting.direction === SortDirection.ASC
                            ? "↑"
                            : "↓"}
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="col-span-1">Kích thước</div>
                  <div className="col-span-2">Thao tác</div>
                </div>
              </div>

              {/* Items */}
              <div className="divide-y divide-gray-200">
                {state.loading ? (
                  <FolderListSkeleton count={8} />
                ) : (
                  <>
                    {/* Folders */}
                    {state.folders.map((folder, index) => (
                      <div
                        key={folder.id}
                        className="px-6 py-4 hover:bg-gray-50 animate-fade-in transition-colors"
                        style={{ animationDelay: `${index * 0.05}s` }}
                      >
                        <div className="grid grid-cols-12 gap-4 items-center">
                          <div className="col-span-1">
                            <input
                              type="checkbox"
                              checked={state.selectedItems.has(folder.id)}
                              onChange={() => toggleItemSelection(folder.id)}
                              className="rounded border-gray-300"
                            />
                          </div>
                          <div className="col-span-1">
                            <FolderIcon className="w-6 h-6 text-blue-500" />
                          </div>
                          <div className="col-span-3">
                            <div className="flex items-center gap-2">
                              <div className="flex-1 min-w-0">
                                <button
                                  onClick={() => navigateToFolder(folder.id)}
                                  className="font-medium text-blue-600 hover:text-blue-800 transition-colors"
                                >
                                  {folder.name}
                                </button>
                              </div>
                              <button
                                onClick={() => navigateToFolder(folder.id)}
                                className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                                title="View folder"
                              >
                                <FolderIcon className="w-4 h-4" />
                              </button>
                            </div>
                            {folder.description && (
                              <p className="text-sm text-gray-500 mt-1">
                                {folder.description}
                              </p>
                            )}
                            <div className="text-xs text-gray-400 mt-1">
                              {folder.fileCount} files, {folder.subfolderCount}{" "}
                              folders
                            </div>
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {folder.ownerName || "Unknown"}
                              </span>
                              <span className="text-xs text-gray-400">
                                {folder.ownerId}
                              </span>
                            </div>
                          </div>
                          <div className="col-span-2 text-sm text-gray-600">
                            {formatDate(folder.updatedAt)}
                          </div>
                          <div className="col-span-1 text-sm text-gray-600">
                            --
                          </div>
                          <div className="col-span-2">
                            <div className="flex items-center gap-1">
                              <button
                                onClick={async () => {
                                  try {
                                    // For folders, we could implement a ZIP download or navigate to folder
                                    navigateToFolder(folder.id);
                                  } catch (error) {
                                    console.error("Navigate failed:", error);
                                  }
                                }}
                                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                title="View folder"
                              >
                                <ArrowDownTrayIcon className="w-4 h-4" />
                              </button>
                              <RoleGuard requiredRoles={["Manager", "Admin"]} showError={false}>
                                <button
                                  onClick={() => {
                                    const newName = prompt("Enter new folder name:", folder.name);
                                    if (newName && newName !== folder.name) {
                                      handleFolderNameUpdate(folder.id, newName);
                                    }
                                  }}
                                  className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                                  title="Edit folder name"
                                >
                                  <PencilIcon className="w-4 h-4" />
                                </button>
                              </RoleGuard>
                              <button
                                onClick={() =>
                                  handleShare({
                                    id: folder.id,
                                    name: folder.name,
                                    type: "folder",
                                    createdAt: folder.createdAt,
                                    updatedAt: folder.updatedAt,
                                    ownerId: folder.ownerId,
                                    ownerName: folder.ownerName,
                                    path: folder.path,
                                    fileCount: folder.fileCount,
                                    subfolderCount: folder.subfolderCount,
                                    description: folder.description,
                                  } as ContentItem)
                                }
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                                title="Share"
                              >
                                <ShareIcon className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => {
                                  showAlert({
                                    type: 'warning',
                                    title: 'Xác nhận xóa folder',
                                    message: `Bạn có chắc chắn muốn xóa folder "${folder.name}"?\n\nLưu ý: Tất cả files và subfolders bên trong sẽ bị xóa vĩnh viễn.`,
                                    confirmText: 'Xóa',
                                    cancelText: 'Hủy',
                                    showCancel: true,
                                    onConfirm: async () => {
                                      try {
                                        await folderService.delete(folder.id);
                                        showToast.success(`Folder "${folder.name}" đã được xóa thành công`);
                                        loadContent(); // Refresh the list
                                      } catch (error) {
                                        console.error("Delete failed:", error);
                                        showToast.error("Không thể xóa folder. Vui lòng thử lại.");
                                      }
                                    }
                                  });
                                }}
                                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                title="Delete"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                    {/* Empty State */}
                    {!state.loading &&
                      state.folders.length === 0 &&
                      state.files.length === 0 && (
                        <EmptyState
                          icon={FolderIcon}
                          title={
                            !state.currentFolder
                              ? "Chưa có folders nào"
                              : "Không có folders hoặc files"
                          }
                          description={
                            !state.currentFolder
                              ? "Tạo folder đầu tiên để bắt đầu tổ chức files của bạn. Files được lưu trữ bên trong các folders."
                              : "Tạo folder mới hoặc upload files để bắt đầu tổ chức nội dung."
                          }
                          action={
                            <button
                              onClick={() =>
                                setState((prev) => ({
                                  ...prev,
                                  showNewFolderModal: true,
                                }))
                              }
                              disabled={state.apiDisabled}
                              className={`btn-primary ${
                                state.apiDisabled
                                  ? "opacity-50 cursor-not-allowed"
                                  : ""
                              }`}
                            >
                              Tạo Folder Mới
                            </button>
                          }
                        />
                      )}
                  </>
                )}
              </div>
            </>
          ) : (
            /* Grid View */
            <div className="p-6">
              {state.loading ? (
                <FolderGridSkeleton count={12} />
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {/* Folders in Grid */}
                  {state.folders.map((folder, index) => (
                    <div
                      key={folder.id}
                      className="card p-4 hover:shadow-md transition-all animate-fade-in relative group"
                      style={{ animationDelay: `${index * 0.05}s` }}
                    >
                      <div 
                        className="text-center cursor-pointer"
                        onClick={() => navigateToFolder(folder.id)}
                      >
                        <FolderIcon className="w-12 h-12 text-blue-500 mx-auto mb-2" />
                        <div className="px-2">
                          <p
                            className="text-sm font-medium text-gray-900 truncate"
                            title={folder.name}
                          >
                            {folder.name}
                          </p>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {folder.fileCount} files, {folder.subfolderCount}{" "}
                          folders
                        </p>
                        <p
                          className="text-xs text-gray-400 mt-1"
                          title={folder.ownerName || "Unknown"}
                        >
                          {folder.ownerName || "Unknown"}
                        </p>
                      </div>
                      
                      {/* Action buttons - show on hover */}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white rounded-lg shadow-lg border p-1 flex gap-1">
                        <RoleGuard requiredRoles={["Manager", "Admin"]} showError={false}>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              const newName = prompt("Enter new folder name:", folder.name);
                              if (newName && newName !== folder.name) {
                                handleFolderNameUpdate(folder.id, newName);
                              }
                            }}
                            className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                            title="Edit folder name"
                          >
                            <PencilIcon className="w-3 h-3" />
                          </button>
                        </RoleGuard>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShare({
                              id: folder.id,
                              name: folder.name,
                              type: "folder",
                              createdAt: folder.createdAt,
                              updatedAt: folder.updatedAt,
                              ownerId: folder.ownerId,
                              ownerName: folder.ownerName,
                              path: folder.path,
                              fileCount: folder.fileCount,
                              subfolderCount: folder.subfolderCount,
                              description: folder.description,
                            } as ContentItem);
                          }}
                          className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                          title="Share"
                        >
                          <ShareIcon className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            showAlert({
                              type: 'warning',
                              title: 'Xác nhận xóa folder',
                              message: `Bạn có chắc chắn muốn xóa folder "${folder.name}"?\n\nLưu ý: Tất cả files và subfolders bên trong sẽ bị xóa vĩnh viễn.`,
                              confirmText: 'Xóa',
                              cancelText: 'Hủy',
                              showCancel: true,
                              onConfirm: async () => {
                                try {
                                  await folderService.delete(folder.id);
                                  showToast.success(`Folder "${folder.name}" đã được xóa thành công`);
                                  loadContent(); // Refresh the list
                                } catch (error) {
                                  console.error("Delete failed:", error);
                                  showToast.error("Không thể xóa folder. Vui lòng thử lại.");
                                }
                              }
                            });
                          }}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete"
                        >
                          <TrashIcon className="w-3 h-3" />
                        </button>
                      </div>
                      
                      <div className="mt-2 flex justify-center">
                        <input
                          type="checkbox"
                          checked={state.selectedItems.has(folder.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            toggleItemSelection(folder.id);
                          }}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  ))}

                  {/* Files in Grid */}
                  {state.files.map((file, index) => (
                    <div
                      key={file.id}
                      className="card p-4 hover:shadow-md transition-all animate-fade-in relative group"
                      style={{
                        animationDelay: `${
                          (state.folders.length + index) * 0.05
                        }s`,
                      }}
                    >
                      <div 
                        className="text-center cursor-pointer"
                        onClick={() => handleFileClick(file)}
                      >
                        <DocumentIcon className="w-12 h-12 text-gray-500 mx-auto mb-2" />
                        <p
                          className="text-sm font-medium text-gray-900 truncate"
                          title={file.name}
                        >
                          {file.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {formatFileSize(file.fileSize)}
                        </p>
                      </div>
                      
                      {/* Action buttons - show on hover */}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-white rounded-lg shadow-lg border p-1 flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleFilePreview(file);
                          }}
                          className="p-1 text-gray-400 hover:text-purple-600 transition-colors"
                          title="Preview"
                        >
                          <EyeIcon className="w-3 h-3" />
                        </button>
                        <button
                          onClick={async (e) => {
                            e.stopPropagation();
                            try {
                              await fileService.downloadWithFilename({
                                id: file.id,
                                name: file.name,
                                displayName: file.displayName,
                                mimeType: file.mimeType
                              });
                            } catch (error) {
                              console.error("Download failed:", error);
                              showToast.error("Không thể tải file. Vui lòng thử lại.");
                            }
                          }}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                          title="Download"
                        >
                          <ArrowDownTrayIcon className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleShare({
                              id: file.id,
                              name: file.name,
                              type: "file",
                              createdAt: file.createdAt,
                              updatedAt: file.updatedAt,
                              ownerId: file.ownerId,
                              displayName: file.displayName,
                              fileSize: file.fileSize,
                              mimeType: file.mimeType,
                            } as ContentItem);
                          }}
                          className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                          title="Share"
                        >
                          <ShareIcon className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            showAlert({
                              type: 'warning',
                              title: 'Xác nhận xóa',
                              message: `Bạn có chắc chắn muốn xóa file "${file.name}"?`,
                              confirmText: 'Xóa',
                              cancelText: 'Hủy',
                              showCancel: true,
                              onConfirm: async () => {
                                try {
                                  await fileService.delete(file.id);
                                  handleFileDeleted(file.id);
                                  showToast.success(`File "${file.name}" đã được xóa thành công`);
                                } catch (error) {
                                  console.error("Delete failed:", error);
                                  showToast.error("Không thể xóa file. Vui lòng thử lại.");
                                }
                              }
                            });
                          }}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete"
                        >
                          <TrashIcon className="w-3 h-3" />
                        </button>
                      </div>
                      
                      <div className="mt-2 flex justify-center">
                        <input
                          type="checkbox"
                          checked={state.selectedItems.has(file.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            toggleItemSelection(file.id);
                          }}
                          className="rounded border-gray-300"
                        />
                      </div>
                    </div>
                  ))}

                  {/* Empty State for Grid */}
                  {!state.loading &&
                    state.folders.length === 0 &&
                    state.files.length === 0 && (
                      <div className="col-span-full text-center py-12 animate-fade-in">
                        <FolderIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          {!state.currentFolder
                            ? "Chưa có folders nào"
                            : "Không có folders hoặc files"}
                        </h3>
                        <p className="text-gray-500 mb-4">
                          {!state.currentFolder
                            ? "Tạo folder đầu tiên để bắt đầu tổ chức files của bạn. Files được lưu trữ bên trong các folders."
                            : "Tạo folder mới hoặc upload files để bắt đầu tổ chức nội dung."}
                        </p>
                        <button
                          onClick={() =>
                            setState((prev) => ({
                              ...prev,
                              showNewFolderModal: true,
                            }))
                          }
                          disabled={state.apiDisabled}
                          className={`btn-primary ${
                            state.apiDisabled
                              ? "opacity-50 cursor-not-allowed"
                              : ""
                          }`}
                        >
                          Tạo Folder Mới
                        </button>
                      </div>
                    )}
                </div>
              )}
            </div>
          )}

          {/* Pagination */}
          {state.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 animate-fade-in">
              <div className="text-sm text-gray-700">
                Hiển thị{" "}
                {(state.pagination.page - 1) * state.pagination.pageSize + 1}{" "}
                đến{" "}
                {Math.min(
                  state.pagination.page * state.pagination.pageSize,
                  state.pagination.totalItems
                )}{" "}
                của {state.pagination.totalItems} kết quả
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => handlePageChange(state.pagination.page - 1)}
                  disabled={state.pagination.page === 1}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeftIcon className="w-4 h-4" />
                </button>

                {/* Page numbers */}
                {Array.from(
                  { length: Math.min(5, state.pagination.totalPages) },
                  (_, i) => {
                    const pageNumber =
                      Math.max(1, state.pagination.page - 2) + i;
                    if (pageNumber <= state.pagination.totalPages) {
                      return (
                        <button
                          key={pageNumber}
                          onClick={() => handlePageChange(pageNumber)}
                          className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                            pageNumber === state.pagination.page
                              ? "bg-blue-600 text-white"
                              : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"
                          }`}
                        >
                          {pageNumber}
                        </button>
                      );
                    }
                    return null;
                  }
                )}

                <button
                  onClick={() => handlePageChange(state.pagination.page + 1)}
                  disabled={
                    state.pagination.page === state.pagination.totalPages
                  }
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRightIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          )}

          {/* Create Folder Modal */}
          <CreateFolderModal
            isOpen={state.showNewFolderModal}
            onClose={() =>
              setState((prev) => ({ ...prev, showNewFolderModal: false }))
            }
            onSubmit={handleCreateFolder}
            currentFolder={state.currentFolder}
            loading={state.loading}
          />

          {/* Share Modal */}
          <ShareModal
            isOpen={state.showShareModal}
            onClose={() =>
              setState((prev) => ({
                ...prev,
                showShareModal: false,
                shareItem: null,
              }))
            }
            itemId={state.shareItem?.id || ""}
            itemType={state.shareItem?.type || "file"}
            itemName={state.shareItem?.name || ""}
            fileService={
              state.shareItem?.type === "file" ? fileService : undefined
            }
            folderService={
              state.shareItem?.type === "folder" ? folderService : undefined
            }
          />

          {/* Bulk Operations Modal */}
          <BulkOperationsModal
            isOpen={state.showBulkOperationsModal}
            onClose={() =>
              setState((prev) => ({ ...prev, showBulkOperationsModal: false }))
            }
            selectedItems={{
              files: state.contents
                .filter(
                  (item) =>
                    item.type === "file" && state.selectedItems.has(item.id)
                )
                .map((item) => item.id),
              folders: state.contents
                .filter(
                  (item) =>
                    item.type === "folder" && state.selectedItems.has(item.id)
                )
                .map((item) => item.id),
            }}
            fileService={fileService}
            folderService={folderService}
            onOperationComplete={handleOperationComplete}
          />

          {/* File Preview Modal */}
          <FilePreviewModal
            isOpen={state.showFilePreviewModal}
            onClose={() =>
              setState((prev) => ({
                ...prev,
                showFilePreviewModal: false,
                selectedFile: null,
              }))
            }
            file={state.selectedFile}
            fileService={fileService}
          />

          {/* Advanced Search Modal */}
          {state.showAdvancedSearch && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
              <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">
                      Advanced Search
                    </h2>
                    <button
                      onClick={() =>
                        setState((prev) => ({
                          ...prev,
                          showAdvancedSearch: false,
                        }))
                      }
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="w-6 h-6" />
                    </button>
                  </div>

                  <div className="space-y-4">
                    {/* Basic Query */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Search Query
                      </label>
                      <input
                        type="text"
                        value={state.advancedSearchFilters.query}
                        onChange={(e) =>
                          setState((prev) => ({
                            ...prev,
                            advancedSearchFilters: {
                              ...prev.advancedSearchFilters,
                              query: e.target.value,
                            },
                          }))
                        }
                        className="input w-full"
                        placeholder="Enter search terms..."
                      />
                    </div>

                    {/* Date Range */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Created After
                        </label>
                        <input
                          type="date"
                          value={state.advancedSearchFilters.createdAfter}
                          onChange={(e) =>
                            setState((prev) => ({
                              ...prev,
                              advancedSearchFilters: {
                                ...prev.advancedSearchFilters,
                                createdAfter: e.target.value,
                              },
                            }))
                          }
                          className="input w-full"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Created Before
                        </label>
                        <input
                          type="date"
                          value={state.advancedSearchFilters.createdBefore}
                          onChange={(e) =>
                            setState((prev) => ({
                              ...prev,
                              advancedSearchFilters: {
                                ...prev.advancedSearchFilters,
                                createdBefore: e.target.value,
                              },
                            }))
                          }
                          className="input w-full"
                        />
                      </div>
                    </div>

                    {/* Modified Date Range */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Modified After
                        </label>
                        <input
                          type="date"
                          value={state.advancedSearchFilters.modifiedAfter}
                          onChange={(e) =>
                            setState((prev) => ({
                              ...prev,
                              advancedSearchFilters: {
                                ...prev.advancedSearchFilters,
                                modifiedAfter: e.target.value,
                              },
                            }))
                          }
                          className="input w-full"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Modified Before
                        </label>
                        <input
                          type="date"
                          value={state.advancedSearchFilters.modifiedBefore}
                          onChange={(e) =>
                            setState((prev) => ({
                              ...prev,
                              advancedSearchFilters: {
                                ...prev.advancedSearchFilters,
                                modifiedBefore: e.target.value,
                              },
                            }))
                          }
                          className="input w-full"
                        />
                      </div>
                    </div>

                    {/* Size Range */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Min Size (bytes)
                        </label>
                        <input
                          type="number"
                          value={state.advancedSearchFilters.minSize}
                          onChange={(e) =>
                            setState((prev) => ({
                              ...prev,
                              advancedSearchFilters: {
                                ...prev.advancedSearchFilters,
                                minSize: e.target.value,
                              },
                            }))
                          }
                          className="input w-full"
                          placeholder="0"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Max Size (bytes)
                        </label>
                        <input
                          type="number"
                          value={state.advancedSearchFilters.maxSize}
                          onChange={(e) =>
                            setState((prev) => ({
                              ...prev,
                              advancedSearchFilters: {
                                ...prev.advancedSearchFilters,
                                maxSize: e.target.value,
                              },
                            }))
                          }
                          className="input w-full"
                          placeholder="No limit"
                        />
                      </div>
                    </div>

                    {/* Tags */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tags (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={state.advancedSearchFilters.tags.join(", ")}
                        onChange={(e) =>
                          setState((prev) => ({
                            ...prev,
                            advancedSearchFilters: {
                              ...prev.advancedSearchFilters,
                              tags: e.target.value
                                .split(",")
                                .map((t) => t.trim())
                                .filter((t) => t),
                            },
                          }))
                        }
                        className="input w-full"
                        placeholder="tag1, tag2, tag3"
                      />
                    </div>

                    {/* Owner IDs */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Owner IDs (comma-separated)
                      </label>
                      <input
                        type="text"
                        value={state.advancedSearchFilters.ownerIds.join(", ")}
                        onChange={(e) =>
                          setState((prev) => ({
                            ...prev,
                            advancedSearchFilters: {
                              ...prev.advancedSearchFilters,
                              ownerIds: e.target.value
                                .split(",")
                                .map((id) => id.trim())
                                .filter((id) => id),
                            },
                          }))
                        }
                        className="input w-full"
                        placeholder="user-id-1, user-id-2"
                      />
                    </div>

                    {/* Include Subfolders */}
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="includeSubfolders"
                        checked={state.advancedSearchFilters.includeSubfolders}
                        onChange={(e) =>
                          setState((prev) => ({
                            ...prev,
                            advancedSearchFilters: {
                              ...prev.advancedSearchFilters,
                              includeSubfolders: e.target.checked,
                            },
                          }))
                        }
                        className="rounded border-gray-300 mr-2"
                      />
                      <label
                        htmlFor="includeSubfolders"
                        className="text-sm text-gray-700"
                      >
                        Include content from subfolders
                      </label>
                    </div>
                  </div>

                  <div className="flex gap-3 mt-6 pt-6 border-t">
                    <button
                      onClick={handleAdvancedSearch}
                      disabled={state.loading || state.apiDisabled}
                      className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {state.loading ? "Searching..." : "Search"}
                    </button>
                    <button
                      onClick={() =>
                        setState((prev) => ({
                          ...prev,
                          advancedSearchFilters: {
                            query: "",
                            tags: [],
                            createdAfter: "",
                            createdBefore: "",
                            modifiedAfter: "",
                            modifiedBefore: "",
                            minSize: "",
                            maxSize: "",
                            ownerIds: [],
                            includeSubfolders: false,
                          },
                        }))
                      }
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      Clear
                    </button>
                    <button
                      onClick={() =>
                        setState((prev) => ({
                          ...prev,
                          showAdvancedSearch: false,
                        }))
                      }
                      className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Export Modal */}
          {state.showExportModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
              <div className="bg-white rounded-lg max-w-lg w-full">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-gray-900">
                      Export Folder Structure
                    </h2>
                    <button
                      onClick={() =>
                        setState((prev) => ({
                          ...prev,
                          showExportModal: false,
                        }))
                      }
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="w-6 h-6" />
                    </button>
                  </div>

                  <div className="space-y-4">
                    <p className="text-gray-600 text-sm">
                      Choose the format to export the folder structure and
                      metadata:
                    </p>

                    <div className="grid grid-cols-3 gap-3">
                      <button
                        onClick={() => handleExportStructure("json")}
                        className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-center"
                      >
                        <DocumentIcon className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                        <div className="font-medium text-sm">JSON</div>
                        <div className="text-xs text-gray-500">
                          Structured data
                        </div>
                      </button>

                      <button
                        onClick={() => handleExportStructure("csv")}
                        className="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors text-center"
                      >
                        <DocumentIcon className="w-8 h-8 text-green-500 mx-auto mb-2" />
                        <div className="font-medium text-sm">CSV</div>
                        <div className="text-xs text-gray-500">Spreadsheet</div>
                      </button>

                      <button
                        onClick={() => handleExportStructure("xml")}
                        className="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:bg-orange-50 transition-colors text-center"
                      >
                        <DocumentIcon className="w-8 h-8 text-orange-500 mx-auto mb-2" />
                        <div className="font-medium text-sm">XML</div>
                        <div className="text-xs text-gray-500">Markup</div>
                      </button>
                    </div>

                    <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                      <strong>Note:</strong> The export will include folder
                      structure, file metadata, and permissions. File contents
                      are not included in the export.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Alert Modal */}
          <AlertModalComponent />

          {/* Recycle Bin Modal */}
          <RecycleBinModal
            isOpen={state.showRecycleBinModal}
            onClose={() => setState((prev) => ({ ...prev, showRecycleBinModal: false }))}
            onItemRestored={() => {
              // Reload content to reflect restored items
              loadContent();
            }}
            onItemDeleted={() => {
              // No need to reload as item is permanently deleted
            }}
          />

          {/* Permissions Modal */}
          {state.permissionsItem && (
            <PermissionsModal
              isOpen={state.showPermissionsModal}
              onClose={() => setState((prev) => ({ ...prev, showPermissionsModal: false, permissionsItem: null }))}
              itemId={state.permissionsItem.id}
              itemType={state.permissionsItem.type}
              itemName={state.permissionsItem.name}
            />
          )}

          {/* Sync Modal */}
          <SyncModal
            isOpen={state.showSyncModal}
            onClose={() => setState((prev) => ({ ...prev, showSyncModal: false }))}
          />
        </motion.div>
      </div>
    </motion.div>
  );
}
