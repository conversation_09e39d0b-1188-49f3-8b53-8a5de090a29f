"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import {
  DocumentIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

export default function UnauthorizedPage() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  const handleGoHome = () => {
    router.push("/");
  };

  const handleLogin = () => {
    router.push("/auth/login");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center">
            <ExclamationTriangleIcon className="h-12 w-12 text-yellow-500" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Access Denied
          </h2>
        </div>

        <div className="mt-8 space-y-6">
          <div className="text-center p-6 bg-white shadow-md rounded-lg">
            <p className="text-gray-700 mb-4">
              You don&apos;t have permission to access the requested resource.
            </p>
            <p className="text-gray-500 mb-6 text-sm">
              Please contact your administrator if you believe this is an error.
            </p>

            <div className="flex flex-col space-y-3">
              <button
                onClick={handleGoHome}
                className="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Go to Home
              </button>

              {!isAuthenticated && (
                <button
                  onClick={handleLogin}
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Sign In
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
