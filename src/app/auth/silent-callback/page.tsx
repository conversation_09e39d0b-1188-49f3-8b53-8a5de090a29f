"use client";

import { useEffect } from "react";
import { UserManager, UserManagerSettings } from "oidc-client-ts";
import { oidcConfig } from "@/lib/oidcConfig";
import { handleAuthError } from "@/lib/authUtils";

export default function SilentCallbackPage() {
  useEffect(() => {
    const handleSilentCallback = async () => {
      try {
        console.log("🔄 Processing silent token renewal...");

        const userManager = new UserManager(oidcConfig as UserManagerSettings);
        const user = await userManager.signinSilentCallback();
        console.log("🔄 Silent token renewal successful:", user);
  
      } catch (error) {
        console.error("❌ Silent token renewal failed:", error);

        // Use centralized error handler
        handleAuthError(error as Error);

        // Additional handling for silent renewal specific errors
        if (error instanceof Error) {
          if (error.message?.includes("login_required")) {
            console.log("🔄 Login required - user needs to re-authenticate");
            // The parent window will handle the login redirect
          } else if (error.message?.includes("consent_required")) {
            console.log("🔄 Consent required - user needs to re-consent");
          } else if (error.message?.includes("interaction_required")) {
            console.log(
              "🔄 Interaction required - silent renewal not possible"
            );
          }
        }
      }
    };

    handleSilentCallback();
  }, []);

  // This page should be invisible and used only in an iframe
  // Return minimal content for debugging purposes in development
  if (process.env.NODE_ENV === "development") {
    return (
      <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: "rgba(0,0,0,0.1)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          fontSize: "12px",
          color: "#666",
          zIndex: 9999,
        }}
      >
        Silent Token Renewal (Dev Mode)
      </div>
    );
  }

  // Production: completely invisible
  return null;
}
