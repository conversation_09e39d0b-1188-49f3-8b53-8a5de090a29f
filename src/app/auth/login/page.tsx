"use client";

import React, { useEffect, Suspense, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { DocumentIcon } from "@heroicons/react/24/outline";

// Interface for the debug info
interface DebugInfo {
  userAgent: string;
  isHttps: boolean;
  storageAvailable: boolean;
  timestamp: string;
  returnUrl: string | null;
}

function LoginContent() {
  const searchParams = useSearchParams();
  const returnUrl = searchParams?.get("returnUrl");
  const { login, isLoading, error } = useAuth();

  // Use ref instead of state to track login attempts
  const loginAttemptedRef = useRef(false);
  const debugInfoRef = useRef<DebugInfo | null>(null);

  // Single login effect with stable dependencies
  useEffect(() => {
    // Skip if already attempted login or currently loading
    if (loginAttemptedRef.current || isLoading) {
      return;
    }

    console.log("🔐 LoginPage: Component mounted, preparing login");
    console.log("🔐 LoginPage: Return URL:", returnUrl);

    // Lưu returnUrl vào sessionStorage
    if (returnUrl) {
      try {
        sessionStorage.setItem("auth_return_url", returnUrl);
        console.log(
          "🔐 LoginPage: Saved returnUrl to sessionStorage:",
          returnUrl
        );
      } catch (e) {
        console.error(
          "🔐 LoginPage: Could not save returnUrl to sessionStorage:",
          e
        );
      }
    }

    // Collect debug info only once
    try {
      const userAgent = window.navigator.userAgent;
      const isHttps = window.location.protocol === "https:";
      const storageAvailable = (() => {
        try {
          localStorage.setItem("test", "test");
          localStorage.removeItem("test");
          return true;
        } catch {
          return false;
        }
      })();

      debugInfoRef.current = {
        userAgent,
        isHttps,
        storageAvailable,
        timestamp: new Date().toISOString(),
        returnUrl,
      };
    } catch (e) {
      console.error("Could not collect debug info:", e);
    }

    // Set a short timer to trigger login after render is stable
    const loginTimer = setTimeout(() => {
      if (!loginAttemptedRef.current) {
        loginAttemptedRef.current = true;

        // Store login attempt in sessionStorage as extra safeguard
        try {
          sessionStorage.setItem("login_attempted", "true");
        } catch (e) {
          console.error("Could not store login status:", e);
        }

        console.log(
          "🔐 LoginPage: Initiating login with returnUrl:",
          returnUrl || "/"
        );

        // Thực hiện login với state chứa returnUrl
        login();
      }
    }, 100);

    return () => clearTimeout(loginTimer);
  }, [returnUrl, login, isLoading]); // Add isLoading to dependency array

  const handleLoginClick = async () => {
    // Allow retry via button click
    console.log("🔐 Manual login button clicked");
    try {
      // Lưu lại returnUrl trước khi login lại
      if (returnUrl) {
        sessionStorage.setItem("auth_return_url", returnUrl);
      }
      await login();
    } catch (err) {
      console.error("🔐 Login click error:", err);
    }
  };

  const debugInfo = debugInfoRef.current;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center">
            <DocumentIcon className="h-12 w-12 text-blue-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            PDF OCR Dashboard
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Sign in to access your PDF files and OCR operations
          </p>
        </div>

        <div className="mt-8 space-y-6">
          <div className="text-center">
            {error && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                <p className="font-bold">Authentication Error:</p>
                <p>{error}</p>
                <p className="text-sm mt-1">
                  Try clearing your browser cache and cookies.
                </p>
              </div>
            )}

            {loginAttemptedRef.current && !isLoading && !error && (
              <div className="mb-4 p-3 bg-yellow-100 border border-yellow-400 text-yellow-800 rounded">
                <p className="font-bold">Redirect Issue:</p>
                <p>Could not redirect to Identity Server automatically.</p>
                <p className="text-sm mt-1">
                  Please try clicking the sign in button below.
                </p>
              </div>
            )}

            {isLoading && (
              <div className="mb-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            )}

            <p className="text-gray-600 mb-4">
              {isLoading
                ? "Redirecting to login..."
                : "Please sign in to continue"}
            </p>

            <button
              onClick={handleLoginClick}
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "Signing in..." : "Sign in with Identity Server"}
            </button>

            {debugInfo && (
              <div className="mt-4 text-xs text-left text-gray-500 bg-gray-50 p-2 rounded">
                <p className="font-bold">Debug Info:</p>
                <pre className="whitespace-pre-wrap overflow-auto max-h-24">
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      }
    >
      <LoginContent />
    </Suspense>
  );
}
