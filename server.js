import { createServer } from "http";
import { parse } from "url";
import next from "next";
import os from "os";

// Environment configuration
const ENV = {
  NODE_ENV: process.env.NODE_ENV || "development",
  PORT: 3000,
  HOSTNAME: process.env.HOSTNAME || "localhost",
  LOG_LEVEL: process.env.LOG_LEVEL || "info",
};

const dev = ENV.NODE_ENV !== "production";
const hostname = ENV.HOSTNAME;
const port = ENV.PORT;

// Simple logging utility
const Logger = {
  info: (msg, meta) => console.log(`ℹ️  ${msg}`, meta || ""),
  error: (msg, meta) => console.error(`❌ ${msg}`, meta || ""),
  success: (msg, meta) => console.log(`✅ ${msg}`, meta || ""),
};

const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      Logger.error("Error occurred handling request", {
        url: req.url,
        error: err.message,
        stack: err.stack,
      });
      res.statusCode = 500;
      res.end("internal server error");
    }
  })
    .once("error", (err) => {
      Logger.error("Server error", { error: err.message, stack: err.stack });
      process.exit(1);
    })
    .listen(port, () => {
      const networkInterface =
        os.networkInterfaces()?.["Wi-Fi"]?.[1]?.address || "localhost";
      Logger.success(`Server ready on http://${hostname}:${port}`);
      Logger.info(`Network access: http://${networkInterface}:${port}`);
    });
});
