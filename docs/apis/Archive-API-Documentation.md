# Archive API Documentation

## Overview

The VeasyFileManager now supports archiving functionality for both files and folders. Archived items are hidden from normal views but remain accessible through specific queries and can be restored at any time.

## Key Features

- **Archive/Unarchive Files**: Individual file archiving with permission control
- **Archive/Unarchive Folders**: Folder archiving with cascade to all contents
- **Filtering Support**: Query archived, non-archived, or all items
- **Audit Trail**: Track who archived items and when
- **Permission Control**: Only owners can archive/unarchive their items

---

## File Archive APIs

### Archive File

Archive a specific file. Only the file owner can perform this action.

**Endpoint:** `POST /api/v1/files/{id}/archive`

**Parameters:**
- `id` (path, required): GUID of the file to archive

**Authentication:** Required (Bear<PERSON>)

**Request Example:**
```bash
curl -X POST "https://api.example.com/api/v1/files/123e4567-e89b-12d3-a456-************/archive" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Response Example:**
```json
{
  "data": true,
  "message": "File archived successfully",
  "statusCode": 200,
  "success": true,
  "errorCode": null
}
```

**Error Responses:**
- `400 Bad Request`: Invalid request or file already archived
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: User doesn't own the file
- `404 Not Found`: File doesn't exist
- `409 Conflict`: Cannot archive (e.g., file is deleted)

---

### Unarchive File

Restore an archived file back to normal view.

**Endpoint:** `POST /api/v1/files/{id}/unarchive`

**Parameters:**
- `id` (path, required): GUID of the file to unarchive

**Authentication:** Required (Bearer Token)

**Request Example:**
```bash
curl -X POST "https://api.example.com/api/v1/files/123e4567-e89b-12d3-a456-************/unarchive" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Response Example:**
```json
{
  "data": true,
  "message": "File unarchived successfully",
  "statusCode": 200,
  "success": true,
  "errorCode": null
}
```

---

### List Files with Archive Filtering

Get user files with optional archive status filtering.

**Endpoint:** `GET /api/v1/files`

**Query Parameters:**
- `parentFolderId` (optional): Filter by parent folder
- `page` (optional, default: 1): Page number
- `pageSize` (optional, default: 20): Items per page
- `search` (optional): Search term
- `sortBy` (optional, default: "CreatedAt"): Sort field
- `sortDirection` (optional, default: "DESC"): Sort direction
- `isArchived` (optional): Archive status filter
  - `null` or omitted: Return all files
  - `true`: Return only archived files
  - `false`: Return only non-archived files

**Request Examples:**
```bash
# Get all files
curl "https://api.example.com/api/v1/files" \
  -H "Authorization: Bearer {token}"

# Get only archived files
curl "https://api.example.com/api/v1/files?isArchived=true" \
  -H "Authorization: Bearer {token}"

# Get only non-archived files
curl "https://api.example.com/api/v1/files?isArchived=false" \
  -H "Authorization: Bearer {token}"
```

**Response Example:**
```json
{
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-************",
        "name": "document.pdf",
        "displayName": "Important Document",
        "fileSize": 1048576,
        "mimeType": "application/pdf",
        "isArchived": true,
        "archivedAt": "2024-06-28T10:30:00Z",
        "archivedBy": "987fcdeb-51a2-43d1-9f12-************",
        "archivedByName": "<EMAIL>",
        "createdAt": "2024-06-20T08:00:00Z",
        "updatedAt": "2024-06-28T10:30:00Z",
        "ownerId": "987fcdeb-51a2-43d1-9f12-************",
        "ownerName": "<EMAIL>"
      }
    ],
    "totalCount": 1,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  },
  "message": null,
  "statusCode": 200,
  "success": true
}
```

---

## Folder Archive APIs

### Archive Folder

Archive a folder and all its contents (files and subfolders). Only the folder owner can perform this action.

**Endpoint:** `POST /api/v1/folders/{id}/archive`

**Parameters:**
- `id` (path, required): GUID of the folder to archive

**Authentication:** Required (Bearer Token)

**Request Example:**
```bash
curl -X POST "https://api.example.com/api/v1/folders/456e7890-e89b-12d3-a456-************/archive" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Response Example:**
```json
{
  "data": true,
  "message": "Folder archived successfully",
  "statusCode": 200,
  "success": true,
  "errorCode": null
}
```

**Important Notes:**
- Archiving a folder automatically archives all its contents
- All subfolders and files within the folder are cascaded to archived status
- The operation is atomic - either all items are archived or none

---

### Unarchive Folder

Restore an archived folder and all its contents back to normal view.

**Endpoint:** `POST /api/v1/folders/{id}/unarchive`

**Parameters:**
- `id` (path, required): GUID of the folder to unarchive

**Authentication:** Required (Bearer Token)

**Request Example:**
```bash
curl -X POST "https://api.example.com/api/v1/folders/456e7890-e89b-12d3-a456-************/unarchive" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Response Example:**
```json
{
  "data": true,
  "message": "Folder unarchived successfully",
  "statusCode": 200,
  "success": true,
  "errorCode": null
}
```

---

### List Folders with Archive Filtering

Get user folders with optional archive status filtering.

**Endpoint:** `GET /api/v1/folders`

**Query Parameters:**
- `parentFolderId` (optional): Filter by parent folder
- `page` (optional, default: 1): Page number
- `pageSize` (optional, default: 20): Items per page
- `search` (optional): Search term
- `sortBy` (optional, default: "Name"): Sort field
- `sortDirection` (optional, default: "ASC"): Sort direction
- `includeShared` (optional, default: true): Include shared folders
- `isArchived` (optional): Archive status filter
  - `null` or omitted: Return all folders
  - `true`: Return only archived folders
  - `false`: Return only non-archived folders

**Request Examples:**
```bash
# Get all folders
curl "https://api.example.com/api/v1/folders" \
  -H "Authorization: Bearer {token}"

# Get only archived folders
curl "https://api.example.com/api/v1/folders?isArchived=true" \
  -H "Authorization: Bearer {token}"

# Get only non-archived folders
curl "https://api.example.com/api/v1/folders?isArchived=false" \
  -H "Authorization: Bearer {token}"
```

**Response Example:**
```json
{
  "data": {
    "items": [
      {
        "id": "456e7890-e89b-12d3-a456-************",
        "name": "Projects",
        "path": "/Projects",
        "level": 0,
        "isArchived": true,
        "archivedAt": "2024-06-28T10:30:00Z",
        "archivedBy": "987fcdeb-51a2-43d1-9f12-************",
        "archivedByName": "<EMAIL>",
        "createdAt": "2024-06-15T08:00:00Z",
        "updatedAt": "2024-06-28T10:30:00Z",
        "ownerId": "987fcdeb-51a2-43d1-9f12-************",
        "ownerName": "<EMAIL>",
        "fileCount": 5,
        "subfolderCount": 2,
        "parentFolderId": null
      }
    ],
    "totalCount": 1,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  },
  "message": null,
  "statusCode": 200,
  "success": true
}
```

---

## Data Models

### Updated FileDto

```json
{
  "id": "guid",
  "name": "string",
  "displayName": "string",
  "fileSize": "number",
  "mimeType": "string",
  "filePath": "string",
  "hashMd5": "string",
  "hashSha256": "string",
  "storageProvider": "string",
  "externalId": "string",
  "parentFolderId": "guid",
  "ownerId": "guid",
  "ownerName": "string",
  "createdBy": "guid",
  "createdByName": "string",
  "updatedBy": "guid",
  "updatedByName": "string",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "isArchived": "boolean",
  "archivedAt": "datetime",
  "archivedBy": "guid",
  "archivedByName": "string",
  "version": "number",
  "permissions": ["string"],
  "isShared": "boolean"
}
```

### Updated FolderDto

```json
{
  "id": "guid",
  "name": "string",
  "parentFolderId": "guid",
  "ownerId": "guid",
  "ownerName": "string",
  "createdBy": "guid",
  "createdByName": "string",
  "updatedBy": "guid",
  "updatedByName": "string",
  "path": "string",
  "level": "number",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "isArchived": "boolean",
  "archivedAt": "datetime",
  "archivedBy": "guid",
  "archivedByName": "string",
  "fileCount": "number",
  "subfolderCount": "number",
  "permissions": ["string"]
}
```

---

## Business Rules

### Archive Rules

1. **Permission Requirements**
   - Only the owner of a file/folder can archive/unarchive it
   - Admin users may have elevated permissions (future enhancement)

2. **Cascade Behavior**
   - Archiving a folder automatically archives all contained files and subfolders
   - Unarchiving a folder automatically unarchives all contained files and subfolders
   - Individual files within an archived folder cannot be unarchived separately

3. **State Validation**
   - Cannot archive already archived items
   - Cannot unarchive non-archived items
   - Cannot archive/unarchive deleted items
   - Deleted items cannot be archived

4. **Data Integrity**
   - Archive operations are atomic
   - Audit trail is maintained (who archived, when)
   - Archive status is indexed for performance

### Query Behavior

1. **Default Filtering**
   - By default, listing endpoints return both archived and non-archived items
   - Use `isArchived` parameter to filter specifically

2. **Search and Sort**
   - Search works across both archived and non-archived items unless filtered
   - Sorting applies to the filtered result set

3. **Pagination**
   - Pagination is applied after filtering and searching
   - Total counts reflect the filtered results

---

## Error Handling

### Common Error Codes

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 400 | VALIDATION_FAILED | Invalid request parameters |
| 401 | UNAUTHORIZED | Missing or invalid authentication |
| 403 | FORBIDDEN | Insufficient permissions |
| 404 | NOT_FOUND | Resource not found |
| 409 | CONFLICT | Operation conflicts with current state |
| 500 | INTERNAL_ERROR | Server error |

### Archive-Specific Errors

```json
{
  "data": null,
  "message": "Cannot archive a deleted file",
  "statusCode": 409,
  "success": false,
  "errorCode": "INVALID_STATE"
}
```

```json
{
  "data": null,
  "message": "You do not have permission to archive this folder",
  "statusCode": 403,
  "success": false,
  "errorCode": "FORBIDDEN"
}
```

---

## Migration Guide

### Database Changes

The following fields have been added to both `Files` and `Folders` tables:

- `IsArchived` (boolean, default: false)
- `ArchivedAt` (timestamp, nullable)
- `ArchivedBy` (uuid, nullable)

### API Changes

1. **New Endpoints Added**
   - `POST /api/v1/files/{id}/archive`
   - `POST /api/v1/files/{id}/unarchive`
   - `POST /api/v1/folders/{id}/archive`
   - `POST /api/v1/folders/{id}/unarchive`

2. **Enhanced Endpoints**
   - `GET /api/v1/files` - Added `isArchived` query parameter
   - `GET /api/v1/folders` - Added `isArchived` query parameter

3. **Response Model Updates**
   - `FileDto` - Added archive-related fields
   - `FolderDto` - Added archive-related fields

---

## Examples and Use Cases

### Example 1: Archive Project Folder

```bash
# Archive a project folder (includes all files and subfolders)
curl -X POST "https://api.example.com/api/v1/folders/project-123/archive" \
  -H "Authorization: Bearer {token}"

# Verify archive status
curl "https://api.example.com/api/v1/folders/project-123" \
  -H "Authorization: Bearer {token}"
```

### Example 2: Find All Archived Files

```bash
# Get all archived files for cleanup
curl "https://api.example.com/api/v1/files?isArchived=true&pageSize=100" \
  -H "Authorization: Bearer {token}"
```

### Example 3: Restore Archived Content

```bash
# Unarchive folder to restore access
curl -X POST "https://api.example.com/api/v1/folders/project-123/unarchive" \
  -H "Authorization: Bearer {token}"

# Verify all contents are restored
curl "https://api.example.com/api/v1/folders/project-123/contents" \
  -H "Authorization: Bearer {token}"
```

---

## Security Considerations

1. **Authentication Required**: All archive operations require valid authentication
2. **Owner Permissions**: Only resource owners can archive/unarchive their items
3. **Audit Trail**: All archive operations are logged with user and timestamp
4. **Data Protection**: Archived items are not deleted, only hidden from normal views
5. **Cascade Security**: Folder archive operations validate permissions on the entire tree

---

## Performance Notes

1. **Database Indexes**: Archive fields are indexed for optimal query performance
2. **Cascade Operations**: Folder archive operations use bulk updates for efficiency
3. **Query Optimization**: Archive filtering is optimized at the database level
4. **Pagination**: Large result sets are properly paginated to prevent memory issues

---

## Version Information

- **API Version**: 1.0
- **Last Updated**: June 28, 2024
- **Changelog**: Added archive functionality for files and folders 