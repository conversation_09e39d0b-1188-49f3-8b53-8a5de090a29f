# SSO Integration with OIDC-Client-TS

This document describes how to integrate Single Sign-On (SSO) using OpenID Connect (OIDC) with the `oidc-client-ts` library in this React application.

## Table of Contents

1. [Overview](#overview)
2. [Configuration](#configuration)
3. [Environment Variables](#environment-variables)
4. [How to Login](#how-to-login)
5. [How to Logout](#how-to-logout)
6. [How to Get User Information](#how-to-get-user-information)
7. [Protected Routes](#protected-routes)
8. [Silent Token Renewal](#silent-token-renewal)
9. [Error Handling](#error-handling)
10. [Debugging](#debugging)
11. [Role Configuration with Identity Server](#role-configuration-with-identity-server)

## Overview

This application uses:
- **oidc-client-ts** (v3.2.1): Core OIDC client library
- **react-oidc-context** (v3.3.0): React context provider for OIDC

The authentication flow follows the Authorization Code flow with PKCE, which is the recommended approach for single-page applications.

## Configuration

### OIDC Configuration

The OIDC client is configured in `src/utils/authConfig.ts`:

```typescript
import { AuthProviderProps } from 'react-oidc-context';
import { WebStorageStateStore } from 'oidc-client-ts';
import { env } from './env';

export const oidcConfig: AuthProviderProps = {
  authority: env.oidcAuthority,                    // Identity provider URL
  client_id: env.oidcClientId,                     // Client ID from IdP
  redirect_uri: env.oidcRedirectUri,               // Callback URL after login
  post_logout_redirect_uri: env.oidcPostLogoutRedirectUri, // URL after logout
  response_type: 'code',                           // Authorization code flow
  scope: 'openid profile email roles identity_admin_api', // Requested scopes
  automaticSilentRenew: true,                      // Enable automatic token renewal
  loadUserInfo: true,                              // Load user info from UserInfo endpoint
  monitorSession: true,                            // Monitor user session
  stateStore: new WebStorageStateStore({ store: window.localStorage }),
  userStore: new WebStorageStateStore({ store: window.localStorage }),
  metadataUrl: `${env.oidcAuthority}/.well-known/openid-configuration`,
  response_mode: 'fragment',                       // Response mode for SPA
  silent_redirect_uri: `${env.oidcRedirectUri.replace('/callback', '/silent-renew')}`,
  extraQueryParams: {},                            // Additional query parameters
  revokeTokensOnSignout: true,                     // Revoke tokens on logout
};
```

### Key Configuration Options

- **authority**: Your OIDC Identity Provider URL
- **client_id**: The client identifier registered with your IdP
- **redirect_uri**: URL where users are redirected after authentication
- **scope**: Permissions requested from the identity provider
- **automaticSilentRenew**: Automatically renews tokens before expiration
- **loadUserInfo**: Fetches additional user claims from the UserInfo endpoint

## Environment Variables

Create a `.env` file in your project root with the following variables:

```bash
# OIDC Configuration
REACT_APP_OIDC_AUTHORITY=https://your-identity-provider.com
REACT_APP_OIDC_CLIENT_ID=your_client_id
REACT_APP_OIDC_REDIRECT_URI=http://localhost:3000/callback
REACT_APP_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/

# API Configuration
REACT_APP_API_BASE_URL=https://your-api.com/api
```

### Example .env for Different Environments

#### Development
```bash
# Development Environment
REACT_APP_OIDC_AUTHORITY=https://localhost:44310
REACT_APP_OIDC_CLIENT_ID=identity_admin_dev
REACT_APP_OIDC_REDIRECT_URI=http://localhost:3000/callback
REACT_APP_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/
REACT_APP_API_BASE_URL=https://localhost:44302/api
```

#### Production
```bash
# Production Environment
REACT_APP_OIDC_AUTHORITY=https://auth.yourcompany.com
REACT_APP_OIDC_CLIENT_ID=identity_admin_prod
REACT_APP_OIDC_REDIRECT_URI=https://admin.yourcompany.com/callback
REACT_APP_OIDC_POST_LOGOUT_REDIRECT_URI=https://admin.yourcompany.com/
REACT_APP_API_BASE_URL=https://api.yourcompany.com/api
```

## How to Login

### 1. Using the Auth Context

The easiest way to trigger login is through the `useAuth` hook:

```typescript
import { useAuth } from '../../contexts/AuthContext';

const LoginComponent = () => {
  const { login, isLoading } = useAuth();

  const handleLogin = () => {
    login(); // This triggers the OIDC login flow
  };

  return (
    <button onClick={handleLogin} disabled={isLoading}>
      {isLoading ? 'Signing in...' : 'Sign in with SSO'}
    </button>
  );
};
```

### 2. Login Flow

1. User clicks login button
2. Application redirects to Identity Provider
3. User authenticates with IdP
4. IdP redirects back to your app with authorization code
5. `AuthCallback` component handles the callback and exchanges code for tokens
6. User is redirected to the intended page or dashboard

### 3. Callback Handling

The `AuthCallback` component (`src/components/auth/AuthCallback.tsx`) handles the return from the identity provider:

```typescript
// This component automatically:
// 1. Processes the authorization code
// 2. Exchanges it for tokens
// 3. Stores tokens securely
// 4. Redirects user to intended destination
```

## How to Logout

### 1. Using the Auth Context

```typescript
import { useAuth } from '../../contexts/AuthContext';

const LogoutComponent = () => {
  const { logout, isAuthenticated } = useAuth();

  const handleLogout = () => {
    logout(); // This triggers the OIDC logout flow
  };

  if (!isAuthenticated) return null;

  return (
    <button onClick={handleLogout}>
      Sign Out
    </button>
  );
};
```

### 2. Logout Flow

1. User clicks logout button
2. Application revokes tokens with IdP (if `revokeTokensOnSignout: true`)
3. Application redirects to IdP logout endpoint
4. IdP clears its session
5. User is redirected to `post_logout_redirect_uri`

### 3. Force Logout (Error Scenarios)

For error scenarios, you can trigger a custom logout event:

```typescript
// Trigger custom logout event
const forceLogout = () => {
  window.dispatchEvent(new Event('userSignout'));
};
```

## How to Get User Information

### 1. Basic User Info

```typescript
import { useAuth } from '../../contexts/AuthContext';

const UserProfile = () => {
  const { user, isAuthenticated } = useAuth();

  if (!isAuthenticated || !user) {
    return <div>Not authenticated</div>;
  }

  return (
    <div>
      <h2>User Profile</h2>
      <p>Name: {user.profile.name}</p>
      <p>Email: {user.profile.email}</p>
      <p>Username: {user.profile.preferred_username}</p>
      <p>Subject: {user.profile.sub}</p>
    </div>
  );
};
```

### 2. Available User Properties

The user object contains:
- `user.profile.sub` - Subject identifier
- `user.profile.name` - Full name
- `user.profile.given_name` - First name
- `user.profile.family_name` - Last name
- `user.profile.email` - Email address
- `user.profile.preferred_username` - Username
- `user.profile.roles` - User roles (if included in scope)
- `user.access_token` - Access token for API calls
- `user.refresh_token` - Refresh token
- `user.expires_at` - Token expiration timestamp

### 3. Using User Info in API Calls

```typescript
import { useAuth } from '../../contexts/AuthContext';

const ApiCallComponent = () => {
  const { user } = useAuth();

  const callAPI = async () => {
    if (!user?.access_token) return;

    const response = await fetch('/api/protected-endpoint', {
      headers: {
        'Authorization': `Bearer ${user.access_token}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    return data;
  };

  return (
    <button onClick={callAPI}>
      Call Protected API
    </button>
  );
};
```

## Protected Routes

### 1. Using ProtectedRoute Component

```typescript
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import { Dashboard } from '../pages/dashboard/Dashboard';

// Wrap protected components
<ProtectedRoute>
  <Dashboard />
</ProtectedRoute>
```

### 2. Role-Based Access

```typescript
import { RoleGuard } from '../components/auth/RoleGuard';

<RoleGuard requiredRoles={['admin', 'manager']}>
  <AdminPanel />
</RoleGuard>
```

### 3. Programmatic Authentication Check

```typescript
import { useAuth } from '../../contexts/AuthContext';

const MyComponent = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Please log in</div>;

  return <div>Protected content</div>;
};
```

## Silent Token Renewal

The application automatically renews tokens before expiration:

### Configuration
```typescript
// In authConfig.ts
automaticSilentRenew: true,
silent_redirect_uri: `${env.oidcRedirectUri.replace('/callback', '/silent-renew')}`,
```

### Silent Renew Route
Add this route to your router:
```typescript
<Route path="/silent-renew" element={<SilentRenew />} />
```

### How it Works
1. Before token expiration, the library creates a hidden iframe
2. Iframe navigates to the silent renewal endpoint
3. IdP issues new tokens without user interaction
4. New tokens are stored automatically

## Error Handling

### 1. Authentication Errors

The `AuthCallback` component handles various error scenarios:

```typescript
// Common error scenarios:
// - Invalid state parameter
// - Expired authorization code
// - Network connectivity issues
// - IdP configuration problems
```

### 2. Token Expiration

```typescript
import { useAuth } from 'react-oidc-context';

const { error } = useAuth();

useEffect(() => {
  if (error) {
    console.error('OIDC Error:', error.message);
    // Handle specific error types
    if (error.message.includes('token')) {
      // Token-related error
    }
  }
}, [error]);
```

### 3. Clear Auth State

Use the utility function to clear corrupted auth state:

```typescript
import { clearAuthState } from '../utils/authUtils';

// Clear all authentication data
clearAuthState();
```

## Debugging

### 1. Enable Debug Logging

In development mode, debug information is automatically logged:

```typescript
// In AuthContext.tsx
useEffect(() => {
  if (process.env.NODE_ENV === 'development') {
    logOidcConfig();
    setTimeout(() => {
      debugAuthState();
    }, 1000);
  }
}, []);
```

### 2. Check Browser Storage

Authentication data is stored in:
- **localStorage**: User profile, tokens
- **sessionStorage**: Temporary state data

### 3. Verify IdP Configuration

Ensure your Identity Provider is configured with:
- Correct client ID
- Proper redirect URIs
- Appropriate scopes
- CORS settings for your domain

### 4. Network Tab

Monitor network requests to:
- `.well-known/openid-configuration` - Metadata endpoint
- `/authorize` - Authorization endpoint
- `/token` - Token endpoint
- `/userinfo` - User info endpoint

## Common Issues and Solutions

### Issue: "Invalid state" error
**Solution**: Clear auth state and try again
```typescript
clearAuthState();
```

### Issue: CORS errors
**Solution**: Configure CORS on your Identity Provider to allow your domain

### Issue: Token renewal fails
**Solution**: Check that silent renewal URL is properly configured and accessible

### Issue: User info not loading
**Solution**: Ensure `loadUserInfo: true` and proper scopes are configured

## Required Identity Provider Configuration

Your OIDC Identity Provider must be configured with:

1. **Client Settings**:
   - Client ID: `identity_admin` (or your chosen ID)
   - Client Type: Public (for SPA) or Confidential (with PKCE)
   - Grant Types: Authorization Code
   - Response Types: Code

2. **Redirect URIs**:
   - Login: `http://localhost:3000/callback`
   - Logout: `http://localhost:3000/`
   - Silent Renew: `http://localhost:3000/silent-renew`

3. **CORS Origins**:
   - `http://localhost:3000` (development)
   - Your production domain

4. **Scopes**:
   - `openid` (required)
   - `profile`
   - `email`
   - `roles`
   - Custom API scopes as needed

## Security Considerations

1. **Always use HTTPS in production**
2. **Store sensitive data securely** (tokens are stored in localStorage)
3. **Implement proper CORS policies**
4. **Use appropriate token lifetimes**
5. **Monitor for token leakage**
6. **Implement proper error handling**
7. **Use PKCE for additional security**

## Support

For issues related to:
- **OIDC Configuration**: Check your Identity Provider documentation
- **Token Issues**: Verify scopes and client configuration
- **Network Issues**: Check CORS and firewall settings
- **Application Issues**: Check browser console and network tab 

## Role Configuration with Identity Server

### 1. Identity Server Claims Configuration

Để roles xuất hiện trong JWT token, Identity Server cần được cấu hình để include role claims:

#### A. Cấu hình Client trong Identity Server

```csharp
// In your Identity Server configuration (e.g., Config.cs or Startup.cs)
public static IEnumerable<Client> Clients =>
    new List<Client>
    {
        new Client
        {
            ClientId = "veasy_web_client",
            ClientName = "Veasy Web Application",
            
            AllowedGrantTypes = GrantTypes.Code,
            RequirePkce = true,
            RequireClientSecret = false, // For public clients (SPAs)
            
            // Redirect URIs
            RedirectUris = { "http://localhost:3000/auth/callback" },
            PostLogoutRedirectUris = { "http://localhost:3000/" },
            
            // Scopes
            AllowedScopes = {
                IdentityServerConstants.StandardScopes.OpenId,
                IdentityServerConstants.StandardScopes.Profile,
                IdentityServerConstants.StandardScopes.Email,
                "roles", // ⭐ Role scope
                "file_service"
            },
            
            // ⭐ Claims configuration để include roles trong tokens
            AlwaysIncludeUserClaimsInIdToken = true,
            AllowedCorsOrigins = { "http://localhost:3000" },
            
            // Token lifetimes
            AccessTokenLifetime = 3600,
            IdentityTokenLifetime = 300,
            
            // ⭐ Enable để roles xuất hiện trong access token
            AlwaysSendClientClaims = true,
            IncludeJwtId = true
        }
    };
```

#### B. Cấu hình Identity Resources

```csharp
public static IEnumerable<IdentityResource> IdentityResources =>
    new List<IdentityResource>
    {
        new IdentityResources.OpenId(),
        new IdentityResources.Profile(),
        new IdentityResources.Email(),
        
        // ⭐ Custom role identity resource
        new IdentityResource
        {
            Name = "roles",
            DisplayName = "User Roles",
            Description = "User roles and permissions",
            UserClaims = { "role", "roles" }, // Support both singular and plural
            Required = false,
            Emphasize = true
        }
    };
```

#### C. Cấu hình API Resources (nếu cần roles trong access token)

```csharp
public static IEnumerable<ApiResource> ApiResources =>
    new List<ApiResource>
    {
        new ApiResource("file_service", "File Management API")
        {
            Scopes = { "file_service" },
            
            // ⭐ Include role claims trong access token
            UserClaims = { "role", "roles", "email", "name" },
            
            // Map roles to access token
            ApiSecrets = { /* your secrets */ }
        }
    };
```

### 2. Profile Service Configuration

Tạo custom Profile Service để đảm bảo roles được include:

```csharp
public class CustomProfileService : IProfileService
{
    private readonly IUserClaimsPrincipalFactory<ApplicationUser> _claimsFactory;
    private readonly UserManager<ApplicationUser> _userManager;

    public CustomProfileService(
        UserManager<ApplicationUser> userManager,
        IUserClaimsPrincipalFactory<ApplicationUser> claimsFactory)
    {
        _userManager = userManager;
        _claimsFactory = claimsFactory;
    }

    public async Task GetProfileDataAsync(ProfileDataRequestContext context)
    {
        var user = await _userManager.GetUserAsync(context.Subject);
        if (user != null)
        {
            var principal = await _claimsFactory.CreateAsync(user);
            var claims = principal.Claims.ToList();

            // ⭐ Add role claims explicitly
            var roles = await _userManager.GetRolesAsync(user);
            foreach (var role in roles)
            {
                claims.Add(new Claim("role", role));
                claims.Add(new Claim("roles", role)); // Support both formats
            }

            // Filter claims based on requested scopes
            context.IssuedClaims = claims
                .Where(x => context.RequestedClaimTypes.Contains(x.Type))
                .ToList();
        }
    }

    public async Task IsActiveAsync(IsActiveContext context)
    {
        var user = await _userManager.GetUserAsync(context.Subject);
        context.IsActive = user != null;
    }
}
```

Register Profile Service:

```csharp
// In Startup.cs ConfigureServices
services.AddTransient<IProfileService, CustomProfileService>();
```

### 3. Frontend Role Configuration

#### A. Cập nhật OIDC Configuration

```typescript
// src/lib/oidcConfig.ts - Đã có sẵn
oidcScope: getEnvVar('NEXT_PUBLIC_OIDC_SCOPE', 'openid profile email roles file_service'),
```

#### B. Role Claims Mapping

File `src/lib/authUtils.ts` đã được cập nhật để support nhiều định dạng role claims:

```typescript
export const getUserRoles = (user: User | null): string[] => {
  if (!user?.profile) return [];
  
  // ⭐ Try different possible role claim names
  const roleClaims = [
    user.profile.roles,      // Array format
    user.profile.role,       // Single role
    user.profile['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'], // Microsoft format
    user.profile['roles'],   // Alternative array format
  ];

  for (const claim of roleClaims) {
    if (claim) {
      if (Array.isArray(claim)) return claim;
      if (typeof claim === 'string') return [claim];
    }
  }

  return [];
};
```

### 4. Testing Role Implementation

#### A. Test trong Development Console

```javascript
// Trong browser console sau khi login
const user = JSON.parse(localStorage.getItem('oidc.user'));
console.log('User Roles:', user.profile.role || user.profile.roles);
console.log('Access Token Claims:', JSON.parse(atob(user.access_token.split('.')[1])));
```

#### B. Component Usage Examples

```typescript
// src/components/RoleGuard.tsx
import { useAuth } from '@/contexts/AuthContext';

export const RoleGuard: React.FC<{ 
  requiredRole: string; 
  children: React.ReactNode 
}> = ({ requiredRole, children }) => {
  const { hasRole } = useAuth();
  
  if (!hasRole(requiredRole)) {
    return <div>Access Denied: Requires {requiredRole} role</div>;
  }
  
  return <>{children}</>;
};

// Usage in components
export const AdminPanel = () => {
  const { getUserRoles, hasRole, hasAnyRole } = useAuth();
  
  const userRoles = getUserRoles();
  const isAdmin = hasRole('Admin');
  const hasManagementAccess = hasAnyRole(['Admin', 'Manager']);
  
  return (
    <div>
      <p>Your roles: {userRoles.join(', ')}</p>
      {isAdmin && <AdminControls />}
      {hasManagementAccess && <ManagementPanel />}
    </div>
  );
};
```

### 5. API Authorization với Roles

```typescript
// src/api/core/apiClient.ts - Đã support role-based calls
export class ApiClient {
  async makeAuthorizedRequest(endpoint: string, options: RequestInit = {}) {
    const token = this.getAccessToken();
    
    // Token đã chứa role claims, API có thể validate
    return fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
  }
}
```

### 6. Troubleshooting Roles

#### A. Roles không xuất hiện trong token

1. **Kiểm tra Identity Server logs** để xem ProfileService có được gọi không
2. **Verify scope "roles"** có được request trong OIDC config
3. **Check User có roles** được assign trong database
4. **Ensure AlwaysIncludeUserClaimsInIdToken = true**

#### B. Debug Role Claims

```typescript
// Thêm vào AuthContext để debug
useEffect(() => {
  if (auth.user) {
    console.log('🔐 User Profile:', auth.user.profile);
    console.log('🎭 User Roles:', getUserRoles(auth.user));
    
    // Decode access token để xem claims
    if (auth.user.access_token) {
      try {
        const payload = JSON.parse(atob(auth.user.access_token.split('.')[1]));
        console.log('📋 Access Token Claims:', payload);
      } catch (e) {
        console.error('Failed to decode access token:', e);
      }
    }
  }
}, [auth.user]);
```

### 7. Role Hierarchy Implementation

```typescript
// src/lib/authUtils.ts - Enhanced role checking
export const hasSSORoleAccess = (user: any, requiredRole: string): boolean => {
  if (!user?.profile?.role) return false;
  
  // ⭐ Role hierarchy mapping
  const roleHierarchy: Record<string, string[]> = {
    'SuperAdmin': ['SuperAdmin', 'Admin', 'Manager', 'User'],
    'Admin': ['Admin', 'Manager', 'User'],
    'Manager': ['Manager', 'User'], 
    'User': ['User']
  };
  
  const userRole = user.profile.role;
  return roleHierarchy[userRole]?.includes(requiredRole) ?? false;
};
```

---

## Environment Variables for Roles

```bash
# .env.local
NEXT_PUBLIC_OIDC_SCOPE=openid profile email roles file_service
```

## Summary Checklist

- [ ] ✅ Identity Server Client configured với role scope
- [ ] ✅ Identity Resource "roles" được định nghĩa
- [ ] ✅ Custom ProfileService include role claims  
- [ ] ✅ AlwaysIncludeUserClaimsInIdToken = true
- [ ] ✅ Frontend OIDC scope includes "roles"
- [ ] ✅ Role utility functions implemented
- [ ] ✅ RoleGuard components created
- [ ] ✅ Debug logging added for troubleshooting

Với configuration này, roles sẽ được include trong cả ID token và Access token, và có thể được sử dụng cho authorization trong frontend và API. 