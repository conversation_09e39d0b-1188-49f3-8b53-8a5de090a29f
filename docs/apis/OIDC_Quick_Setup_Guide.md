# OIDC Quick Setup Guide

This guide will help you quickly set up OIDC authentication in your development environment.

## Prerequisites

- Identity Provider (IdP) running and accessible
- OIDC Client configured in your IdP
- Node.js and npm/yarn installed
- Identity Server 4+ instance running and accessible
- Client application registered in Identity Server
- Basic understanding of OIDC flows

## Quick Setup Steps

### 1. Environment Configuration

Copy the example environment file:
```bash
cp docs/example.env .env
```

Update the `.env` file with your specific values:
```bash
# Minimum required configuration
REACT_APP_OIDC_AUTHORITY=https://your-idp-url.com
REACT_APP_OIDC_CLIENT_ID=your_client_id
REACT_APP_OIDC_REDIRECT_URI=http://localhost:3000/callback
REACT_APP_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/
REACT_APP_API_BASE_URL=https://your-api-url.com/api
```

### 2. Identity Provider Configuration

Configure your OIDC Identity Provider with these settings:

**Client Configuration:**
- **Client ID**: `your_client_id` (match your .env file)
- **Client Type**: Public (for SPA)
- **Grant Types**: Authorization Code
- **Response Types**: Code
- **PKCE**: Enabled (recommended)

**Redirect URIs:**
```
http://localhost:3000/callback
http://localhost:3000/silent-renew
```

**Post Logout Redirect URIs:**
```
http://localhost:3000/
```

**CORS Origins:**
```
http://localhost:3000
```

**Scopes:**
```
openid
profile
email
roles
identity_admin_api
```

### 3. Identity Server Configuration

Create `.env.local` in project root:

```bash
# Identity Server Configuration
NEXT_PUBLIC_OIDC_AUTHORITY=https://sso.veasy.vn
NEXT_PUBLIC_OIDC_CLIENT_ID=veasy_web_client
NEXT_PUBLIC_OIDC_CLIENT_SECRET=secret

# Redirect URIs
NEXT_PUBLIC_OIDC_REDIRECT_URI=http://localhost:3000/auth/callback
NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/
NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI=http://localhost:3000/auth/silent-callback

# ⭐ Scopes (include 'roles' for role support)
NEXT_PUBLIC_OIDC_SCOPE=openid profile email roles file_service

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://localhost:7040
```

### 4. Identity Server Client Configuration

```csharp
new Client
{
    ClientId = "veasy_web_client",
    AllowedGrantTypes = GrantTypes.Code,
    RequirePkce = true,
    RequireClientSecret = false,
    
    RedirectUris = { "http://localhost:3000/auth/callback" },
    PostLogoutRedirectUris = { "http://localhost:3000/" },
    
    AllowedScopes = {
        IdentityServerConstants.StandardScopes.OpenId,
        IdentityServerConstants.StandardScopes.Profile,
        IdentityServerConstants.StandardScopes.Email,
        "roles", // ⭐ Essential for role support
        "file_service"
    },
    
    // ⭐ Critical for roles in tokens
    AlwaysIncludeUserClaimsInIdToken = true,
    AllowedCorsOrigins = { "http://localhost:3000" }
}
```

### 5. Start the Application

```bash
npm start
```

The application will start at `http://localhost:3000`

### 6. Test Authentication

1. Navigate to `http://localhost:3000`
2. Click "Sign in with SSO"
3. You should be redirected to your Identity Provider
4. Sign in with your credentials
5. You should be redirected back to the application

### 7. Testing & Debugging

#### A. Test Authentication Flow

1. Start application: `npm run dev`
2. Navigate to protected page
3. Should redirect to Identity Server login
4. After login, check browser console for auth logs

#### B. **Role Testing & Debugging**

##### Quick Role Check via Console

After successful login, run in browser console:

```javascript
// Get stored user data
const user = JSON.parse(localStorage.getItem('oidc.user'));

// Check user profile
console.log('User Profile:', user.profile);

// Check detected roles
console.log('Detected Roles:', user.profile.role || user.profile.roles);

// Decode access token to see claims
const accessTokenPayload = JSON.parse(atob(user.access_token.split('.')[1]));
console.log('Access Token Claims:', accessTokenPayload);

// Decode ID token to see claims  
const idTokenPayload = JSON.parse(atob(user.id_token.split('.')[1]));
console.log('ID Token Claims:', idTokenPayload);
```

##### Check Role Functions

```javascript
// Test role checking functions (in React DevTools or console)
const authContext = /* get from React DevTools */;

// Get user roles
const roles = authContext.getUserRoles();
console.log('User Roles:', roles);

// Test role checking
console.log('Is Admin?', authContext.hasRole('Admin'));
console.log('Has any management role?', authContext.hasAnyRole(['Admin', 'Manager']));
```

##### Component Testing

Create a test component to verify roles:

```tsx
// src/components/RoleTest.tsx
import { useAuth } from '@/contexts/AuthContext';
import { RoleGuard, AdminOnly, ManagerOrAdmin } from '@/components/auth/RoleGuard';

export const RoleTest = () => {
  const { getUserRoles, hasRole, hasAnyRole } = useAuth();
  
  const roles = getUserRoles();
  
  return (
    <div className="p-4 space-y-4">
      <div className="bg-blue-50 p-4 rounded">
        <h3 className="font-bold">Your Roles: {roles.join(', ') || 'None'}</h3>
      </div>
      
      {/* Test single role guard */}
      <RoleGuard requiredRole="Admin">
        <div className="bg-green-50 p-4 rounded">
          ✅ Admin content visible
        </div>
      </RoleGuard>
      
      {/* Test multiple roles */}
      <RoleGuard requiredRoles={["Manager", "Admin"]}>
        <div className="bg-yellow-50 p-4 rounded">
          ✅ Manager or Admin content visible
        </div>
      </RoleGuard>
      
      {/* Test convenience components */}
      <AdminOnly>
        <div className="bg-red-50 p-4 rounded">
          ✅ Admin only content
        </div>
      </AdminOnly>
      
      <ManagerOrAdmin>
        <div className="bg-purple-50 p-4 rounded">
          ✅ Manager or Admin content
        </div>
      </ManagerOrAdmin>
    </div>
  );
};
```

#### C. Common Role Issues & Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **No roles in token** | `getUserRoles()` returns `[]` | - Check `AlwaysIncludeUserClaimsInIdToken = true`<br/>- Verify scope includes "roles"<br/>- Check ProfileService configuration |
| **Roles in wrong format** | Roles detected but authorization fails | - Check role claim name in `getUserRoles()`<br/>- Verify Identity Server claim mapping |
| **Role checks fail** | RoleGuard shows access denied | - Check exact role name spelling<br/>- Verify user has roles assigned<br/>- Check case sensitivity |

#### D. Identity Server ProfileService Debug

Add logging to your ProfileService:

```csharp
public async Task GetProfileDataAsync(ProfileDataRequestContext context)
{
    var user = await _userManager.GetUserAsync(context.Subject);
    if (user != null)
    {
        // ⭐ Debug logging
        var roles = await _userManager.GetRolesAsync(user);
        _logger.LogInformation($"ProfileService: User {user.Email} has roles: {string.Join(", ", roles)}");
        
        var principal = await _claimsFactory.CreateAsync(user);
        var claims = principal.Claims.ToList();

        // Add role claims
        foreach (var role in roles)
        {
            claims.Add(new Claim("role", role));
            claims.Add(new Claim("roles", role));
        }

        // ⭐ Debug what claims are being issued
        _logger.LogInformation($"ProfileService: Issuing claims: {string.Join(", ", claims.Select(c => $"{c.Type}={c.Value}"))}");

        context.IssuedClaims = claims
            .Where(x => context.RequestedClaimTypes.Contains(x.Type))
            .ToList();
    }
}
```

#### E. Frontend Debug Logging

The application automatically logs role information in development mode. Look for these console messages:

- `🔐 Auth State Changed:` - Shows authentication status and roles
- `🎭 Role Detection Debug:` - Shows role claim detection process  
- `✅ Found roles as array/string:` - Successful role detection
- `📋 Access Token Claims:` - All claims in access token
- `🆔 ID Token Claims:` - All claims in ID token

### 8. Production Checklist

- [ ] ✅ HTTPS enabled for all URLs
- [ ] ✅ Correct production URLs in environment variables
- [ ] ✅ Identity Server client configured with production URIs
- [ ] ✅ CORS configured for production domain
- [ ] ✅ Role scope included in OIDC configuration
- [ ] ✅ ProfileService registered and tested
- [ ] ✅ Role claims appear in tokens
- [ ] ✅ Authorization working correctly in UI

### 9. Troubleshooting Quick Reference

```bash
# Check current OIDC configuration
console.log(window.localStorage.getItem('oidc.user'));

# Clear auth state if stuck
localStorage.removeItem('oidc.user');
sessionStorage.clear();

# Test API with bearer token
fetch('/api/test', {
  headers: {
    'Authorization': `Bearer ${user.access_token}`
  }
});
```

For detailed configuration and troubleshooting, see [SSO_OIDC_Integration.md](./SSO_OIDC_Integration.md).

## Verification Checklist

- [ ] Environment variables are set correctly
- [ ] Identity Provider client is configured
- [ ] Redirect URIs match exactly
- [ ] CORS is configured in IdP
- [ ] Required scopes are granted
- [ ] Application starts without errors
- [ ] Login redirects to IdP
- [ ] Successful login redirects back to app
- [ ] User information is displayed
- [ ] Logout works correctly

## Next Steps

Once basic authentication is working:

1. Configure role-based access control
2. Set up API authentication with bearer tokens
3. Configure production environment
4. Implement error handling and monitoring

## Development Tools

### Browser Developer Tools
- **Network Tab**: Monitor OIDC requests
- **Application Tab**: Check localStorage for tokens
- **Console**: View debug messages

### Useful URLs to Monitor
- `/.well-known/openid-configuration` - OIDC metadata
- `/authorize` - Authorization endpoint
- `/token` - Token endpoint
- `/userinfo` - User information endpoint

### Debug Mode
The application automatically enables debug logging in development mode. Check the browser console for detailed authentication information.

## Support

For detailed information, see the main documentation: [SSO_OIDC_Integration.md](./SSO_OIDC_Integration.md)

Common resources:
- [OpenID Connect Specification](https://openid.net/connect/)
- [OIDC-Client-TS Documentation](https://github.com/authts/oidc-client-ts)
- [React-OIDC-Context Documentation](https://github.com/authts/react-oidc-context) 