# VeasyFileManager API Documentation

## Overview

The VeasyFileManager API is a comprehensive file management system built with ASP.NET Core that provides file storage, organization, sharing, and synchronization capabilities with Google Drive integration.

**Base URL:** `https://api.veasy.vn` (or your configured domain)  
**API Version:** v1  
**Authentication:** JWT Bearer Token  

## Authentication

All API endpoints require JWT Bearer authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

**JWT Configuration:**
- **Authority:** `https://sso.veasy.vn`
- **Audience:** `file_service`
- **Token Claims:** `sub` (user ID), `email`, `name`

## Common Use Cases

### File Management Workflow
1. **Upload Files**: Use `/api/v1/files/upload` for single files or `/api/v1/files/upload/multiple` for batch
2. **Organize in Folders**: Create folders with `/api/v1/folders` and move files using `/api/v1/files/{id}/move`
3. **Share Content**: Create shares with `/api/v1/files/{id}/share` or `/api/v1/folders/{id}/share`
4. **Manage Permissions**: Grant access with `/api/v1/files/{id}/permissions` or `/api/v1/folders/{id}/permissions`
5. **Archive/Delete**: Archive with `/api/v1/files/{id}/archive` or delete to recycle bin
6. **Sync with Google Drive**: Use `/api/v1/sync/google-drive` for synchronization

### Large File Handling
1. **Initialize**: Use `/api/v1/files/upload/chunked/init` for files >100MB
2. **Upload Chunks**: Send chunks via `/api/v1/files/upload/chunked/{sessionId}/chunk`
3. **Complete**: Finalize with `/api/v1/files/upload/chunked/{sessionId}/complete`

### Content Access
- **Download**: Use `/api/v1/files/{id}/download` for direct download
- **Stream**: Use `/api/v1/files/{id}/stream` for browser viewing with range support
- **Shared Access**: Access via `/api/v1/shares/{token}` without authentication

### File Deduplication
The system automatically detects duplicate files using MD5 and SHA256 hashes. When uploading a file that already exists (same hash), the system will return a 409 Conflict response to prevent storage waste.

## Rate Limiting

The API implements rate limiting middleware. Specific limits depend on your configuration.

## Common Response Formats

### Success Response
```json
{
  "data": <response_data>,
  "success": true,
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"]
}
```

### Paged Response
```json
{
  "items": [<array_of_items>],
  "totalCount": 100,
  "page": 1,
  "pageSize": 20,
  "totalPages": 5,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

**Pagination Limits:**
- Default page size: 20 items
- Maximum page size: 100 items  
- Page numbers start from 1

## Data Models

### FileDto
```json
{
  "id": "guid",
  "name": "string",
  "displayName": "string", 
  "fileSize": "number",
  "mimeType": "string",
  "filePath": "string (optional)",
  "hashMd5": "string (optional)",
  "hashSha256": "string (optional)",
  "storageProvider": "string",
  "externalId": "string (optional)",
  "parentFolderId": "guid (optional)",
  "ownerId": "guid",
  "ownerName": "string (optional)",
  "createdBy": "guid",
  "createdByName": "string (optional)",
  "updatedBy": "guid (optional)",
  "updatedByName": "string (optional)",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "version": "number",
  "permissions": ["string"],
  "isShared": "boolean",
  "isArchived": "boolean",
  "archivedAt": "datetime (optional)",
  "archivedBy": "guid (optional)",
  "archivedByName": "string (optional)"
}
```

### FolderDto
```json
{
  "id": "guid",
  "name": "string",
  "parentFolderId": "guid (optional)",
  "ownerId": "guid",
  "ownerName": "string (optional)",
  "createdBy": "guid",
  "createdByName": "string (optional)",
  "updatedBy": "guid (optional)",
  "updatedByName": "string (optional)",
  "path": "string",
  "level": "number",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "fileCount": "number",
  "subfolderCount": "number",
  "permissions": ["string"],
  "isArchived": "boolean",
  "archivedAt": "datetime (optional)",
  "archivedBy": "guid (optional)",
  "archivedByName": "string (optional)"
}
```

### Enums

#### PermissionType
- `Read` (0) - View and download files/folders and their contents
- `Write` (1) - Create, edit, upload files and create subfolders
- `Delete` (2) - Delete files and folders
- `Share` (3) - Create share links and manage permissions for files/folders

**Note**: Admin permission type exists in the codebase but is not returned by the `/api/v1/permissions/types` endpoint, suggesting it's for internal use only.

#### ShareType
- `Public` (0) - Publicly accessible
- `Password` (1) - Password protected
- `UserSpecific` (2) - Specific user access

#### SyncStatus
- `Pending` (0) - Waiting to sync
- `Syncing` (1) - Currently syncing
- `Synced` (2) - Successfully synced
- `Failed` (3) - Sync failed

#### StorageProvider
- `R2` (0) - Cloudflare R2
- `GoogleDrive` (1) - Google Drive
- `Local` (2) - Local storage

#### DeletedItemType
- `Folder` (1) - Deleted folder
- `File` (2) - Deleted file

---

# API Endpoints

## Files API (`/api/v1/files`)

### Upload Single File
**POST** `/api/v1/files/upload`

Upload a single file with metadata and optional folder placement.

**Content-Type:** `multipart/form-data`

**Request Body:**
```json
{
  "file": "file (required)",
  "parentFolderId": "guid (optional)",
  "displayName": "string (optional, max: 255 chars)",
  "description": "string (optional, max: 1000 chars)", 
  "syncToGoogleDrive": "boolean (default: true)",
  "tags": ["string"] (optional, max 50 chars each, alphanumeric + spaces only),
  "overwriteExisting": "boolean (default: false)",
  "customMetadata": "string (JSON, optional)"
}
```

**File Validation Rules:**
- **File Size**: Maximum 100MB (104,857,600 bytes), minimum 1 byte
- **Allowed Extensions**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.webp`, `.pdf`, `.doc`, `.docx`, `.xls`, `.xlsx`, `.ppt`, `.pptx`, `.txt`, `.rtf`, `.csv`, `.zip`, `.rar`, `.7z`, `.mp4`, `.avi`, `.mov`, `.wmv`, `.mp3`, `.wav`, `.flac`
- **Allowed MIME Types**: `image/jpeg`, `image/png`, `image/gif`, `application/pdf`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`, `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`, `text/plain`, `application/zip`, `application/x-rar-compressed`, and others matching extensions
- **File Name**: Cannot contain invalid path characters, cannot be empty
- **Display Name**: If provided, same rules as file name, max 255 characters
- **Description**: Max 1000 characters
- **Tags**: Each tag max 50 characters, only alphanumeric characters and spaces allowed

**Responses:**
- **201 Created:** Returns `FileDto`
- **400 Bad Request:** Validation errors (invalid file type, size, name, etc.)
- **401 Unauthorized:** Authentication required
- **403 Forbidden:** Insufficient permissions to upload to target folder
- **404 Not Found:** Parent folder not found
- **409 Conflict:** File with same hash already exists
- **413 Payload Too Large:** File size exceeds limit (100MB)
- **422 Unprocessable Entity:** Validation failed (FluentValidation errors)
- **500 Internal Server Error:** Storage service error

**Example Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "document.pdf",
  "displayName": "Important Document",
  "fileSize": 1048576,
  "mimeType": "application/pdf",
  "storageProvider": "R2",
  "parentFolderId": null,
  "ownerId": "user-guid",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z",
  "version": 1,
  "permissions": ["Read", "Write", "Delete"],
  "isShared": false
}
```

### Upload Multiple Files
**POST** `/api/v1/files/upload/multiple`

Upload multiple files in a single request.

**Content-Type:** `multipart/form-data`

**Request Body:**
```json
{
  "files": "file[] (required)",
  "parentFolderId": "guid (optional)",
  "syncToGoogleDrive": "boolean (default: true)",
  "failOnError": "boolean (default: false)",
  "tags": ["string"] (optional)
}
```

**Responses:**
- **201 Created:** Returns `BatchUploadResult`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required

**BatchUploadResult:**
```json
{
  "totalFiles": 5,
  "successfulUploads": 4,
  "failedUploads": 1,
  "uploadedFiles": [FileDto],
  "errors": [
    {
      "fileName": "corrupted.txt",
      "errorMessage": "File is corrupted",
      "errorCode": "INVALID_FILE"
    }
  ],
  "totalProcessingTime": "00:00:15",
  "totalSizeUploaded": 5242880
}
```

### Initialize Chunked Upload
**POST** `/api/v1/files/upload/chunked/init`

Initialize a chunked upload session for large files.

**Request Body:**
```json
{
  "fileName": "string (required)",
  "totalFileSize": "number (required)",
  "contentType": "string (required)",
  "fileHash": "string (optional, SHA256)",
  "parentFolderId": "guid (optional)",
  "displayName": "string (optional)",
  "description": "string (optional)",
  "syncToGoogleDrive": "boolean (default: true)",
  "tags": ["string"] (optional)
}
```

**Responses:**
- **201 Created:** Returns upload session details
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required

### Upload File Chunk
**POST** `/api/v1/files/upload/chunked/{sessionId}/chunk`

Upload a specific chunk of a file.

**Path Parameters:**
- `sessionId` (guid, required) - Upload session ID

**Content-Type:** `multipart/form-data`

**Request Body:**
```json
{
  "chunk": "file (required)",
  "chunkNumber": "integer (required)" // 1-based chunk number
}
```

**Responses:**
- **200 OK:** Returns `ChunkUploadResult`
- **400 Bad Request:** Invalid chunk or session
- **404 Not Found:** Session not found

**ChunkUploadResult:**
```json
{
  "chunkNumber": 1,
  "chunkSize": 1048576,
  "isLastChunk": false,
  "chunkHash": "sha256-hash",
  "remainingChunks": 9,
  "progressPercentage": 10.0
}
```

### Complete Chunked Upload
**POST** `/api/v1/files/upload/chunked/{sessionId}/complete`

Complete a chunked upload session and create the final file.

**Path Parameters:**
- `sessionId` (guid, required) - Upload session ID

**Request Body:**
```json
{
  "chunkHashes": ["string"] (required),
  "finalFileHash": "string (optional, SHA256)"
}
```

**Responses:**
- **201 Created:** Returns `FileDto`
- **400 Bad Request:** Validation errors or chunk mismatch
- **404 Not Found:** Session not found

### Get User Files
**GET** `/api/v1/files`

Get paginated list of user files with filtering options.

**Query Parameters:**
- `parentFolderId` (guid, optional) - Filter by parent folder
- `page` (integer, default: 1) - Page number
- `pageSize` (integer, default: 20) - Items per page
- `search` (string, optional) - Search term
- `sortBy` (string, default: "Name") - Sort field
- `sortDirection` (string, default: "ASC") - Sort direction
- `uploaderEmail` (string, optional) - Filter by uploader
- `mimeType` (string, optional) - Filter by MIME type
- `createdAfter` (datetime, optional) - Filter by creation date
- `createdBefore` (datetime, optional) - Filter by creation date
- `includeShared` (boolean, default: true) - Include shared files
- `isArchived` (boolean, optional) - Filter by archive status (true=archived only, false=non-archived only, null=all)

**Responses:**
- **200 OK:** Returns `PagedResult<FileDto>`
- **401 Unauthorized:** Authentication required

### Get File Details
**GET** `/api/v1/files/{id}`

Get detailed information about a specific file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Responses:**
- **200 OK:** Returns `FileDto`
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Update File Metadata
**PUT** `/api/v1/files/{id}`

Update file name, description and other metadata.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "displayName": "string (optional)",
  "description": "string (optional)",
  "parentFolderId": "guid (optional)",
  "tags": ["string"] (optional)
}
```

**Responses:**
- **200 OK:** Returns updated `FileDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Download File
**GET** `/api/v1/files/{id}/download`

Download a file or get a presigned download URL.

**Path Parameters:**
- `id` (guid, required) - File ID

**Query Parameters:**
- `presigned` (boolean, default: false) - Return presigned URL instead of file stream
- `expiration` (integer, default: 3600) - Presigned URL expiration in seconds

**Responses:**
- **200 OK:** File stream or presigned URL object
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

**Presigned URL Response:**
```json
{
  "url": "https://storage.example.com/file?signature=...",
  "expires": "2024-01-15T11:30:00Z"
}
```

### Stream File for Viewing
**GET** `/api/v1/files/{id}/stream`

Stream a file for frontend viewing with range request support for large files, images, videos, and documents.

**Path Parameters:**
- `id` (guid, required) - File ID

**Headers:**
- `Range` (optional) - Range header for partial content requests (e.g., "bytes=0-1023")

**Responses:**
- **200 OK:** Full file content
- **206 Partial Content:** Partial file content (when Range header is provided)
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found
- **416 Range Not Satisfiable:** Invalid range request

**Response Headers:**
- `Content-Type`: File MIME type
- `Content-Length`: Content length
- `Content-Range`: Content range (for 206 responses)
- `Accept-Ranges`: "bytes"
- `ETag`: File entity tag for caching
- `Cache-Control`: Caching directives

### Delete File
**DELETE** `/api/v1/files/{id}`

Delete a file (moves to recycle bin by default).

**Path Parameters:**
- `id` (guid, required) - File ID

**Query Parameters:**
- `permanent` (boolean, default: false) - Permanently delete without recycle bin

**Responses:**
- **204 No Content:** File deleted successfully
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Archive File
**POST** `/api/v1/files/{id}/archive`

Archive a file. Archived files are hidden from normal views but can be restored.

**Path Parameters:**
- `id` (guid, required) - File ID

**Responses:**
- **200 OK:** File archived successfully
- **400 Bad Request:** Invalid request
- **401 Unauthorized:** Authentication required
- **403 Forbidden:** Insufficient permissions
- **404 Not Found:** File not found
- **409 Conflict:** Cannot archive file (e.g., already archived)

**Example Response:**
```json
{
  "success": true,
  "message": "File archived successfully",
  "data": {
    "fileId": "123e4567-e89b-12d3-a456-************",
    "archivedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Unarchive File
**POST** `/api/v1/files/{id}/unarchive`

Restore an archived file back to normal view.

**Path Parameters:**
- `id` (guid, required) - File ID

**Responses:**
- **200 OK:** File unarchived successfully
- **400 Bad Request:** Invalid request
- **401 Unauthorized:** Authentication required
- **403 Forbidden:** Insufficient permissions
- **404 Not Found:** File not found
- **409 Conflict:** Cannot unarchive file (e.g., not archived)

**Example Response:**
```json
{
  "success": true,
  "message": "File unarchived successfully",
  "data": {
    "fileId": "123e4567-e89b-12d3-a456-************",
    "unarchivedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Copy File
**POST** `/api/v1/files/{id}/copy`

Create a copy of a file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "targetFolderId": "guid (optional)",
  "newName": "string (optional)",
  "syncToGoogleDrive": "boolean (default: true)"
}
```

**Responses:**
- **201 Created:** Returns new `FileDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Move File
**POST** `/api/v1/files/{id}/move`

Move a file to a different folder.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "targetFolderId": "guid (optional, null for root)"
}
```

**Responses:**
- **200 OK:** Returns updated `FileDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found


### Create File Share
**POST** `/api/v1/files/{id}/share`

Create a shareable link for a file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "shareType": "ShareType (required)", (enum 0, 1, 2) - Public, Password, UserSpecific
  "password": "string (optional, required for Password type)",
  "expiresAt": "datetime (optional)",
  "maxDownloads": "number (optional)"
}
```

**Responses:**
- **201 Created:** Returns share details with token
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Get File Shares
**GET** `/api/v1/files/{id}/shares`

Get all shares for a specific file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Responses:**
- **200 OK:** Returns array of `FileShareDto`
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Grant File Permission
**POST** `/api/v1/files/{id}/permissions`

Grant permission to a user or role for a file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "userId": "guid (optional, either userId or roleId required)",
  "roleId": "guid (optional, either userId or roleId required)",
  "permission": "PermissionType (required)", // Read, Write, Delete, Share, Admin
  "expiresAt": "datetime (optional)"
}
```

**Responses:**
- **201 Created:** Returns permission ID
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Get File Permissions
**GET** `/api/v1/files/{id}/permissions`

Get all permissions for a specific file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Responses:**
- **200 OK:** Returns array of `FilePermissionDto`
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

---

## Folders API (`/api/v1/folders`)

### Create Folder
**POST** `/api/v1/folders`

Create a new folder in the specified parent folder or root.

**Request Body:**
```json
{
  "name": "string (required, max: 255 chars)",
  "parentFolderId": "guid (optional, null for root)",
  "description": "string (optional, max: 1000 chars)"
}
```

**Folder Validation Rules:**
- **Name**: Required, max 255 characters, cannot contain invalid path characters
- **Name Restrictions**: Cannot be reserved names (CON, PRN, AUX, NUL, COM1-9, LPT1-9)
- **Name Format**: Cannot start/end with spaces or dots
- **Description**: Optional, max 1000 characters
- **Parent Access**: User must have Write permission on parent folder

**Responses:**
- **201 Created:** Returns `FolderDto`
- **400 Bad Request:** Validation errors (invalid name, description too long)
- **401 Unauthorized:** Authentication required
- **403 Forbidden:** Insufficient permissions to create folder in parent
- **404 Not Found:** Parent folder not found
- **409 Conflict:** Folder with same name already exists in location
- **422 Unprocessable Entity:** Validation failed (FluentValidation errors)

### Get User Folders
**GET** `/api/v1/folders`

Get paginated list of user folders with filtering options.

**Query Parameters:**
- `parentFolderId` (guid, optional) - Filter by parent folder
- `page` (integer, default: 1) - Page number
- `pageSize` (integer, default: 20) - Items per page
- `search` (string, optional) - Search term
- `sortBy` (string, default: "Name") - Sort field
- `sortDirection` (string, default: "ASC") - Sort direction
- `uploaderEmail` (string, optional) - Filter by creator
- `folderType` (string, optional) - Filter by folder type
- `createdAfter` (datetime, optional) - Filter by creation date
- `createdBefore` (datetime, optional) - Filter by creation date
- `includeShared` (boolean, default: true) - Include shared folders
- `isArchived` (boolean, optional) - Filter by archive status (true=archived only, false=non-archived only, null=all)

**Responses:**
- **200 OK:** Returns `PagedResult<FolderDto>`
- **401 Unauthorized:** Authentication required

### Get Folder Details
**GET** `/api/v1/folders/{id}`

Get detailed information about a specific folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Responses:**
- **200 OK:** Returns `FolderDto`
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Update Folder
**PUT** `/api/v1/folders/{id}`

Update folder name, parent, and description.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Request Body:**
```json
{
  "name": "string (optional)",
  "parentFolderId": "guid (optional)",
  "description": "string (optional)"
}
```

**Responses:**
- **200 OK:** Returns updated `FolderDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Delete Folder
**DELETE** `/api/v1/folders/{id}`

Delete a folder and its contents (moves to recycle bin by default).

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Query Parameters:**
- `permanent` (boolean, default: false) - Permanently delete without recycle bin
- `force` (boolean, default: false) - Force delete even if not empty

**Responses:**
- **204 No Content:** Folder deleted successfully
- **400 Bad Request:** Folder not empty (when force=false)
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Get Folder Contents
**GET** `/api/v1/folders/{id}/contents`

Get files and subfolders within a specific folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `pageSize` (integer, default: 20) - Items per page
- `sortBy` (string, default: "Name") - Sort field
- `sortDirection` (string, default: "ASC") - Sort direction

**Responses:**
- **200 OK:** Returns folder contents with files and subfolders
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

**Response Format:**
```json
{
  "folder": FolderDto,
  "files": PagedResult<FileDto>,
  "subfolders": PagedResult<FolderDto>
}
```

### Move Folder
**POST** `/api/v1/folders/{id}/move`

Move a folder to a different parent folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Request Body:**
```json
{
  "targetParentFolderId": "guid (optional, null for root)"
}
```

**Responses:**
- **200 OK:** Returns updated `FolderDto`
- **400 Bad Request:** Validation errors (e.g., circular reference)
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Download Folder as ZIP
**GET** `/api/v1/folders/{id}/download`

Download folder contents as a ZIP file.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Query Parameters:**
- `includeSubfolders` (boolean, default: true) - Include subfolders in ZIP

**Responses:**
- **200 OK:** ZIP file stream
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Bulk Delete Folders
**DELETE** `/api/v1/folders/bulk`

Delete multiple folders and their contents at once.

**Request Body:**
```json
{
  "folderIds": ["guid"] (required),
  "force": "boolean (default: false)",
  "permanent": "boolean (default: false)"
}
```

**Responses:**
- **200 OK:** Returns bulk operation results
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required

### Archive Folder
**POST** `/api/v1/folders/{id}/archive`

Archive a folder and all its contents. Archived folders are hidden from normal views but can be restored.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Responses:**
- **200 OK:** Folder archived successfully
- **400 Bad Request:** Invalid request
- **401 Unauthorized:** Authentication required
- **403 Forbidden:** Insufficient permissions
- **404 Not Found:** Folder not found
- **409 Conflict:** Cannot archive folder (e.g., already archived or deleted)

**Example Response:**
```json
{
  "success": true,
  "message": "Folder archived successfully",
  "data": {
    "folderId": "123e4567-e89b-12d3-a456-************",
    "archivedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Unarchive Folder
**POST** `/api/v1/folders/{id}/unarchive`

Restore an archived folder and all its contents back to normal view.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Responses:**
- **200 OK:** Folder unarchived successfully
- **400 Bad Request:** Invalid request
- **401 Unauthorized:** Authentication required
- **403 Forbidden:** Insufficient permissions
- **404 Not Found:** Folder not found
- **409 Conflict:** Cannot unarchive folder (e.g., not archived or deleted)

**Example Response:**
```json
{
  "success": true,
  "message": "Folder unarchived successfully",
  "data": {
    "folderId": "123e4567-e89b-12d3-a456-************",
    "unarchivedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Create Folder Share
**POST** `/api/v1/folders/{id}/share`

Create a shareable link for a folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Request Body:**
```json
{
  "shareType": "ShareType (required)",
  "password": "string (optional)",
  "expiresAt": "datetime (optional)",
  "maxDownloads": "number (optional)",
  "includeSubfolders": "boolean (default: true)"
}
```

**Responses:**
- **201 Created:** Returns share details with token
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Grant Folder Permission
**POST** `/api/v1/folders/{id}/permissions`

Grant permission to a user or role for a folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Request Body:**
```json
{
  "userId": "guid (optional)",
  "roleId": "guid (optional)",
  "permission": "PermissionType (required)",
  "expiresAt": "datetime (optional)",
  "inheritToChildren": "boolean (default: true)"
}
```

**Responses:**
- **201 Created:** Returns permission ID
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

---

## Sync API (`/api/v1/sync`)

### Trigger Google Drive Sync
**POST** `/api/v1/sync/google-drive`

Trigger synchronization with Google Drive.

**Request Body:**
```json
{
  "fileId": "guid (optional, sync specific file)",
  "forceSync": "boolean (default: false)"
}
```

**Responses:**
- **202 Accepted:** Sync operation started
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required

### Get Sync Status
**GET** `/api/v1/sync/status`

Get synchronization status for files.

**Query Parameters:**
- `fileId` (guid, optional) - Get status for specific file
- `provider` (string, optional) - Filter by provider (e.g., "GoogleDrive")

**Responses:**
- **200 OK:** Returns array of `SyncStatusDto`
- **401 Unauthorized:** Authentication required

---

## Google Drive API (`/api/v1/google-drive`)

### Get Google Drive Files
**GET** `/api/v1/google-drive/get-files`

List files from user's Google Drive.

**Responses:**
- **200 OK:** Returns Google Drive file list
- **401 Unauthorized:** Authentication required
- **500 Internal Server Error:** Google Drive API error

### Move File to/from Google Drive
**POST** `/api/v1/google-drive/move`

Move files between local storage and Google Drive.

**Request Body:**
```json
{
  "fileId": "guid (required)",
  "direction": "string (required)", // "to-gdrive" or "from-gdrive"
  "targetFolderId": "string (optional)"
}
```

**Responses:**
- **200 OK:** Move operation completed
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

---

## Permissions API (`/api/v1/permissions`)

### Get Available Permission Types
**GET** `/api/v1/permissions/types`

Get all available permission types that can be assigned.

**Query Parameters:**
- `resourceType` (string, optional) - Filter by resource type ("file", "folder")

**Responses:**
- **200 OK:** Returns array of `PermissionTypeDto`
- **500 Internal Server Error:** Server error

**PermissionTypeDto:**
```json
{
  "value": "PermissionType",
  "name": "string",
  "description": "string",
  "applicableToFiles": "boolean",
  "applicableToFolders": "boolean",
  "sortOrder": "number"
}
```

---

## Recycle Bin API (`/api/v1/recycle-bin`)

### Get Deleted Items
**GET** `/api/v1/recycle-bin`

Get paginated list of deleted items in the recycle bin.

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `pageSize` (integer, default: 20) - Items per page
- `itemType` (DeletedItemType, optional) - Filter by item type (Folder=1, File=2)
- `searchTerm` (string, optional) - Search term for names or paths
- `uploaderEmail` (string, optional) - Filter by creator email (future feature - not implemented yet)
- `deletedAfter` (datetime, optional) - Filter items deleted after this date
- `deletedBefore` (datetime, optional) - Filter items deleted before this date
- `onlyRestorable` (boolean, default: true) - Show only items that can be restored

**Responses:**
- **200 OK:** Returns `PagedResult<DeletedItemDto>`
- **401 Unauthorized:** Authentication required

### Restore Deleted Item
**POST** `/api/v1/recycle-bin/{deletedItemId}/restore`

Restore a deleted item from the recycle bin.

**Path Parameters:**
- `deletedItemId` (guid, required) - Deleted item ID

**Request Body:**
```json
{
  "newParentFolderId": "guid (optional)" // Restore to different location
}
```

**Responses:**
- **200 OK:** Item restored successfully
- **400 Bad Request:** Item cannot be restored (expired or conflicts)
- **401 Unauthorized:** Authentication required
- **403 Forbidden:** Insufficient permissions
- **404 Not Found:** Deleted item not found

### Permanently Delete Item
**DELETE** `/api/v1/recycle-bin/{deletedItemId}/permanent`

Permanently delete an item from the recycle bin.

**Path Parameters:**
- `deletedItemId` (guid, required) - Deleted item ID

**Responses:**
- **200 OK:** Item permanently deleted successfully
- **401 Unauthorized:** Authentication required
- **403 Forbidden:** Insufficient permissions
- **404 Not Found:** Deleted item not found

### Get Recycle Bin Statistics
**GET** `/api/v1/recycle-bin/statistics`

Get statistics about deleted items including counts and sizes.

**Responses:**
- **200 OK:** Returns recycle bin statistics
- **401 Unauthorized:** Authentication required

**Statistics Response:**
```json
{
  "totalItems": 25,
  "totalFiles": 20,
  "totalFolders": 5,
  "totalSize": 104857600,
  "itemsExpiringSoon": 3,
  "oldestItemDate": "2024-01-01T10:00:00Z"
}
```

### Empty Recycle Bin
**DELETE** `/api/v1/recycle-bin/empty`

Permanently delete all expired items (older than 30 days) from the recycle bin.

**Note**: Items in recycle bin are automatically eligible for permanent deletion after 30 days. This endpoint only deletes already-expired items and does not affect items within the 30-day recovery period.

**Responses:**
- **200 OK:** Returns cleanup results
- **401 Unauthorized:** Authentication required

**Empty Response:**
```json
{
  "deletedItems": 15,
  "freedSpace": 52428800,
  "message": "Recycle bin cleaned successfully"
}
```

---

## Error Handling

### Common HTTP Status Codes

- **200 OK:** Request successful
- **201 Created:** Resource created successfully
- **202 Accepted:** Request accepted for processing
- **204 No Content:** Request successful, no content to return
- **400 Bad Request:** Invalid request data or validation errors
- **401 Unauthorized:** Authentication required or invalid token
- **403 Forbidden:** Access denied (insufficient permissions)
- **404 Not Found:** Resource not found
- **409 Conflict:** Resource conflict (e.g., duplicate name)
- **413 Payload Too Large:** File size exceeds limits
- **429 Too Many Requests:** Rate limit exceeded
- **500 Internal Server Error:** Server error

### Error Response Format

```json
{
  "type": "https://tools.ietf.org/html/rfc7231#section-6.5.1",
  "title": "One or more validation errors occurred.",
  "status": 400,
  "detail": "Detailed error description",
  "instance": "/api/v1/files/upload",
  "errors": {
    "File": ["File is required"],
    "DisplayName": ["Display name cannot exceed 255 characters"]
  }
}
```

### Common Error Codes

The API uses specific error codes in responses for better error handling:

- **FOLDER_NOT_FOUND**: Specified folder does not exist
- **PERMISSION_DENIED**: Insufficient permissions for the operation
- **FOLDER_EXISTS**: Folder with same name already exists in location
- **VALIDATION_FAILED**: Input validation failed
- **UNAUTHORIZED**: Authentication required or invalid
- **GOOGLE_DRIVE_UNAUTHORIZED**: Google Drive access denied
- **NOT_FOUND**: Requested resource not found
- **CANNOT_RESTORE**: Item cannot be restored (expired/conflicts)
- **RESTORE_FAILED**: Restoration operation failed
- **DELETE_FAILED**: Deletion operation failed

---

## Rate Limiting

The API implements rate limiting to prevent abuse. When rate limits are exceeded, the API returns:

**Status Code:** 429 Too Many Requests

**Headers:**
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

---

## CORS Configuration

The API supports Cross-Origin Resource Sharing (CORS) with the following configuration:
- **Allowed Origins:** All origins (`*`)
- **Allowed Methods:** All HTTP methods
- **Allowed Headers:** All headers

---

## Swagger/OpenAPI

Interactive API documentation is available at:
- **Swagger UI:** `/swagger`
- **OpenAPI Spec:** `/swagger/v1/swagger.json`

The Swagger UI provides a complete interactive interface for testing all API endpoints with authentication support.

