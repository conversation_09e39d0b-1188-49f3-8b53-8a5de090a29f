# VeasyFileManager API Documentation

## Overview

The VeasyFileManager API is a comprehensive file management system built with ASP.NET Core that provides file storage, organization, sharing, and synchronization capabilities with Google Drive integration.

**Base URL:** `https://api.veasy.vn` (or your configured domain)  
**API Version:** v1  
**Authentication:** JWT Bearer Token  

## Authentication

All API endpoints require JWT Bearer authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

**JWT Configuration:**
- **Authority:** `https://sso.veasy.vn`
- **Audience:** `file_service`
- **Token Claims:** `sub` (user ID), `email`, `name`

## Rate Limiting

The API implements rate limiting middleware. Specific limits depend on your configuration.

## Common Response Formats

### Success Response
```json
{
  "data": <response_data>,
  "success": true,
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"]
}
```

### Paged Response
```json
{
  "items": [<array_of_items>],
  "totalCount": 100,
  "page": 1,
  "pageSize": 20,
  "totalPages": 5,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

## Data Models

### FileDto
```json
{
  "id": "guid",
  "name": "string",
  "displayName": "string", 
  "fileSize": "number",
  "mimeType": "string",
  "filePath": "string",
  "hashMd5": "string",
  "hashSha256": "string",
  "storageProvider": "string",
  "externalId": "string",
  "parentFolderId": "guid",
  "ownerId": "guid",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "version": "number",
  "permissions": ["string"],
  "isShared": "boolean"
}
```

### FolderDto
```json
{
  "id": "guid",
  "name": "string",
  "parentFolderId": "guid",
  "ownerId": "guid", 
  "path": "string",
  "level": "number",
  "createdAt": "datetime",
  "updatedAt": "datetime",
  "fileCount": "number",
  "subfolderCount": "number",
  "permissions": ["string"]
}
```

### Enums

#### PermissionType
- `Read` (0) - View and download files/folders
- `Write` (1) - Create, edit, upload files
- `Delete` (2) - Delete files and folders  
- `Share` (3) - Share files and folders
- `Admin` (4) - Full administrative access

#### ShareType
- `Public` (0) - Publicly accessible
- `Password` (1) - Password protected
- `UserSpecific` (2) - Specific user access

#### SyncStatus
- `Pending` (0) - Waiting to sync
- `Syncing` (1) - Currently syncing
- `Synced` (2) - Successfully synced
- `Failed` (3) - Sync failed

#### StorageProvider
- `R2` (0) - Cloudflare R2
- `GoogleDrive` (1) - Google Drive
- `Local` (2) - Local storage

#### DeletedItemType
- `Folder` (1) - Deleted folder
- `File` (2) - Deleted file

---

# API Endpoints

## Files API (`/api/v1/files`)

### Upload Single File
**POST** `/api/v1/files/upload`

Upload a single file with metadata and optional folder placement.

**Content-Type:** `multipart/form-data`

**Request Body:**
```json
{
  "file": "file (required)",
  "parentFolderId": "guid (optional)",
  "displayName": "string (optional)",
  "description": "string (optional)", 
  "syncToGoogleDrive": "boolean (default: true)",
  "tags": ["string"] (optional),
  "overwriteExisting": "boolean (default: false)",
  "customMetadata": "string (JSON, optional)"
}
```

**Responses:**
- **201 Created:** Returns `FileDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **413 Payload Too Large:** File size exceeds limit

**Example Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "document.pdf",
  "displayName": "Important Document",
  "fileSize": 1048576,
  "mimeType": "application/pdf",
  "storageProvider": "R2",
  "parentFolderId": null,
  "ownerId": "user-guid",
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z",
  "version": 1,
  "permissions": ["Read", "Write", "Delete"],
  "isShared": false
}
```

### Upload Multiple Files
**POST** `/api/v1/files/upload/multiple`

Upload multiple files in a single request.

**Content-Type:** `multipart/form-data`

**Request Body:**
```json
{
  "files": "file[] (required)",
  "parentFolderId": "guid (optional)",
  "syncToGoogleDrive": "boolean (default: true)",
  "failOnError": "boolean (default: false)",
  "tags": ["string"] (optional)
}
```

**Responses:**
- **201 Created:** Returns `BatchUploadResult`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required

**BatchUploadResult:**
```json
{
  "totalFiles": 5,
  "successfulUploads": 4,
  "failedUploads": 1,
  "uploadedFiles": [FileDto],
  "errors": [
    {
      "fileName": "corrupted.txt",
      "errorMessage": "File is corrupted",
      "errorCode": "INVALID_FILE"
    }
  ],
  "totalProcessingTime": "00:00:15",
  "totalSizeUploaded": 5242880
}
```

### Initialize Chunked Upload
**POST** `/api/v1/files/upload/chunked/initialize`

Initialize a chunked upload session for large files.

**Request Body:**
```json
{
  "fileName": "string (required)",
  "totalFileSize": "number (required)",
  "contentType": "string (required)",
  "fileHash": "string (optional, SHA256)",
  "parentFolderId": "guid (optional)",
  "displayName": "string (optional)",
  "description": "string (optional)",
  "syncToGoogleDrive": "boolean (default: true)",
  "tags": ["string"] (optional)
}
```

**Responses:**
- **201 Created:** Returns upload session details
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required

### Upload File Chunk
**POST** `/api/v1/files/upload/chunked/{sessionId}/chunk/{chunkNumber}`

Upload a specific chunk of a file.

**Path Parameters:**
- `sessionId` (guid, required) - Upload session ID
- `chunkNumber` (integer, required) - Chunk number (1-based)

**Content-Type:** `multipart/form-data`

**Request Body:**
```json
{
  "chunk": "file (required)"
}
```

**Responses:**
- **200 OK:** Returns `ChunkUploadResult`
- **400 Bad Request:** Invalid chunk or session
- **404 Not Found:** Session not found

**ChunkUploadResult:**
```json
{
  "chunkNumber": 1,
  "chunkSize": 1048576,
  "isLastChunk": false,
  "chunkHash": "sha256-hash",
  "remainingChunks": 9,
  "progressPercentage": 10.0
}
```

### Complete Chunked Upload
**POST** `/api/v1/files/upload/chunked/{sessionId}/complete`

Complete a chunked upload session and create the final file.

**Path Parameters:**
- `sessionId` (guid, required) - Upload session ID

**Request Body:**
```json
{
  "chunkHashes": ["string"] (required),
  "finalFileHash": "string (optional, SHA256)"
}
```

**Responses:**
- **201 Created:** Returns `FileDto`
- **400 Bad Request:** Validation errors or chunk mismatch
- **404 Not Found:** Session not found

### Get User Files
**GET** `/api/v1/files`

Get paginated list of user files with filtering options.

**Query Parameters:**
- `parentFolderId` (guid, optional) - Filter by parent folder
- `page` (integer, default: 1) - Page number
- `pageSize` (integer, default: 20) - Items per page
- `search` (string, optional) - Search term
- `sortBy` (string, default: "Name") - Sort field
- `sortDirection` (string, default: "ASC") - Sort direction
- `uploaderEmail` (string, optional) - Filter by uploader
- `mimeType` (string, optional) - Filter by MIME type
- `createdAfter` (datetime, optional) - Filter by creation date
- `createdBefore` (datetime, optional) - Filter by creation date
- `includeShared` (boolean, default: true) - Include shared files

**Responses:**
- **200 OK:** Returns `PagedResult<FileDto>`
- **401 Unauthorized:** Authentication required

### Get File Details
**GET** `/api/v1/files/{id}`

Get detailed information about a specific file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Responses:**
- **200 OK:** Returns `FileDto`
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Update File Metadata
**PUT** `/api/v1/files/{id}`

Update file name, description and other metadata.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "displayName": "string (optional)",
  "description": "string (optional)",
  "parentFolderId": "guid (optional)",
  "tags": ["string"] (optional)
}
```

**Responses:**
- **200 OK:** Returns updated `FileDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Download File
**GET** `/api/v1/files/{id}/download`

Download a file or get a presigned download URL.

**Path Parameters:**
- `id` (guid, required) - File ID

**Query Parameters:**
- `presigned` (boolean, default: false) - Return presigned URL instead of file stream
- `expiration` (integer, default: 3600) - Presigned URL expiration in seconds

**Responses:**
- **200 OK:** File stream or presigned URL object
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

**Presigned URL Response:**
```json
{
  "url": "https://storage.example.com/file?signature=...",
  "expires": "2024-01-15T11:30:00Z"
}
```

### Delete File
**DELETE** `/api/v1/files/{id}`

Delete a file (moves to recycle bin by default).

**Path Parameters:**
- `id` (guid, required) - File ID

**Query Parameters:**
- `permanent` (boolean, default: false) - Permanently delete without recycle bin

**Responses:**
- **204 No Content:** File deleted successfully
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Copy File
**POST** `/api/v1/files/{id}/copy`

Create a copy of a file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "targetFolderId": "guid (optional)",
  "newName": "string (optional)",
  "syncToGoogleDrive": "boolean (default: true)"
}
```

**Responses:**
- **201 Created:** Returns new `FileDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Move File
**POST** `/api/v1/files/{id}/move`

Move a file to a different folder.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "targetFolderId": "guid (optional, null for root)"
}
```

**Responses:**
- **200 OK:** Returns updated `FileDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found


### Create File Share
**POST** `/api/v1/files/{id}/shares`

Create a shareable link for a file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "shareType": "ShareType (required)", // Public, Password, UserSpecific
  "password": "string (optional, required for Password type)",
  "expiresAt": "datetime (optional)",
  "maxDownloads": "number (optional)"
}
```

**Responses:**
- **201 Created:** Returns share details with token
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Get File Shares
**GET** `/api/v1/files/{id}/shares`

Get all shares for a specific file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Responses:**
- **200 OK:** Returns array of `FileShareDto`
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Grant File Permission
**POST** `/api/v1/files/{id}/permissions`

Grant permission to a user or role for a file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Request Body:**
```json
{
  "userId": "guid (optional, either userId or roleId required)",
  "roleId": "guid (optional, either userId or roleId required)",
  "permission": "PermissionType (required)", // Read, Write, Delete, Share, Admin
  "expiresAt": "datetime (optional)"
}
```

**Responses:**
- **201 Created:** Returns permission ID
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

### Get File Permissions
**GET** `/api/v1/files/{id}/permissions`

Get all permissions for a specific file.

**Path Parameters:**
- `id` (guid, required) - File ID

**Responses:**
- **200 OK:** Returns array of `FilePermissionDto`
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

---

## Folders API (`/api/v1/folders`)

### Create Folder
**POST** `/api/v1/folders`

Create a new folder in the specified parent folder or root.

**Request Body:**
```json
{
  "name": "string (required)",
  "parentFolderId": "guid (optional, null for root)",
  "description": "string (optional)"
}
```

**Responses:**
- **201 Created:** Returns `FolderDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required

### Get User Folders
**GET** `/api/v1/folders`

Get paginated list of user folders with filtering options.

**Query Parameters:**
- `parentFolderId` (guid, optional) - Filter by parent folder
- `page` (integer, default: 1) - Page number
- `pageSize` (integer, default: 20) - Items per page
- `search` (string, optional) - Search term
- `sortBy` (string, default: "Name") - Sort field
- `sortDirection` (string, default: "ASC") - Sort direction
- `uploaderEmail` (string, optional) - Filter by creator
- `folderType` (string, optional) - Filter by folder type
- `createdAfter` (datetime, optional) - Filter by creation date
- `createdBefore` (datetime, optional) - Filter by creation date
- `includeShared` (boolean, default: true) - Include shared folders

**Responses:**
- **200 OK:** Returns `PagedResult<FolderDto>`
- **401 Unauthorized:** Authentication required

### Get Folder Details
**GET** `/api/v1/folders/{id}`

Get detailed information about a specific folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Responses:**
- **200 OK:** Returns `FolderDto`
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Update Folder
**PUT** `/api/v1/folders/{id}`

Update folder name, parent, and description.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Request Body:**
```json
{
  "name": "string (optional)",
  "parentFolderId": "guid (optional)",
  "description": "string (optional)"
}
```

**Responses:**
- **200 OK:** Returns updated `FolderDto`
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Delete Folder
**DELETE** `/api/v1/folders/{id}`

Delete a folder and its contents (moves to recycle bin by default).

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Query Parameters:**
- `permanent` (boolean, default: false) - Permanently delete without recycle bin
- `force` (boolean, default: false) - Force delete even if not empty

**Responses:**
- **204 No Content:** Folder deleted successfully
- **400 Bad Request:** Folder not empty (when force=false)
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Get Folder Contents
**GET** `/api/v1/folders/{id}/contents`

Get files and subfolders within a specific folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `pageSize` (integer, default: 20) - Items per page
- `sortBy` (string, default: "Name") - Sort field
- `sortDirection` (string, default: "ASC") - Sort direction

**Responses:**
- **200 OK:** Returns folder contents with files and subfolders
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

**Response Format:**
```json
{
  "folder": FolderDto,
  "files": PagedResult<FileDto>,
  "subfolders": PagedResult<FolderDto>
}
```

### Move Folder
**POST** `/api/v1/folders/{id}/move`

Move a folder to a different parent folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Request Body:**
```json
{
  "targetParentFolderId": "guid (optional, null for root)"
}
```

**Responses:**
- **200 OK:** Returns updated `FolderDto`
- **400 Bad Request:** Validation errors (e.g., circular reference)
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Download Folder as ZIP
**GET** `/api/v1/folders/{id}/download`

Download folder contents as a ZIP file.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Query Parameters:**
- `includeSubfolders` (boolean, default: true) - Include subfolders in ZIP

**Responses:**
- **200 OK:** ZIP file stream
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Bulk Delete Folders
**DELETE** `/api/v1/folders/bulk`

Delete multiple folders and their contents at once.

**Request Body:**
```json
{
  "folderIds": ["guid"] (required),
  "force": "boolean (default: false)",
  "permanent": "boolean (default: false)"
}
```

**Responses:**
- **200 OK:** Returns bulk operation results
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required

### Create Folder Share
**POST** `/api/v1/folders/{id}/shares`

Create a shareable link for a folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Request Body:**
```json
{
  "shareType": "ShareType (required)",
  "password": "string (optional)",
  "expiresAt": "datetime (optional)",
  "maxDownloads": "number (optional)",
  "includeSubfolders": "boolean (default: true)"
}
```

**Responses:**
- **201 Created:** Returns share details with token
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

### Grant Folder Permission
**POST** `/api/v1/folders/{id}/permissions`

Grant permission to a user or role for a folder.

**Path Parameters:**
- `id` (guid, required) - Folder ID

**Request Body:**
```json
{
  "userId": "guid (optional)",
  "roleId": "guid (optional)",
  "permission": "PermissionType (required)",
  "expiresAt": "datetime (optional)",
  "inheritToChildren": "boolean (default: true)"
}
```

**Responses:**
- **201 Created:** Returns permission ID
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Folder not found

---

## Sync API (`/api/v1/sync`)

### Trigger Google Drive Sync
**POST** `/api/v1/sync/google-drive`

Trigger synchronization with Google Drive.

**Request Body:**
```json
{
  "fileId": "guid (optional, sync specific file)",
  "forceSync": "boolean (default: false)"
}
```

**Responses:**
- **202 Accepted:** Sync operation started
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required

### Get Sync Status
**GET** `/api/v1/sync/status`

Get synchronization status for files.

**Query Parameters:**
- `fileId` (guid, optional) - Get status for specific file
- `provider` (string, optional) - Filter by provider (e.g., "GoogleDrive")

**Responses:**
- **200 OK:** Returns array of `SyncStatusDto`
- **401 Unauthorized:** Authentication required

---

## Google Drive API (`/api/v1/google-drive`)

### Get Google Drive Files
**GET** `/api/v1/google-drive/get-files`

List files from user's Google Drive.

**Responses:**
- **200 OK:** Returns Google Drive file list
- **401 Unauthorized:** Authentication required
- **500 Internal Server Error:** Google Drive API error

### Move File to/from Google Drive
**POST** `/api/v1/google-drive/move`

Move files between local storage and Google Drive.

**Request Body:**
```json
{
  "fileId": "guid (required)",
  "direction": "string (required)", // "to-gdrive" or "from-gdrive"
  "targetFolderId": "string (optional)"
}
```

**Responses:**
- **200 OK:** Move operation completed
- **400 Bad Request:** Validation errors
- **401 Unauthorized:** Authentication required
- **404 Not Found:** File not found

---

## Permissions API (`/api/v1/permissions`)

### Get Available Permission Types
**GET** `/api/v1/permissions/types`

Get all available permission types that can be assigned.

**Query Parameters:**
- `resourceType` (string, optional) - Filter by resource type ("file", "folder")

**Responses:**
- **200 OK:** Returns array of `PermissionTypeDto`
- **500 Internal Server Error:** Server error

**PermissionTypeDto:**
```json
{
  "value": "PermissionType",
  "name": "string",
  "description": "string",
  "applicableToFiles": "boolean",
  "applicableToFolders": "boolean",
  "sortOrder": "number"
}
```

---

## Recycle Bin API (`/api/v1/recycle-bin`)

### Get Deleted Items
**GET** `/api/v1/recycle-bin`

Get paginated list of deleted items in the recycle bin.

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `pageSize` (integer, default: 20) - Items per page
- `search` (string, optional) - Search term
- `itemType` (DeletedItemType, optional) - Filter by item type
- `sortBy` (string, default: "DeletedAt") - Sort field
- `sortDirection` (string, default: "DESC") - Sort direction

**Responses:**
- **200 OK:** Returns `PagedResult<DeletedItemDto>`
- **401 Unauthorized:** Authentication required

### Restore Deleted Item
**POST** `/api/v1/recycle-bin/{id}/restore`

Restore a deleted item from the recycle bin.

**Path Parameters:**
- `id` (guid, required) - Deleted item ID

**Responses:**
- **200 OK:** Item restored successfully
- **400 Bad Request:** Item cannot be restored (expired or conflicts)
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Deleted item not found

### Permanently Delete Item
**DELETE** `/api/v1/recycle-bin/{id}`

Permanently delete an item from the recycle bin.

**Path Parameters:**
- `id` (guid, required) - Deleted item ID

**Responses:**
- **204 No Content:** Item permanently deleted
- **401 Unauthorized:** Authentication required
- **404 Not Found:** Deleted item not found

### Get Recycle Bin Statistics
**GET** `/api/v1/recycle-bin/statistics`

Get statistics about deleted items including counts and sizes.

**Responses:**
- **200 OK:** Returns recycle bin statistics
- **401 Unauthorized:** Authentication required

**Statistics Response:**
```json
{
  "totalItems": 25,
  "totalFiles": 20,
  "totalFolders": 5,
  "totalSize": 104857600,
  "itemsExpiringSoon": 3,
  "oldestItemDate": "2024-01-01T10:00:00Z"
}
```

### Empty Recycle Bin
**DELETE** `/api/v1/recycle-bin/empty`

Permanently delete all expired items (older than 30 days) from the recycle bin.

**Responses:**
- **200 OK:** Returns cleanup results
- **401 Unauthorized:** Authentication required

**Empty Response:**
```json
{
  "deletedItems": 15,
  "freedSpace": 52428800,
  "message": "Recycle bin cleaned successfully"
}
```

---

## Error Handling

### Common HTTP Status Codes

- **200 OK:** Request successful
- **201 Created:** Resource created successfully
- **202 Accepted:** Request accepted for processing
- **204 No Content:** Request successful, no content to return
- **400 Bad Request:** Invalid request data or validation errors
- **401 Unauthorized:** Authentication required or invalid token
- **403 Forbidden:** Access denied (insufficient permissions)
- **404 Not Found:** Resource not found
- **409 Conflict:** Resource conflict (e.g., duplicate name)
- **413 Payload Too Large:** File size exceeds limits
- **429 Too Many Requests:** Rate limit exceeded
- **500 Internal Server Error:** Server error

### Error Response Format

```json
{
  "type": "https://tools.ietf.org/html/rfc7231#section-6.5.1",
  "title": "One or more validation errors occurred.",
  "status": 400,
  "detail": "Detailed error description",
  "instance": "/api/v1/files/upload",
  "errors": {
    "File": ["File is required"],
    "DisplayName": ["Display name cannot exceed 255 characters"]
  }
}
```

---

## Rate Limiting

The API implements rate limiting to prevent abuse. When rate limits are exceeded, the API returns:

**Status Code:** 429 Too Many Requests

**Headers:**
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

---

## CORS Configuration

The API supports Cross-Origin Resource Sharing (CORS) with the following configuration:
- **Allowed Origins:** All origins (`*`)
- **Allowed Methods:** All HTTP methods
- **Allowed Headers:** All headers

---

## Swagger/OpenAPI

Interactive API documentation is available at:
- **Swagger UI:** `/swagger`
- **OpenAPI Spec:** `/swagger/v1/swagger.json`

The Swagger UI provides a complete interactive interface for testing all API endpoints with authentication support.

