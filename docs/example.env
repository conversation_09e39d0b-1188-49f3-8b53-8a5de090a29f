# OIDC Configuration
# Replace these values with your actual Identity Provider settings

# Identity Provider URL (without trailing slash)
NEXT_PUBLIC_OIDC_AUTHORITY=https://your-identity-provider.com

# Client ID registered with your Identity Provider
NEXT_PUBLIC_OIDC_CLIENT_ID=your_client_id

# Redirect URI for authentication callback
NEXT_PUBLIC_OIDC_REDIRECT_URI=http://localhost:3000/auth/callback

# Redirect URI after logout
NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/

# Silent token renewal callback URI
NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI=http://localhost:3000/auth/silent-callback

# OIDC Scopes (space-separated)
NEXT_PUBLIC_OIDC_SCOPE=openid profile email roles identity_admin_api

# Client Secret (if using confidential client)
NEXT_PUBLIC_OIDC_CLIENT_SECRET=your_client_secret

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://your-api.com/api

# Development Environment Example:
# NEXT_PUBLIC_OIDC_AUTHORITY=https://localhost:44310
# NEXT_PUBLIC_OIDC_CLIENT_ID=identity_admin_dev
# NEXT_PUBLIC_OIDC_REDIRECT_URI=http://localhost:3000/auth/callback
# NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/
# NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI=http://localhost:3000/auth/silent-callback
# NEXT_PUBLIC_OIDC_SCOPE=openid profile email roles identity_admin_api
# NEXT_PUBLIC_API_BASE_URL=https://localhost:44302/api

# Production Environment Example:
# NEXT_PUBLIC_OIDC_AUTHORITY=https://auth.yourcompany.com
# NEXT_PUBLIC_OIDC_CLIENT_ID=identity_admin_prod
# NEXT_PUBLIC_OIDC_REDIRECT_URI=https://admin.yourcompany.com/auth/callback
# NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI=https://admin.yourcompany.com/
# NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI=https://admin.yourcompany.com/auth/silent-callback
# NEXT_PUBLIC_OIDC_SCOPE=openid profile email roles identity_admin_api
# NEXT_PUBLIC_API_BASE_URL=https://api.yourcompany.com/api 