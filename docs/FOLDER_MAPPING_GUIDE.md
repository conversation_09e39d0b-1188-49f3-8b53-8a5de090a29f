# Folder Mapping Guide

This document explains how the folder manager has been updated to handle the API response structure for folder management.

## API Response Structure

Your folder API returns data in the following format:

```json
{
  "data": {
    "items": [
      {
        "id": "bc3ad824-7bda-445c-8051-5b7862c5ac09",
        "name": "<PERSON><PERSON><PERSON>",
        "parentFolderId": null,
        "ownerId": "ca91af00-3762-48b8-a047-5e10ffca4f0d",
        "ownerName": "User-ca91af00",
        "createdBy": "00000000-0000-0000-0000-000000000000",
        "createdByName": "Unknown User",
        "updatedBy": null,
        "updatedByName": null,
        "path": "/Bùi <PERSON>h Thái",
        "level": 0,
        "createdAt": "2025-06-28T06:46:52.614462Z",
        "updatedAt": "2025-06-28T06:46:52.614462Z",
        "fileCount": 0,
        "subfolderCount": 0,
        "permissions": []
      }
    ],
    "totalCount": 3,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1,
    "hasNextPage": false,
    "hasPreviousPage": false
  },
  "message": "Folders retrieved successfully",
  "statusCode": 200,
  "success": true,
  "errorCode": null
}
```

## Changes Made

### 1. Updated Interfaces (`src/api/types/interfaces.ts`)

**Extended FolderDto interface** to include the new fields from your API:
- `ownerName?: string` - Display name for the folder owner
- `createdBy?: string` - ID of the user who created the folder
- `createdByName?: string` - Display name of the creator
- `updatedBy?: string` - ID of the user who last updated the folder
- `updatedByName?: string` - Display name of the last updater

**Added new API response wrapper interface**:
```typescript
export interface FolderListApiResponse {
  data: {
    items: FolderDto[];
    totalCount: number;
    page: number;
    pageSize: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  message: string;
  statusCode: number;
  success: boolean;
  errorCode: string | null;
}
```

### 2. Updated Folder Service (`src/api/services/folderService.ts`)

**Modified `getList()` method** to handle the wrapped API response:
- Maps the API response to the expected internal format
- Includes backward compatibility for different response structures
- Handles missing or null values gracefully

### 3. Updated Manager Folders Page (`src/app/manager-folders/page.tsx`)

**Enhanced folder display**:
- Shows owner name prominently with owner ID as secondary info
- Displays file count and subfolder count under each folder name
- Updated both list and grid views
- Added debug function for testing API response mapping (development only)

## Display Changes

### List View
- **Owner Column**: Now shows the owner's display name (e.g., "User-ca91af00") with the owner ID below in smaller text
- **Folder Info**: Shows file count and subfolder count below the folder name
- **Better Layout**: Improved spacing and typography for better readability

### Grid View
- **Enhanced Cards**: Shows file count, subfolder count, and owner name
- **Tooltips**: Added tooltips for owner names that might be truncated

## Testing

### Development Mode
In development mode, you'll see a "🧪 Test Mapping" button that logs sample API response data to the console. This helps verify that the mapping is working correctly.

### Console Debugging
Open your browser's developer console and click the test button to see:
- The complete API response structure
- Mapped folder data
- Owner information extraction

## Usage

The folder manager will now automatically:
1. Parse your API response format
2. Extract owner information and display it properly
3. Show folder statistics (file count, subfolder count)
4. Handle pagination correctly
5. Provide graceful fallbacks for missing data

## Error Handling

The implementation includes:
- **Backward Compatibility**: Works with both wrapped and direct response formats
- **Null Safety**: Handles missing or null values gracefully
- **Default Values**: Provides sensible defaults for pagination and counts
- **Error Boundaries**: Displays appropriate error messages if API calls fail

## Next Steps

You can further customize the display by:
1. Adding more folder metadata from your API response
2. Implementing additional sorting options
3. Adding filter capabilities for owner names or creation dates
4. Customizing the date format or localization

The foundation is now in place to handle your specific API response structure while maintaining flexibility for future changes. 