# URL Routing Guide for Folder Manager

This document explains the URL routing functionality added to the folder manager page.

## Overview

The folder manager now supports URL-based navigation, allowing users to:
- Bookmark specific folders
- Share direct links to folders
- Use browser back/forward navigation
- Refresh the page and stay in the same folder

## URL Structure

### Root Folder
```
/manager-folders
```
- Shows all root-level folders
- No `folderId` parameter in URL

### Specific Folder
```
/manager-folders?folderId=8ae0d5f4-c0d9-4308-9a27-b2d6c9b7a0f8
```
- Shows contents of the specified folder
- `folderId` parameter contains the folder's unique ID

## Implementation Details

### URL Parameters
- **`folderId`**: The unique identifier of the current folder being viewed
- Other parameters (filters, search terms) are preserved when navigating

### Navigation Functions

#### `navigateToFolder(folderId: string)`
- Updates the URL with the new folder ID
- Loads the folder contents
- Resets pagination to page 1

#### `navigateToRoot()`
- Removes the `folderId` parameter from URL
- Returns to the root folder view
- Preserves other URL parameters (search, filters)

#### `navigateToBreadcrumb(index: number)`
- For root (index 0): Calls `navigateToRoot()`
- For specific folders: Calls `navigateToFolder()` with the breadcrumb folder ID

### Browser Integration

#### Back/Forward Navigation
- Users can use browser back/forward buttons
- URL changes are properly handled by Next.js router
- Page state updates automatically based on URL

#### Page Refresh
- Refreshing the page maintains the current folder view
- Folder ID is read from URL on page load
- Automatic navigation to the correct folder

#### Bookmarking & Sharing
- Each folder has a unique, shareable URL
- Bookmarked folders open directly to the correct location
- URLs can be shared with other users

## User Experience Features

### Visual Indicators

#### Page Header
- **Root View**: Shows "Quản lý Folder"
- **Folder View**: Shows the current folder name as the page title
- **Subtitle**: Shows the folder path for context

#### Back Button
- Appears when viewing a specific folder (not at root)
- Located next to the page title
- Provides quick navigation back to root

#### Development Mode
- Shows current URL parameter value for debugging
- Displays "root" when no folder ID is present
- Helpful for testing and development

### Navigation Flow

1. **Start at Root**: `/manager-folders`
2. **Click Folder**: URL becomes `/manager-folders?folderId=abc123`
3. **Page Updates**: Header shows folder name, back button appears
4. **Browser Back**: Returns to root, URL updates automatically
5. **Direct Link**: Paste URL in new tab, opens directly to folder

## Benefits

### For Users
- **Bookmarking**: Save frequently accessed folders
- **Sharing**: Send direct links to team members
- **Browser Navigation**: Use familiar back/forward buttons
- **Refresh Safety**: Page refresh doesn't lose current location

### For Developers
- **SEO**: Search engines can index specific folder pages
- **Analytics**: Track which folders are accessed most
- **Deep Linking**: Link directly to folders from other parts of the app
- **State Management**: URL serves as single source of truth for current location

## Technical Notes

### Next.js Integration
- Uses `useRouter()` and `useSearchParams()` hooks
- Follows Next.js App Router conventions
- Maintains other URL parameters during navigation

### State Synchronization
- URL changes trigger page state updates
- Page state changes update the URL
- Bidirectional synchronization ensures consistency

### Performance
- Only updates URL when necessary
- Preserves existing URL parameters
- Minimal impact on page performance

## Future Enhancements

Potential improvements to consider:
- **Folder Paths in URL**: Use folder paths instead of IDs for prettier URLs
- **Multiple Parameters**: Support for view mode, sorting in URL
- **URL Validation**: Validate folder IDs and handle invalid folders gracefully
- **History Management**: Custom history management for better UX

## Usage Examples

### Direct Navigation
```javascript
// Navigate to specific folder programmatically
navigateToFolder('folder-id-here');

// Return to root
navigateToRoot();
```

### URL Parsing
```javascript
// Get current folder ID from URL
const folderId = searchParams.get('folderId');

// Check if viewing root
const isRoot = !folderId;
```

This URL routing system provides a modern, user-friendly navigation experience while maintaining clean, shareable URLs for the folder management interface. 