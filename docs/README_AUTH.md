# Authentication Setup Guide

This document describes how to set up and configure OIDC authentication in this PDF OCR Dashboard application.

## Quick Start

1. **Copy environment configuration**:
   ```bash
   cp docs/example.env .env.local
   ```

2. **Update environment variables** with your Identity Provider settings:
   ```bash
   # Required settings
   NEXT_PUBLIC_OIDC_AUTHORITY=https://your-identity-provider.com
   NEXT_PUBLIC_OIDC_CLIENT_ID=your_client_id
   NEXT_PUBLIC_OIDC_REDIRECT_URI=http://localhost:3000/auth/callback
   NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI=http://localhost:3000/
   NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI=http://localhost:3000/auth/silent-callback
   ```

3. **Configure your Identity Provider** with these redirect URIs:
   - Login callback: `http://localhost:3000/auth/callback`
   - Logout callback: `http://localhost:3000/`
   - Silent renewal: `http://localhost:3000/auth/silent-callback`

4. **Start the application**:
   ```bash
   npm run dev
   ```

## Environment Variables

All authentication configuration is done through environment variables:

### Required Variables

- `NEXT_PUBLIC_OIDC_AUTHORITY`: Your OIDC Identity Provider URL
- `NEXT_PUBLIC_OIDC_CLIENT_ID`: Client ID from your Identity Provider

### Optional Variables

- `NEXT_PUBLIC_OIDC_REDIRECT_URI`: Callback URL after login (default: `http://localhost:3000/auth/callback`)
- `NEXT_PUBLIC_OIDC_POST_LOGOUT_REDIRECT_URI`: URL after logout (default: `http://localhost:3000/`)
- `NEXT_PUBLIC_OIDC_SILENT_REDIRECT_URI`: Silent renewal callback (default: `http://localhost:3000/auth/silent-callback`)
- `NEXT_PUBLIC_OIDC_SCOPE`: Requested scopes (default: `openid profile email roles identity_admin_api`)
- `NEXT_PUBLIC_OIDC_CLIENT_SECRET`: Client secret (for confidential clients)
- `NEXT_PUBLIC_API_BASE_URL`: Base URL for API calls

## Identity Provider Configuration

Your OIDC Identity Provider must be configured with:

### Client Settings
- **Client Type**: Public (for SPA) or Confidential (with PKCE)
- **Grant Types**: Authorization Code
- **Response Types**: Code
- **PKCE**: Enabled (recommended)

### Redirect URIs
```
http://localhost:3000/auth/callback
http://localhost:3000/auth/silent-callback
```

### Post Logout Redirect URIs
```
http://localhost:3000/
```

### CORS Origins
```
http://localhost:3000
```

### Required Scopes
```
openid
profile
email
roles
identity_admin_api
```

## Features

### Automatic Silent Token Renewal
The application automatically renews tokens before expiration using a hidden iframe.

### Role-Based Access Control
Protect components and routes based on user roles:

```tsx
import { RoleGuard } from '@/components/auth/RoleGuard';

<RoleGuard requiredRoles={['admin', 'manager']}>
  <AdminPanel />
</RoleGuard>
```

### Protected Routes
Wrap components to require authentication:

```tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

<ProtectedRoute requiredRoles={['user']}>
  <Dashboard />
</ProtectedRoute>
```

### Enhanced Error Handling
The application includes comprehensive error handling for:
- Network connectivity issues
- Token expiration
- State validation errors
- Silent renewal failures

## API Integration

The authentication system automatically provides access tokens for API calls:

```tsx
import { useAuth } from '@/contexts/AuthContext';

const { user } = useAuth();

const callAPI = async () => {
  const response = await fetch('/api/protected-endpoint', {
    headers: {
      'Authorization': `Bearer ${user?.access_token}`,
      'Content-Type': 'application/json',
    },
  });
  return response.json();
};
```

## Debugging

### Development Mode
In development, the application automatically enables:
- Detailed OIDC logging
- Configuration validation
- Debug state information
- Enhanced error messages

### Browser Developer Tools
Monitor authentication in the browser:
- **Network Tab**: Check OIDC requests to your Identity Provider
- **Application Tab**: View stored tokens in localStorage
- **Console**: See detailed debug information

### Common Debug URLs
- `/.well-known/openid-configuration` - OIDC metadata
- `/authorize` - Authorization endpoint
- `/token` - Token endpoint
- `/userinfo` - User information endpoint

## Troubleshooting

### "Invalid redirect_uri" Error
**Solution**: Ensure redirect URIs in environment variables exactly match those configured in your Identity Provider.

### CORS Errors
**Solution**: Add your application domain to your Identity Provider's CORS origins.

### "Invalid client_id" Error
**Solution**: Verify the client ID in your environment file matches exactly what's configured in your Identity Provider.

### Silent Renewal Failures
**Solution**: Check that the silent renewal URL is accessible and properly configured.

### State Validation Errors
**Solution**: Use the clear auth state utility:
```tsx
import { useAuth } from '@/contexts/AuthContext';

const { clearAuthState } = useAuth();
clearAuthState(); // Clears corrupted auth state
```

## Security Considerations

1. **Always use HTTPS in production**
2. **Implement proper CORS policies**
3. **Use appropriate token lifetimes**
4. **Monitor for token leakage**
5. **Use PKCE for additional security**
6. **Implement proper error handling**

## Development vs Production

### Development
```bash
NEXT_PUBLIC_OIDC_AUTHORITY=https://localhost:44310
NEXT_PUBLIC_OIDC_CLIENT_ID=identity_admin_dev
NEXT_PUBLIC_OIDC_REDIRECT_URI=http://localhost:3000/auth/callback
```

### Production
```bash
NEXT_PUBLIC_OIDC_AUTHORITY=https://auth.yourcompany.com
NEXT_PUBLIC_OIDC_CLIENT_ID=identity_admin_prod
NEXT_PUBLIC_OIDC_REDIRECT_URI=https://admin.yourcompany.com/auth/callback
```

## Support

For detailed technical documentation, see:
- [OIDC Quick Setup Guide](./apis/OIDC_Quick_Setup_Guide.md)
- [SSO OIDC Integration](./apis/SSO_OIDC_Integration.md)

For issues:
1. Check browser console for error messages
2. Verify Identity Provider configuration
3. Test with debug mode enabled
4. Check network connectivity and CORS settings 